{"agreement_0_test": {"inquiry": "What is the latest active agreement between Ramseier & Amman and Electrolux Professionell sas?", "sql": "SELECT agreement_code,start_validity_date,stop_validity_date,plant_description FROM DWH_PUBLIC.agreement_view WHERE locked_code = '0' AND CURRENT_DATE BETWEEN start_validity_date AND stop_validity_date AND upper(supplier_description) LIKE upper('Ramseier & Amman') AND company_code = 'FFD'", "bot": ["COMPLI_BOT"]}, "agreement_1_test": {"inquiry": "Compile a list of agreements in use for suppliers having SPA in their name across all companies", "sql": "SELECT DISTINCT company_description, plant_description, agreement_code, start_validity_date, stop_validity_date FROM DWH_PUBLIC.agreement_view WHERE locked_code = '0' AND CURRENT_DATE BETWEEN start_validity_date AND stop_validity_date AND supplier_description like '%SPA%'", "bot": ["COMPLI_BOT"]}, "agreement_2_test": {"inquiry": "What are the suppliers with whom we currently have an agreement with in ITC?", "sql": "SELECT DISTINCT supplier, supplier_description FROM DWH_PUBLIC.agreement_view WHERE company_code = 'ITC' AND locked_code = '0' AND CURRENT_DATE BETWEEN start_validity_date AND stop_validity_date", "bot": ["COMPLI_BOT"]}, "agreement_3_test": {"inquiry": "Fetch a complete list of agreements for retailer 01022539 in Electrolux Professional AB that have been ongoing for the last 3 quarters.", "sql": "SELECT DISTINCT supplier, supplier_description,agreement_code,start_validity_date,stop_validity_date,plant_description FROM DWH_PUBLIC.agreement_view WHERE upper(DWH_PUBLIC.agreement_view.company_description) LIKE upper('%Electrolux Professional AB%') AND locked_code = '0' AND trim(supplier) = '01022539 ' AND start_validity_date >= add_months(trunc(CURRENT_DATE, 'Q'), -(3 * 3))", "bot": ["COMPLI_BOT"]}, "agreement_4_test": {"inquiry": "Which suppliers provide Electrolux AS with RR000GMFJE?", "sql": "SELECT DISTINCT supplier, supplier_description FROM DWH_PUBLIC.agreement_view WHERE upper(company_description) LIKE upper('%Electrolux AS%') AND item_code = 'RR000GMFJE'", "bot": ["COMPLI_BOT"]}, "agreement_5_test": {"inquiry": "Show me the quantity of items present in agreement J329015D with Electrolux Professional Gmbh.", "sql": "SELECT count(item_code) FROM DWH_PUBLIC.agreement_row WHERE upper(company_description) like upper('%Electrolux Professional Gmbh%') AND agreement_code = 'J329015D'", "bot": ["COMPLI_BOT"]}, "agreement_6_test": {"inquiry": "Is there a final number available that represents the active amount of articles agreed between provider 00963595 and a company which has Lim in its name?", "sql": "SELECT count(item_code) FROM DWH_PUBLIC.agreement_view WHERE CURRENT_DATE BETWEEN start_validity_date AND stop_validity_date AND supplier = '00706023' AND Upper(company_description) like upper('%Lim%')", "bot": ["COMPLI_BOT"]}, "agreement_7_test": {"inquiry": "Present me with the details of the agreement, pricing, and discount for component 05038AHW00 with provider 00380506.", "sql": "SELECT DISTINCT company_description, plant_description, item_code, agreement_code, item_price, item_discount_percentage_1, item_discount_percentage_2 FROM DWH_PUBLIC.agreement_view WHERE supplier = '05038AHW00' AND item_code = '00380506'", "bot": ["COMPLI_BOT"]}, "items_6_test": {"inquiry": "Fetch the supplier suggested for procuring item RR000GM1VM", "sql": "SELECT COMPANY_CODE,COMPANY_DESCRIPTION,PLANT_CODE,PLANT_DESCRIPTION,ITEM_CODE,MAIN_ITEM_DESCRIPTION,ITEM_STOCKING_TYPE,item_preferred_supplier, ITEM_CODE_VALID FROM DWH_PUBLIC.item_master WHERE item_stocking_type='B' AND trim(item_code)='RR000GM1VM'", "bot": ["COMPLI_BOT", "CALL_CENTER_BOT"]}, "items_7_test": {"inquiry": "List the codes from Rayong that have supplier 11067 as their preferred", "sql": "SELECT COMPANY_CODE,COMPANY_DESCRIPTION,PLANT_CODE,PLANT_DESCRIPTION,ITEM_CODE,MAIN_ITEM_DESCRIPTION,ITEM_STOCKING_TYPE,item_preferred_supplier, ITEM_CODE_VALID FROM DWH_PUBLIC.item_master WHERE item_stocking_type='B' AND trim(item_preferred_supplier)='588123' AND upper(company_description) like upper('%Ryong%')", "bot": ["COMPLI_BOT", "CALL_CENTER_BOT"]}, "items_9_test": {"inquiry": "Please provide me with a catalog of components for Sursee that are from Malaysia.", "sql": "SELECT COMPANY_CODE,COMPANY_DESCRIPTION,PLANT_CODE,PLANT_DESCRIPTION,ITEM_CODE,MAIN_ITEM_DESCRIPTION,ITEM_STOCKING_TYPE,item_preferred_supplier FROM DWH_PUBLIC.item_master WHERE ITEM_CODE_VALID='Y' AND upper(COMPANY_DESCRIPTION) like upper('%Sursee%') AND trim(country_of_origin)='MY'", "bot": ["COMPLI_BOT", "CALL_CENTER_BOT"]}, "items_10_test": {"inquiry": "What are the valid items for Troyes that have less than 60% EU content?", "sql": "SELECT COMPANY_CODE,COMPANY_DESCRIPTION,PLANT_CODE,PLANT_DESCRIPTION,ITEM_CODE,MAIN_ITEM_DESCRIPTION,ITEM_STOCKING_TYPE,item_preferred_supplier,country_of_origin,percentage_not_ue FROM DWH_PUBLIC.item_master WHERE ITEM_CODE_VALID='Y' AND upper(company_description) like upper('%Troyes%') AND 100-percentage_not_ue < 40", "bot": ["COMPLI_BOT", "CALL_CENTER_BOT"]}, "items_11_test": {"inquiry": "Could you please provide me with a list of items that are valid for company ITC, have an empty country of origin field and a cost of more than 10 EUR?", "sql": "SELECT COMPANY_CODE,COMPANY_DESCRIPTION,PLANT_CODE,PLANT_DESCRIPTION,ITEM_CODE,MAIN_ITEM_DESCRIPTION,ITEM_STOCKING_TYPE,item_preferred_supplier,country_of_origin,percentage_not_ue FROM DWH_PUBLIC.item_master WHERE ITEM_CODE_VALID='Y' AND trim(company_code)='ITC' AND country_of_origin IS NULL AND purchasing_cost>10 AND currency='EUR'", "bot": ["COMPLI_BOT", "CALL_CENTER_BOT"]}, "agreement_8_test": {"inquiry": "Are you in possession of the list of valid items mentioned in agreement J306C574 for company Electrolux Professionell sas? If yes, please provide it to me.", "sql": "SELECT DWH_PUBLIC.item_master.item_code, DWH_PUBLIC.item_master.main_item_description, DWH_PUBLIC.agreement_row.agreement_code, DWH_PUBLIC.agreement_row.plant_code, DWH_PUBLIC.agreement_row.plant_description FROM DWH_PUBLIC.agreement_row, DWH_PUBLIC.item_master WHERE ITEM_CODE_VALID='Y' AND upper(DWH_PUBLIC.agreement_row.company_description) LIKE upper('%Electrolux Professionell sas%') AND DWH_PUBLIC.agreement_row.agreement_code = 'J306C574' AND DWH_PUBLIC.agreement_row.company_code = DWH_PUBLIC.item_master.company_code AND DWH_PUBLIC.agreement_row.plant_code = DWH_PUBLIC.item_master.plant_code AND trim(DWH_PUBLIC.agreement_row.item_code) = trim(DWH_PUBLIC.item_master.item_code)", "bot": ["COMPLI_BOT"]}, "agreement_9_test": {"inquiry": "Show me the codes that are being used but are not included in any valid contract for Electrolux Professional SWEDEN AB.", "sql": "SELECT DWH_PUBLIC.item_master.item_code, DWH_PUBLIC.item_master.main_item_description, DWH_PUBLIC.item_master.plant_code, DWH_PUBLIC.item_master.plant_description FROM DWH_PUBLIC.item_master WHERE upper(company_description) LIKE upper('%Electrolux Professional SWEDEN AB%') AND NOT EXISTS (SELECT item_code FROM DWH_PUBLIC.agreement_view WHERE CURRENT_DATE BETWEEN start_validity_date AND stop_validity_date AND DWH_PUBLIC.agreement_view.company_description LIKE upper('%Electrolux Professional SWEDEN AB%') AND ITEM_CODE_VALID = 'Y' AND DWH_PUBLIC.agreement_view.company_code = DWH_PUBLIC.item_master.company_code AND DWH_PUBLIC.agreement_view.plant_code = DWH_PUBLIC.item_master.plant_code AND trim(DWH_PUBLIC.agreement_view.item_code) = trim(DWH_PUBLIC.item_master.item_code))", "bot": ["COMPLI_BOT"]}, "agreement_10_test": {"inquiry": "Is it possible for you to share with me the comprehensive set of codes that are currently not used but belong to a valid agreement for company UAB?", "sql": "SELECT DWH_PUBLIC.item_master.item_code, DWH_PUBLIC.item_master.main_item_description, DWH_PUBLIC.agreement_row.agreement_code FROM DWH_PUBLIC.agreement_view JOIN DWH_PUBLIC.agreement_row ON DWH_PUBLIC.agreement_row.company_code = DWH_PUBLIC.agreement_view.company_code AND DWH_PUBLIC.agreement_row.plant_code = DWH_PUBLIC.agreement_view.plant_code AND DWH_PUBLIC.agreement_row.agreement_code = DWH_PUBLIC.agreement_view.agreement_code JOIN DWH_PUBLIC.item_master ON DWH_PUBLIC.agreement_row.company_code = DWH_PUBLIC.item_master.company_code AND DWH_PUBLIC.agreement_row.plant_code = DWH_PUBLIC.item_master.plant_code AND trim(DWH_PUBLIC.agreement_row.item_code) = trim(DWH_PUBLIC.item_master.item_code) WHERE DWH_PUBLIC.agreement_view.company_code = 'UAB' AND locked_code = '0' AND CURRENT_DATE BETWEEN start_validity_date AND stop_validity_date AND DWH_PUBLIC.item_master.iTEM_CODE_VALID='N'", "bot": ["COMPLI_BOT"]}, "agreement_11_test": {"inquiry": "I am in need of the catalog of currently active codes, as well as the agreement that is legally enforceable for supplier 00977324 and company JPE", "sql": "SELECT DWH_PUBLIC.item_master.item_code, DWH_PUBLIC.item_master.main_item_description, DWH_PUBLIC.agreement_row.agreement_code, DWH_PUBLIC.agreement_row.plant_code, DWH_PUBLIC.agreement_row.plant_description FROM DWH_PUBLIC.agreement_view JOIN DWH_PUBLIC.agreement_row ON DWH_PUBLIC.agreement_row.company_code = DWH_PUBLIC.agreement_view.company_code AND DWH_PUBLIC.agreement_row.plant_code = DWH_PUBLIC.agreement_view.plant_code AND DWH_PUBLIC.agreement_row.agreement_code = DWH_PUBLIC.agreement_view.agreement_code JOIN DWH_PUBLIC.item_master ON DWH_PUBLIC.agreement_row.company_code = DWH_PUBLIC.item_master.company_code AND DWH_PUBLIC.agreement_row.plant_code = DWH_PUBLIC.item_master.plant_code AND trim(DWH_PUBLIC.agreement_row.item_code) = trim(DWH_PUBLIC.item_master.item_code) WHERE locked_code = '0' AND CURRENT_DATE BETWEEN start_validity_date AND stop_validity_date AND supplier = '00977324' AND DWH_PUBLIC.agreement_view.company_code = 'JPE' AND DWH_PUBLIC.item_master.iTEM_CODE_VALID='Y'", "bot": ["COMPLI_BOT"]}, "agreement_12_test": {"inquiry": "Can you share the inventory of codes that are not currently active, yet appear in an ongoing agreement between supplier 00380341 and company DEB?", "sql": "SELECT DWH_PUBLIC.item_master.item_code, DWH_PUBLIC.item_master.main_item_description, DWH_PUBLIC.agreement_row.agreement_code, DWH_PUBLIC.agreement_row.plant_code, DWH_PUBLIC.agreement_row.plant_description FROM DWH_PUBLIC.agreement_view JOIN DWH_PUBLIC.agreement_row ON DWH_PUBLIC.agreement_row.company_code = DWH_PUBLIC.agreement_view.company_code AND DWH_PUBLIC.agreement_row.plant_code = DWH_PUBLIC.agreement_view.plant_code AND DWH_PUBLIC.agreement_row.agreement_code = DWH_PUBLIC.agreement_view.agreement_code JOIN DWH_PUBLIC.item_master ON DWH_PUBLIC.agreement_row.company_code = DWH_PUBLIC.item_master.company_code AND DWH_PUBLIC.agreement_row.plant_code = DWH_PUBLIC.item_master.plant_code AND trim(DWH_PUBLIC.agreement_row.item_code) = trim(DWH_PUBLIC.item_master.item_code) WHERE locked_code = '0' AND CURRENT_DATE BETWEEN start_validity_date AND stop_validity_date AND supplier = '00380341' AND DWH_PUBLIC.agreement_view.company_code = 'DEB' AND DWH_PUBLIC.item_master.iTEM_CODE_VALID='N'", "bot": ["COMPLI_BOT"]}, "compliance_0_test": {"inquiry": "Kindly supply me with a selection of GBN components on which we have no information about where they come from", "sql": "SELECT item_code,plant_description FROM DWH_PUBLIC.item_compliances WHERE company_code = 'GBN' AND CERTIFICATION_TYPE = 'CoO' AND CERTIFICATION_COUNTRY IS NULL", "bot": ["COMPLI_BOT"]}, "compliance_1_test": {"inquiry": "Would it be possible for you to generate a catalog of items that have a country of origin certification specifically for Spm Drink Systems Spa?", "sql": "SELECT item_code,plant_description FROM DWH_PUBLIC.item_compliances WHERE upper(company_description) like upper('%Spm Drink Systems Spa%') AND CERTIFICATION_TYPE = 'CoO' AND CERTIFICATION_COUNTRY IS NOT NULL", "bot": ["COMPLI_BOT"]}, "compliance_2_test": {"inquiry": "Which are the valid items in Electrolux Professional Lim for which the supplier did not provide a certification about their origin?", "sql": "SELECT DWH_PUBLIC.item_master.item_code,DWH_PUBLIC.item_master.mAIN_ITEM_DESCRIPTION, DWH_PUBLIC.item_compliances.plant_description FROM DWH_PUBLIC.item_compliances, DWH_PUBLIC.item_master WHERE upper(c.company_code) = upper('Electrolux Professional Lim') AND DWH_PUBLIC.item_compliances.CERTIFICATION_TYPE = 'CoO' AND DWH_PUBLIC.item_compliances.CERTIFICATION_COUNTRY IS NULL AND DWH_PUBLIC.item_master.iTEM_CODE_VALID='Y' AND DWH_PUBLIC.item_compliances.company_code = DWH_PUBLIC.item_master.company_code AND DWH_PUBLIC.item_compliances.plant_code = DWH_PUBLIC.item_master.plant_code AND DWH_PUBLIC.item_compliances.item_code = DWH_PUBLIC.item_master.item_code", "bot": ["COMPLI_BOT"]}, "compliance_3_test": {"inquiry": "I am in need of a record of permissible items for DEB that possess a country of origin certification, including those that qualify for common/preferential benefits.", "sql": "SELECT DWH_PUBLIC.item_master.item_code,DWH_PUBLIC.item_master.mAIN_ITEM_DESCRIPTION, DWH_PUBLIC.item_compliances.plant_description,preferential_description FROM DWH_PUBLIC.item_compliances, DWH_PUBLIC.item_master WHERE DWH_PUBLIC.item_compliances.company_code = 'DEB' AND DWH_PUBLIC.item_compliances.CERTIFICATION_TYPE = 'CoO' AND DWH_PUBLIC.item_compliances.CERTIFICATION_COUNTRY IS NOT NULL AND DWH_PUBLIC.item_master.iTEM_CODE_VALID='Y' AND DWH_PUBLIC.item_compliances.company_code = DWH_PUBLIC.item_master.company_code AND DWH_PUBLIC.item_compliances.plant_code = DWH_PUBLIC.item_master.plant_code AND DWH_PUBLIC.item_compliances.item_code = DWH_PUBLIC.item_master.item_code", "bot": ["COMPLI_BOT"]}, "compliance_4_test": {"inquiry": "Could you please provide me with a comprehensive list of valid items for Electrolux Professional Gmbh that is certified with a currently valid country of origin?", "sql": "SELECT DWH_PUBLIC.item_master.item_code,DWH_PUBLIC.item_master.mAIN_ITEM_DESCRIPTION, DWH_PUBLIC.item_compliances.plant_description,preferential_description FROM DWH_PUBLIC.item_compliances, DWH_PUBLIC.item_master WHERE upper(c.company_description) LIKE upper('%Electrolux Professional Gmbh%') AND DWH_PUBLIC.item_compliances.CERTIFICATION_TYPE = 'CoO' AND DWH_PUBLIC.item_compliances.CERTIFICATION_COUNTRY IS NOT NULL AND DWH_PUBLIC.item_master.iTEM_CODE_VALID='Y' AND DWH_PUBLIC.item_compliances.certification_end_validity_date >= CURRENT_DATE AND DWH_PUBLIC.item_compliances.company_code = DWH_PUBLIC.item_master.company_code AND DWH_PUBLIC.item_compliances.plant_code = DWH_PUBLIC.item_master.plant_code AND DWH_PUBLIC.item_compliances.item_code = DWH_PUBLIC.item_master.item_code", "bot": ["COMPLI_BOT"]}, "compliance_5_test": {"inquiry": "Would it be possible for you to furnish me with an inventory of suppliers, uncertified for their country of origin, who are currently furnishing Electrolux Professional Australia?", "sql": "SELECT supplier,supplier_description FROM DWH_PUBLIC.item_compliances WHERE upper(company_description) like upper('%Electrolux Professional Australia%') AND CERTIFICATION_TYPE = 'CoO' AND CERTIFICATION_COUNTRY IS NULL GROUP BY supplier,supplier_description", "bot": ["COMPLI_BOT"]}, "compliance_6_test": {"inquiry": "I am in need of a comprehensive list of components from LY, plant Laundry Factory - Production, that are proved to originate from the United Kingdom ", "sql": "SELECT item_code,plant_description FROM DWH_PUBLIC.item_compliances WHERE company_code = 'LY' AND upper(plant_code) = upper('Laundry Factory - Production') AND CERTIFICATION_TYPE = 'CoO' AND upper(CERTIFICATION_COUNTRY_description) LIKE upper('%United Kingdom%')", "bot": ["COMPLI_BOT"]}, "compliance_7_test": {"inquiry": "Can you provide me with an inventory of parts obtained from the country ID, specifying if they have common/preferential status and stating their supplier?", "sql": "SELECT item_code,plant_description, supplier, supplier_description, preferential_description FROM DWH_PUBLIC.item_compliances WHERE CERTIFICATION_COUNTRY = 'ID' AND CERTIFICATION_TYPE = 'CoO'", "bot": ["COMPLI_BOT"]}, "compliance_8_test": {"inquiry": "I'm interested in obtaining the complete list of items, along with their respective HS codes, for Electrolux Laundry systems France SNC, Troyes-Services plant. Show me only those that have a country of origin certificate from supplier 00807177 that still holds", "sql": "SELECT item_code,CERTIFICATION_END_VALIDITY_DATE, hs_code, preferential_description FROM DWH_PUBLIC.item_compliances WHERE CERTIFICATION_COUNTRY IS NOT NULL AND CERTIFICATION_TYPE = 'CoO' AND supplier = '00571187' AND upper(company_description) like upper('%Electrolux Laundry systems France SNC%') AND upper(plant_description) like upper('Troyes-Services') AND CERTIFICATION_END_VALIDITY_DATE >= CURRENT_DATE", "bot": ["COMPLI_BOT"]}, "compliance_9_test": {"inquiry": "Kindly share the list of items associated with electrolux professional thailand co. ltd, plant rayong factory, for supplier GCE. However, please omit any items that lack a valid CoO certification.", "sql": "SELECT item_code,CERTIFICATION_END_VALIDITY_DATE, hs_code, preferential_description FROM DWH_PUBLIC.item_compliances WHERE CERTIFICATION_COUNTRY IS NOT NULL AND CERTIFICATION_TYPE = 'CoO' AND upper(supplier_description) like '%GCE%' AND upper(company_description) like upper('%electrolux professional thailand co. ltd%') AND upper(plant_description) LIKE upper('%rayong factory%') AND CERTIFICATION_END_VALIDITY_DATE >= CURRENT_DATE", "bot": ["COMPLI_BOT"]}, "compliance_10_test": {"inquiry": "I would appreciate it if you could furnish me with a detailed breakdown of the origin certificate of article RR000W01A6, specifically in connection with the company LY.", "sql": "SELECT plant_description,supplier,supplier_description,item_code,certification_start_validity_date,certification_end_validity_date,certification_country,certification_country_description,preferential, preferential_description,hs_code FROM DWH_PUBLIC.item_compliances WHERE company_code = 'LY' AND item_code = 'RR000W01A6' AND certification_type = 'CoO'", "bot": ["COMPLI_BOT"]}, "compliance_11_test": {"inquiry": "May I request an organized inventory of items for Electrolux Professional SpA, plant HB modular components, with expiring Country of Origin (COO) certification?", "sql": "SELECT item_code,plant_description, certification_end_validity_date FROM DWH_PUBLIC.item_compliances WHERE upper(company_description) like upper('%Electrolux Professional SpA%') AND upper(plant_code)= upper('HB modular components') AND CERTIFICATION_TYPE = 'CoO' AND certification_end_validity_date <= CURRENT_DATE", "bot": ["COMPLI_BOT"]}, "compliance_12_test": {"inquiry": "What percentage of ITP suppliers have obtained a certification for their provenance?", "sql": "SELECT CONCAT(TO_CHAR(count(DISTINCT c2.supplier)/count(DISTINCT c1.supplier) *100, 'fm00D00') , '%') FROM DWH_PUBLIC.item_compliances c1 LEFT JOIN DWH_PUBLIC.item_compliances c2 ON c2.company_code = c1.company_code AND c2.plant_code = c1.plant_code AND c2.item_code = c1.item_code AND c2.SUPPLIER = c1.SUPPLIER AND c2.CERTIFICATION_TYPE =c1.CERTIFICATION_TYPE AND c2.CERTIFICATION_COUNTRY IS NOT NULL WHERE c1.company_code = 'ITP' AND c1.CERTIFICATION_TYPE = 'CoO'", "bot": ["COMPLI_BOT"]}, "compliance_13_test": {"inquiry": "I'm interested in knowing the prevalence of items for an Electrolux Laundry systems France SNC that have a country of origin certification. Can you provide that information?", "sql": "SELECT CONCAT(TO_CHAR(count(c2.item_code)/count(c1.item_code) *100, 'fm00D00') , '%') FROM DWH_PUBLIC.item_compliances c1 LEFT JOIN DWH_PUBLIC.item_compliances c2 ON c2.company_code = c1.company_code AND c2.plant_code = c1.plant_code AND c2.item_code = c1.item_code AND c2.SUPPLIER = c1.SUPPLIER AND c2.CERTIFICATION_TYPE =c1.CERTIFICATION_TYPE AND c2.CERTIFICATION_COUNTRY IS NOT NULL WHERE upper(c1.company_description) LIKE upper('%Electrolux Laundry systems France SNC%') AND c1.CERTIFICATION_TYPE = 'CoO'", "bot": ["COMPLI_BOT"]}, "compliance_14_test": {"inquiry": "Could you please give me the number of items we have from supplier 00503904 that do not possess any ROHS certification?", "sql": "SELECT count(*),plant_description FROM DWH_PUBLIC.item_compliances WHERE CERTIFICATION_TYPE = 'RoH' AND supplier = '00503904' AND certification_status IS NULL GROUP BY plant_description, certification_status", "bot": ["COMPLI_BOT"]}, "compliance_15_test": {"inquiry": "Please share the list of suppliers for Electrolux Laundry Systems France SNC that possess the necessary ROHS certification.", "sql": "SELECT count(DISTINCT supplier) FROM DWH_PUBLIC.item_compliances WHERE CERTIFICATION_TYPE = 'RoH' AND upper(company_description) like upper('%Electrolux Laundry systems France SNC%') AND certification_status IN ('C','V')", "bot": ["COMPLI_BOT"]}, "compliance_16_test": {"inquiry": "Could you kindly provide me with the list of suppliers for GBN lacking ROHS certification?", "sql": "SELECT count(DISTINCT supplier) FROM DWH_PUBLIC.item_compliances WHERE CERTIFICATION_TYPE = 'RoH' AND upper(company_description) like upper('%Electrolux Professional SAS%') AND certification_status IS NULL", "bot": ["COMPLI_BOT"]}, "compliance_17_test": {"inquiry": "I would like to obtain information on the share of Electrolux Professionell sas' suppliers that hold a valid ROHS certification.", "sql": "SELECT CONCAT(TO_CHAR(count(DISTINCT c2.supplier)/count(DISTINCT c1.supplier) *100, 'fm00D00') , '%') FROM DWH_PUBLIC.item_compliances c1 LEFT JOIN DWH_PUBLIC.item_compliances c2 ON c2.company_code = c1.company_code AND c2.plant_code = c1.plant_code AND c2.item_code = c1.item_code AND c2.SUPPLIER = c1.SUPPLIER AND c2.CERTIFICATION_TYPE =c1.CERTIFICATION_TYPE AND c2.certification_status IN ('C','V') WHERE upper(c1.company_description) LIKE upper('%Electrolux Professionell sas%') AND c1.CERTIFICATION_TYPE = 'RoH'", "bot": ["COMPLI_BOT"]}, "compliance_18_test": {"inquiry": "What is the percentage of items in FSP that have been accredited with a ROHS certification?", "sql": "SELECT CONCAT(TO_CHAR(count(c2.item_code)/count(c1.item_code) *100, 'fm00D00') , '%') FROM DWH_PUBLIC.item_compliances c1 LEFT JOIN DWH_PUBLIC.item_compliances c2 ON c2.company_code = c1.company_code AND c2.plant_code = c1.plant_code AND c2.item_code = c1.item_code AND c2.SUPPLIER = c1.SUPPLIER AND c2.CERTIFICATION_TYPE =c1.CERTIFICATION_TYPE AND c2.certification_status IN ('C','V') WHERE c1.company_code = 'FSP' AND c1.CERTIFICATION_TYPE = 'RoH'", "bot": ["COMPLI_BOT"]}, "compliance_19_test": {"inquiry": "Could you furnish me with a comprehensive record of suppliers per company, specifically those with a valid ROHS certification pending validation?", "sql": "SELECT supplier, supplier_description, company_description FROM DWH_PUBLIC.item_compliances WHERE CERTIFICATION_TYPE = 'RoH' AND certification_status = 'C' AND certification_end_validity_date >= CURRENT_DATE GROUP BY supplier, supplier_description, company_description", "bot": ["COMPLI_BOT"]}, "compliance_20_test": {"inquiry": "List all the components in Electrolux Professional Gmbh with a ROHS certification still awaiting validation.", "sql": "SELECT item_code, plant_description FROM DWH_PUBLIC.item_compliances WHERE CERTIFICATION_TYPE = 'RoH' AND upper(company_description) like upper('%Electrolux Professional Gmbh%') AND certification_status = 'C'", "bot": ["COMPLI_BOT"]}, "compliance_21_test": {"inquiry": "Can you furnish me with a verified record of items that hold a ROHS certification that has already been validated?", "sql": "SELECT item_code, company_description,plant_description FROM DWH_PUBLIC.item_compliances WHERE CERTIFICATION_TYPE = 'RoH' AND certification_status = 'V'", "bot": ["COMPLI_BOT"]}, "compliance_22_test": {"inquiry": "I want the tally of parts from supplier 00320239 that do not possess a valid REACH certification?L", "sql": "SELECT count(*), company_description,plant_description FROM DWH_PUBLIC.item_compliances WHERE CERTIFICATION_TYPE = 'Rea' AND trim(nvl(certification_status,'-')) NOT IN ('C', 'V') AND supplier = '00320239' GROUP BY company_description,plant_description", "bot": ["COMPLI_BOT"]}, "compliance_23_test": {"inquiry": "I would like to receive the compilation of suppliers for Electrolux Professional SpA who sell articles with a valid REACH certification.", "sql": "SELECT DISTINCT supplier, supplier_description FROM DWH_PUBLIC.item_compliances WHERE CERTIFICATION_TYPE = 'Rea' AND certification_status IN ('C', 'V') AND upper(company_description) like upper('%Electrolux Professional SpA%')", "bot": ["COMPLI_BOT"]}, "compliance_24_test": {"inquiry": "I would like to receive the compilation of suppliers for company ITP who do not hold a valid REACH certification.", "sql": "SELECT DISTINCT supplier, supplier_description FROM DWH_PUBLIC.item_compliances WHERE CERTIFICATION_TYPE = 'Rea' AND trim(nvl(certification_status,'-')) NOT IN ('C', 'V','O') AND company_code = 'ITP'", "bot": ["COMPLI_BOT"]}, "compliance_25_test": {"inquiry": "What is the proportion of suppliers in Electrolux Professionell sas who possess a REACH certification?", "sql": "SELECT CONCAT(TO_CHAR(count(DISTINCT c2.supplier)/count(DISTINCT c1.supplier) *100, 'fm00D00') , '%') FROM DWH_PUBLIC.item_compliances c1 LEFT JOIN DWH_PUBLIC.item_compliances c2 ON c2.company_code = c1.company_code AND c2.plant_code = c1.plant_code AND c2.item_code = c1.item_code AND c2.SUPPLIER = c1.SUPPLIER AND c2.CERTIFICATION_TYPE =c1.CERTIFICATION_TYPE AND c2.certification_status IN ('C','V') WHERE upper(c1.company_description) LIKE upper('%Electrolux Professionell sas%') AND c1.CERTIFICATION_TYPE = 'Rea'", "bot": ["COMPLI_BOT"]}, "compliance_26_test": {"inquiry": "Could you provide me with the percentage of parts in RUH that are REACH certified?", "sql": "SELECT CONCAT(TO_CHAR(count(c2.item_code)/count(c1.item_code) *100, 'fm00D00') , '%') FROM DWH_PUBLIC.item_compliances c1 LEFT JOIN DWH_PUBLIC.item_compliances c2 ON c2.company_code = c1.company_code AND c2.plant_code = c1.plant_code AND c2.item_code = c1.item_code AND c2.SUPPLIER = c1.SUPPLIER AND c2.CERTIFICATION_TYPE =c1.CERTIFICATION_TYPE AND c2.certification_status IN ('C','V') WHERE c1.company_code = 'RUH' AND c1.CERTIFICATION_TYPE = 'Rea'", "bot": ["COMPLI_BOT"]}, "compliance_27_test": {"inquiry": "Could you furnish me with the inventory of active suppliers whose REACH certification requires verification?", "sql": "SELECT supplier, supplier_description, company_description FROM DWH_PUBLIC.item_compliances WHERE CERTIFICATION_TYPE = 'Rea' AND certification_status = 'C' AND certification_end_validity_date >= CURRENT_DATE GROUP BY supplier, supplier_description, company_description", "bot": ["COMPLI_BOT"]}, "compliance_28_test": {"inquiry": "Please share the list of articles that require checking for their REACH certification.", "sql": "SELECT item_code, company_description FROM DWH_PUBLIC.item_compliances WHERE CERTIFICATION_TYPE = 'Rea' AND certification_status = 'C'", "bot": ["COMPLI_BOT"]}, "compliance_29_test": {"inquiry": "Could you show me the codes whose REACH certification has been approved?", "sql": "SELECT item_code, company_description FROM DWH_PUBLIC.item_compliances WHERE CERTIFICATION_TYPE = 'Rea' AND certification_status = 'V'", "bot": ["COMPLI_BOT"]}, "compliance_30_test": {"inquiry": "I need the catalogue of articles whose REACH certification has lapsed.", "sql": "SELECT item_code, company_description, certification_end_validity_date, certification_status FROM DWH_PUBLIC.item_compliances WHERE CERTIFICATION_TYPE = 'Rea' AND certification_end_validity_date < CURRENT_DATE AND certification_end_validity_date > certification_start_validity_date", "bot": ["COMPLI_BOT"]}, "compliance_31_test": {"inquiry": "Can you provide me with the inventory of items that have an unfinished REACH certification and require additional documentation?", "sql": "SELECT item_code, company_description FROM DWH_PUBLIC.item_compliances WHERE CERTIFICATION_TYPE = 'Rea' AND trim(nvl(certification_status,'-')) NOT IN ('C', 'V','O')", "bot": ["COMPLI_BOT"]}, "compliance_32_test": {"inquiry": "Can you provide me with the roster of items that possess valid REACH certification and are categorized as \"nd\"?", "sql": "SELECT item_code, company_description FROM DWH_PUBLIC.item_compliances WHERE CERTIFICATION_TYPE = 'Rea' AND certification_status IN ('C','V') AND upper(certification_category_description) like upper('%nd%')", "bot": ["COMPLI_BOT"]}, "compliance_33_test": {"inquiry": "Please inform me of the number of items we acquire from supplier 00271386 that do not meet the food contact certification standards.", "sql": "SELECT count(*), company_description,plant_description FROM DWH_PUBLIC.item_compliances WHERE CERTIFICATION_TYPE = 'FCo' AND nvl(certification_status,'-') NOT IN ('C', 'V') AND supplier = '00271386' GROUP BY company_description,plant_description", "bot": ["COMPLI_BOT"]}, "compliance_34_test": {"inquiry": "I would like to receive a comprehensive list of the suppliers that hold FC certification for Electrolux Professional Thailand CO. LTD.", "sql": "SELECT DISTINCT supplier, supplier_description FROM DWH_PUBLIC.item_compliances WHERE CERTIFICATION_TYPE = 'FCo' AND nvl(certification_status,'-') IN ('C', 'V') AND upper(company_description) like upper('%Electrolux Professional Thailand CO. LTD%')", "bot": ["COMPLI_BOT"]}, "compliance_35_test": {"inquiry": "Can you furnish me with a roster of the suppliers for Electrolux Professional SpA that are not certified for food contact?", "sql": "SELECT DISTINCT supplier, supplier_description FROM DWH_PUBLIC.item_compliances WHERE CERTIFICATION_TYPE = 'FCo' AND nvl(certification_status,'-') NOT IN ('C', 'V','O') AND upper(company_description) like upper('%Electrolux Professional SpA%')", "bot": ["COMPLI_BOT"]}, "compliance_36_test": {"inquiry": "Give me the percentage of suppliers for FFW that have achieved an FC certification.", "sql": "SELECT CONCAT(TO_CHAR(count(DISTINCT c2.supplier)/count(DISTINCT c1.supplier) *100, 'fm00D00') , '%') FROM DWH_PUBLIC.item_compliances c1 LEFT JOIN DWH_PUBLIC.item_compliances c2 ON c2.company_code = c1.company_code AND c2.plant_code = c1.plant_code AND c2.item_code = c1.item_code AND c2.SUPPLIER = c1.SUPPLIER AND c2.CERTIFICATION_TYPE =c1.CERTIFICATION_TYPE AND c2.certification_status IN ('C','V') WHERE company_code = 'FFW' AND c1.CERTIFICATION_TYPE = 'FCo'", "bot": ["COMPLI_BOT"]}, "compliance_37_test": {"inquiry": "Please share the percentage of items bought in Sweden that have obtained a food contact certification.", "sql": "SELECT CONCAT(TO_CHAR(count(c2.item_code)/count(c1.item_code) *100, 'fm00D00') , '%') FROM DWH_PUBLIC.item_compliances c1 LEFT JOIN DWH_PUBLIC.item_compliances c2 ON c2.company_code = c1.company_code AND c2.plant_code = c1.plant_code AND c2.item_code = c1.item_code AND c2.SUPPLIER = c1.SUPPLIER AND c2.CERTIFICATION_TYPE =c1.CERTIFICATION_TYPE AND c2.certification_status IN ('C','V') WHERE c1.company_code = 'PA' AND c1.CERTIFICATION_TYPE = 'FCo'", "bot": ["COMPLI_BOT"]}, "compliance_38_test": {"inquiry": "Give me the inventory of suppliers who hold a certification for food contact and require validation.", "sql": "SELECT supplier, supplier_description, company_description FROM DWH_PUBLIC.item_compliances WHERE CERTIFICATION_TYPE = 'FCo' AND certification_status = 'C' AND certification_end_validity_date >= CURRENT_DATE GROUP BY supplier, supplier_description, company_description", "bot": ["COMPLI_BOT"]}, "compliance_39_test": {"inquiry": "I would appreciate it if you could supply me with the record of items that have a certification for food contact that still needs to undergo validation.", "sql": "SELECT item_code, company_description FROM DWH_PUBLIC.item_compliances WHERE CERTIFICATION_TYPE = 'FCo' AND trim(nvl(certification_status,'-')) NOT IN ('V','O')", "bot": ["COMPLI_BOT"]}, "compliance_40_test": {"inquiry": "Please furnish me with the comprehensive inventory of components that have met the requirements for an FC certification.", "sql": "SELECT item_code, company_description FROM DWH_PUBLIC.item_compliances WHERE CERTIFICATION_TYPE = 'FCo' AND certification_status='V'", "bot": ["COMPLI_BOT"]}, "items_12_test": {"inquiry": "What is the amount we paid to purchase item RR000G1604?", "sql": "SELECT company_code,plant_code,item_code,main_item_description,purchasing_cost FROM DWH_PUBLIC.item_master WHERE item_code='RR000G1604'", "bot": ["COMPLI_BOT", "CALL_CENTER_BOT"]}, "items_13_test": {"inquiry": "What is the stk3 cost at the regular level for item 9F11316325?", "sql": "SELECT company_code,plant_code,item_code,main_item_description,standard_stk3_cost FROM DWH_PUBLIC.item_master WHERE item_code='9F11316325' AND item_stocking_type='M'", "bot": ["COMPLI_BOT", "CALL_CENTER_BOT"]}, "items_14_test": {"inquiry": "Fetch me the stk3 rate for item 0217036604", "sql": "SELECT company_code,plant_code,item_code,main_item_description,standard_stk3_cost FROM DWH_PUBLIC.item_master WHERE item_code='0217036604' AND item_stocking_type='M'", "bot": ["COMPLI_BOT", "CALL_CENTER_BOT"]}, "items_15_test": {"inquiry": "Show me the production cost for item 9FP9902700.", "sql": "SELECT company_code,plant_code,item_code,main_item_description,standard_stk3_cost FROM DWH_PUBLIC.item_master WHERE item_code='9FP9902700' AND item_stocking_type='M'", "bot": ["COMPLI_BOT", "CALL_CENTER_BOT"]}, "items_16_test": {"inquiry": "What is the expenditure of external workforce for item 0M02249000?", "sql": "SELECT company_code,plant_code,item_code,main_item_description,external_work_cost FROM DWH_PUBLIC.item_master WHERE item_code='0M02249000'", "bot": ["COMPLI_BOT", "CALL_CENTER_BOT"]}, "items_17_test": {"inquiry": "Please share with me the inventory details for company RUH, arranged in descending order of cost of manufactoring.", "sql": "SELECT company_code,plant_code,item_code,main_item_description,standard_stk3_cost FROM DWH_PUBLIC.item_master WHERE company_code='RUH' AND item_stocking_type='M' ORDER BY standard_stk3_cost DESC", "bot": ["COMPLI_BOT", "CALL_CENTER_BOT"]}, "items_18_test": {"inquiry": "What is the pricing structure for the items produced by the Electrolux Professional OY company?", "sql": "SELECT company_code,plant_code,item_code,main_item_description,standard_stk3_cost FROM DWH_PUBLIC.item_master WHERE upper(company_description) like upper('%Electrolux Professional OY%') AND item_stocking_type='M'", "bot": ["COMPLI_BOT", "CALL_CENTER_BOT"]}, "items_19_test": {"inquiry": "What is the typical manufacturing outlay for each item?", "sql": "SELECT company_code,plant_code,item_code,main_item_description,standard_stk3_cost FROM DWH_PUBLIC.item_master WHERE item_stocking_type='M'", "bot": ["COMPLI_BOT", "CALL_CENTER_BOT"]}, "items_20_test": {"inquiry": "Can you inform me of the regular STK3 cost for items with type M?", "sql": "SELECT company_code,plant_code,item_code,main_item_description,standard_stk3_cost FROM DWH_PUBLIC.item_master WHERE item_stocking_type='M'", "bot": ["COMPLI_BOT", "CALL_CENTER_BOT"]}, "items_21_test": {"inquiry": "Tell me the purchased cost for the items that are classified as type B in terms of stocking.", "sql": "SELECT company_code,plant_code,item_code,main_item_description,purchasing_cost FROM DWH_PUBLIC.item_master WHERE item_stocking_type='B'", "bot": ["COMPLI_BOT", "CALL_CENTER_BOT"]}, "items_22_test": {"inquiry": "What is the purchasing expenditure for our items?", "sql": "SELECT company_code,plant_code,item_code,main_item_description,purchasing_cost FROM DWH_PUBLIC.item_master WHERE item_stocking_type='B'", "bot": ["COMPLI_BOT", "CALL_CENTER_BOT"]}, "suppliers_0_test": {"inquiry": "Who are the companies that supply to the company ITP?", "sql": "SELECT supplier, supplier_description FROM DWH_PUBLIC.SUPPLIER WHERE company_code = 'ITP'", "bot": ["COMPLI_BOT", "CALL_CENTER_BOT"]}, "suppliers_1_test": {"inquiry": "Can you give me an overview of the suppliers associated with the company in Germany?", "sql": "SELECT supplier, supplier_description FROM DWH_PUBLIC.SUPPLIER WHERE upper(company_description) like upper('%Germany%')", "bot": ["COMPLI_BOT", "CALL_CENTER_BOT"]}, "suppliers_2_test": {"inquiry": "Can you share the information regarding suppliers in DXD who are currently active?", "sql": "SELECT supplier, supplier_description FROM DWH_PUBLIC.SUPPLIER WHERE company_code = 'DXD' AND SUPPLIER_STATUS = 'A'", "bot": ["COMPLI_BOT", "CALL_CENTER_BOT"]}, "suppliers_3_test": {"inquiry": "I request you to provide me with the list of suppliers for SOB who are currently marked as inactive.", "sql": "SELECT supplier, supplier_description FROM DWH_PUBLIC.SUPPLIER WHERE company_code = 'SOB' AND SUPPLIER_STATUS <> 'A'", "bot": ["COMPLI_BOT", "CALL_CENTER_BOT"]}, "suppliers_4_test": {"inquiry": "Is it feasible for you to provide the details of the description, currency, and currency description for supplier code 00952954?", "sql": "SELECT company_description, supplier_description, currency, currency_description FROM DWH_PUBLIC.SUPPLIER WHERE supplier = '00952954'", "bot": ["COMPLI_BOT", "CALL_CENTER_BOT"]}, "suppliers_5_test": {"inquiry": "May I ask what language is set for supplier 00296984?", "sql": "SELECT company_description, LANGUAGE, LANGUAGE_description FROM DWH_PUBLIC.SUPPLIER WHERE supplier = '00296984'", "bot": ["COMPLI_BOT", "CALL_CENTER_BOT"]}, "suppliers_6_test": {"inquiry": "Can you make the suppliers' codes, descriptions, and status for NLV accessible to me?", "sql": "SELECT supplier, supplier_description, supplier_status FROM DWH_PUBLIC.SUPPLIER WHERE company_code = 'NLV'", "bot": ["COMPLI_BOT", "CALL_CENTER_BOT"]}, "suppliers_7_test": {"inquiry": "Would you mind telling me the payment code and payment description associated with supplier code 00500813?", "sql": "SELECT company_description, PAYMENT_CODE, PAYMENT_DESCRIPTION FROM DWH_PUBLIC.SUPPLIER WHERE supplier = '00500813'", "bot": ["COMPLI_BOT", "CALL_CENTER_BOT"]}, "suppliers_8_test": {"inquiry": "Can you share the suppliers whose payment code is 120B in Electrolux Professional Thailand CO. LTD?", "sql": "SELECT supplier, supplier_description FROM DWH_PUBLIC.SUPPLIER WHERE payment_code = '120B' AND upper(company_description) like upper('%SPM DRINK SYSTEMS SPA%')", "bot": ["COMPLI_BOT", "CALL_CENTER_BOT"]}, "suppliers_9_test": {"inquiry": "Could you provide me with an extensive summary of suppliers that have a name containing \"Gaia\"?", "sql": "SELECT SUPPLIER,<PERSON><PERSON><PERSON><PERSON><PERSON>_DESC<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>,COMP<PERSON><PERSON>_DESCRIPTION,OLD_SUPPLIER,CURRENC<PERSON>,CURRENCY_DESCRIPTION,PURCHASING_STATUS,ADMIN_STATUS,<PERSON>YME<PERSON>_CODE,PAYMENT_DESCRIPTION,<PERSON><PERSON><PERSON>IER_CATEGORY,SUPPLIER_CATEGORY_DESCRIPTION,LANGUAGE,LANGUAGE_DESCRIPTION,EDI_STATUS,TOP_IDCO,TOP_IDCO_DESCRIPTION,SUPPLY_TYPE,SUPPLIER_STATUS FROM DWH_PUBLIC.SUPPLIER WHERE upper(supplier_description) like upper('%Gaia%')", "bot": ["COMPLI_BOT", "CALL_CENTER_BOT"]}, "suppliers_10_test": {"inquiry": "I would appreciate it if you could give me a complete record of suppliers for the company code DEB with the payment code 262B.", "sql": "SELECT supplier, supplier_description FROM DWH_PUBLIC.SUPPLIER WHERE payment_code = '262B' AND company_code = 'DEB'", "bot": ["COMPLI_BOT", "CALL_CENTER_BOT"]}, "suppliers_11_test": {"inquiry": "What is the figure for the suppliers in 'Electrolux Professional BV that have established connections through EDI?", "sql": "SELECT count(*) FROM DWH_PUBLIC.SUPPLIER WHERE upper(company_description) like upper('%'Electrolux Professional BV%') and edi_status = 'H'", "bot": ["COMPLI_BOT", "CALL_CENTER_BOT"]}, "suppliers_12_test": {"inquiry": "What is the total number of suppliers in Electrolux Professional S.p. with a populated IDCO code?", "sql": "SELECT count(*) FROM DWH_PUBLIC.SUPPLIER WHERE upper(company_description) like upper('%Electrolux Professional S.p.%') AND top_idco IS NOT NULL", "bot": ["COMPLI_BOT", "CALL_CENTER_BOT"]}, "suppliers_13_test": {"inquiry": "Please provide me with the generated inventory of suppliers for company CHC without a populated IDCO code.", "sql": "SELECT supplier, supplier_description, supplier FROM DWH_PUBLIC.SUPPLIER WHERE company_code = 'CHC' AND top_idco IS NULL", "bot": ["COMPLI_BOT", "CALL_CENTER_BOT"]}, "suppliers_14_test": {"inquiry": "What currency is associated with supplier code 01019086?", "sql": "SELECT currency_description FROM DWH_PUBLIC.SUPPLIER WHERE supplier = '01019086'", "bot": ["COMPLI_BOT", "CALL_CENTER_BOT"]}, "bom_3_test": {"inquiry": "Can you please provide me with a detailed inventory of all the constituent parts and subparts that make up item 9FM0914200, produced by company LY plant 5784P01?", "sql": "SELECT PARENT_ITEM_CODE, CHILD_ITEM_CODE FROM DWH_PUBLIC.BOM START WITH PARENT_ITEM_CODE = '9FM0914200' AND COMPANY_CODE='LY' AND PLANT_CODE='5784P01' CONNECT BY NOCYCLE PRIOR CHILD_ITEM_CODE = PARENT_ITEM_CODE ORDER BY PARENT_ITEM_CODE, CHILD_ITEM_CODE", "bot": ["DELETED"]}, "bom_4_test": {"inquiry": "Hand over the scalar bill of materials for item 0504S4YN00, for company JPE and plant 9144P01.", "sql": "SELECT PARENT_ITEM_CODE, CHILD_ITEM_CODE FROM DWH_PUBLIC.BOM START WITH PARENT_ITEM_CODE = '0504S4YN00' AND COMPANY_CODE='JPE' AND PLANT_CODE='9144P01' CONNECT BY NOCYCLE PRIOR CHILD_ITEM_CODE = PARENT_ITEM_CODE ORDER BY PARENT_ITEM_CODE, CHILD_ITEM_CODE", "bot": ["DELETED"]}}