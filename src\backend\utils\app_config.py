import threading
import time


class AppConfig:
    _instance = None
    _lock = threading.Lock()

    def __init__(self):
        if AppConfig._instance is not None:
            raise Exception("This class is a singleton!")
        self._config = {}
        self._refresh_interval = 30  # 5 minutes
        self._stop_event = threading.Event()
        self._thread = threading.Thread(target=self._refresh_config)
        self._thread.daemon = True
        self._thread.start()

    @staticmethod
    def get_instance():
        if AppConfig._instance is None:
            with AppConfig._lock:
                if AppConfig._instance is None:
                    AppConfig._instance = AppConfig()
        return AppConfig._instance

    def _refresh_config(self):
        while not self._stop_event.is_set():
            self._load_config_from_db()
            time.sleep(self._refresh_interval)

    def _load_config_from_db(self):
        # Local import to avoid circular import
        from src.backend.models import AppConfigDB
        from flask import current_app

        try:
            # First attempt with a local context (if it exists)
            results = AppConfigDB.query.all()
            self._config = {row.key_name: row.value for row in results}
        except RuntimeError:
            # If the context is no present, import and create a new context to use the db
            from src import app
            with app.app_context():
                results = AppConfigDB.query.all()
                self._config = {row.key_name: row.value for row in results}

    def get(self, key, default=None):
        return self._config.get(key, default)

    def stop(self):
        self._stop_event.set()
        self._thread.join()