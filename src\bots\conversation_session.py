from concurrent.futures.thread import Thread<PERSON>oolExecutor
from enum import Str<PERSON>num, auto
from json import dumps, loads
from threading import Event
from typing import Dict, List

import pandas as pd

from src.backend.contracts.chat_data import UserRole
from utils.core import get_logger
from utils.exceptions import StoppedException


class ExecutionMode(StrEnum):
    TEST = auto()
    INFERENCE = auto()


logger = get_logger(__file__)


class ConversationSession:
    def __init__(self, id: str, mode: UserRole) -> None:
        self.id: str = id
        self.executor: ThreadPoolExecutor = None
        self.stop_event: Event = Event()
        self.__mode: UserRole = mode

    @property
    def mode(self) -> UserRole:
        return self.__mode

    def get_pool_executor(
        self, n_concurrent: int, name: str = ""
    ) -> ThreadPoolExecutor:
        self.executor = ThreadPoolExecutor(n_concurrent, name)
        return self.executor

    def checkpoint(self) -> None:
        logger.debug(f"Stopped? {self.stop_event.is_set()}")
        if self.stop_event.is_set():
            raise StoppedException()

    def stop(self) -> None:
        logger.info("Stop command received!")
        self.stop_event.set()

        if self.executor:
            self.executor.shutdown(wait=False, cancel_futures=True)

    def save(self) -> str:
        return dumps({"id": self.id, "mode": self.__mode.value})

    def update(self, json_string: str) -> None:
        raw_data = loads(json_string)

        self.id = raw_data["id"]
        self.__mode = UserRole(raw_data["mode"])

        self.executor = self.executor or None
        self.stop_event = self.stop_event or Event()
