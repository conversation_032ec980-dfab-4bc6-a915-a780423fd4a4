CREATE TABLE DWH_PUBLIC.get_hierarchy_data (
    ID NUMBER,
    FATHER VARCHAR2(1000),
    CHILD VARCHAR2(1000),
    SPARE VARCHAR2(1000),
    COMPANY_CODE VARCHAR2(1000),
    PLANT_CODE VARCHAR2(1000),
    SEP2 VARCHAR2(1),
    LEVEL_1 VARCHAR2(1000),
    LEVEL_2 VARCHAR2(1000),
    LEVEL_3 VARCHAR2(1000),
    LEVEL_4 VARCHAR2(1000),
    LEVEL_5 VARCHAR2(1000),
    LEVEL_6 VARCHAR2(1000),
    LEVEL_7 VARCHAR2(1000),
    LEVEL_8 VARCHAR2(1000),
    LEVEL_9 VARCHAR2(1000),
    LEVEL_10 VARCHAR2(1000),
    LEVEL_11 VARCHAR2(1000),
    LEVEL_12 VARCHAR2(1000),
    LEVEL_13 VARCHAR2(1000)
);