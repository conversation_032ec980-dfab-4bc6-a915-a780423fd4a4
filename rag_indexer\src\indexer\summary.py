from langchain_core.output_parsers import StrOutputParser
from langchain_core.prompts import ChatPromptTemplate
from langchain_openai import AzureChatOpenAI
from langchain_core.runnables import RunnablePassthrough
from langchain_core.documents.base import Document
from config.config import RAGConfig

import base64

class Summary():
    def __init__(self) -> None:
        
        config = RAGConfig()

        #Initialize LLM to summarize chunk of text, tables and images
        self.llm= AzureChatOpenAI(
            api_version=config.generator_version,
            api_key=config.generator_key,
            azure_endpoint=config.generator_endpoint,
            azure_deployment=config.generator_model,
            temperature=config.generator_temperature,
            streaming= False,
            verbose= False,
        )

        # Prompt to summarize text and images
        self.prompt_text_tables = """
            You are an assistant tasked with summarizing tables and text particularly for semantic retrieval.
            These summaries will be embedded and used to retrieve the raw text or table elements
            Give a detailed summary of the table or text below that is well optimized for retrieval.
            For any tables also add in a one line description of what the table is about besides the summary.
            Do not add additional words like Summary: etc.
            Table or text chunk:
            {element}
            """

        # Prompt
        self.prompt_images = """
            You are an assistant tasked with summarizing images for retrieval.
            Remember these images could potentially contain graphs, charts or 
            tables also.
            These summaries will be embedded and used to retrieve the raw image 
            for question answering.
            Give a detailed summary of the image that is well optimized for 
            retrieval.
            Do not add additional words like Summary: etc.
            """
        
    # Encode images in base 64
    def encode_image(image):
        """Getting the base64 string"""
        # with open(image_path, "rb") as image_file:
        return base64.b64encode(image).decode("utf-8")
        
    # Summarize chunk of text
    def text_summary(self, doc: Document) -> str:
        prompt = ChatPromptTemplate.from_template(self.prompt_text_tables)
        summarize_text_chain = ({"element": RunnablePassthrough()} | prompt | self.llm | StrOutputParser())
        summary = summarize_text_chain.invoke(doc)
        return summary
    
    # Summarize tables
    def table_summary(self, table) -> str:
        prompt = ChatPromptTemplate.from_template(self.prompt_text_tables)
        summarize_table_chain = ({"element": RunnablePassthrough()} | prompt | self.llm | StrOutputParser())
        summary = summarize_table_chain.invoke(table)
        return summary

    # Summarize images
    def image_summary(self, image) -> str:
        # encoded_imgae = self.encode_image(image)
        prompt = ChatPromptTemplate.from_template(self.prompt_images)
        summarize_images_chain = ({"element": RunnablePassthrough()} | prompt | self.llm | StrOutputParser())
        summary = summarize_images_chain.invoke(image)
        return summary