  CREATE TABLE DWH_PUBLIC.SERVICE_JOBS_COMPLETE_LIST (
	ACTIVITY_ID VARCHAR2(10), 
	SERVICE_PARTNER_AREA VARCHAR2(10), 
	AREA_MANAGER_CODE VARCHAR2(16), 
	AREA_MANAGER_NAME VARCHAR2(100), 
	ACTIVITY_TYPE CHAR(1), 
	ACTIVITY_STATUS CHAR(1), 
	ACTIVITY_YEAR NUMBER, 
	ACTIVITY_NUMBER NUMBER, 
	CALL_DATE VARCHAR2(10), 
	ENGINEER CHAR(3), 
	ACTIVITY_NOTE VARCHAR2(1000), 
	CUSTOMER_PO_NUMBER CHAR(50), 
	CUSTOMER_REFERENCE_PERSON VARCHAR2(120), 
	CUSTOMER_COMPLAIN VARCHAR2(1000), 
	ENGINEER_ACTIVITY_DESCRIPTION VARCHAR2(1000), 
	ACTIVITY_INSERTION_USER CHAR(16), 
	START_ACTIVITY_DATE VARCHAR2(10), 
	END_ACTIVITY_DATE VARCHAR2(10), 
	CONTRACT_NUM CHAR(10), 
	CONTRACT_WO_NUMBER CHAR(10), 
	CONTRACT_WO_DATE DATE, 
	CONTRACT_SWO_NUMBER CHAR(10), 
	CONTRACT_SWO_DATE DATE, 
	SERVICE_PARTNER_CODE CHAR(3), 
	SERVICE_PARTNER_APPROVAL_DATE DATE, 
	REQUEST_INVOICE_DATE DATE, 
	WARRANTY_CODE_LABOUR CHAR(2), 
	WARRANTY_CODE_SPARES CHAR(2), 
	MACHINE_ERROR_CODE CHAR(10), 
	MACHINE_RUNNING_HOUR NUMBER, 
	ACTIVITY_INSERTION_DATE DATE, 
	ACTIVITY_PRIORITY CHAR(1), 
	CUSTOMER_REQUEST_DATE DATE, 
	MACHINE_YEAR_AGE NUMBER, 
	ENGINEER_AREA VARCHAR2(10), 
	MACHINE_PRODUCT_LINE VARCHAR2(2), 
	BRAND VARCHAR2(3), 
	COMMISSIONING_TYPE CHAR(1), 
	MAINTENANCE_CONTRACT_YEAR NUMBER, 
	MAINTENANCE_CONTRACT_NUMBER NUMBER, 
	MAINTENANCE_CONTRACT_SUB_NUMBER NUMBER, 
	MAINTENANCE_CONTRACT_TYPE VARCHAR2(3), 
	CUSTOMER_CONTACT_NAME VARCHAR2(360), 
	CUSTOMER_CONTACT_MOBILE VARCHAR2(80), 
	CUSTOMER_CONTACT_PHONE VARCHAR2(80), 
	CUSTOMER_CONTACT_EMAIL VARCHAR2(360), 
	SERVICE_PARTNER_NAME VARCHAR2(400), 
	SITE_CODE CHAR(10), 
	SITE_COST_CENTER VARCHAR2(20), 
	SERVICE_SITE_MOBILE VARCHAR2(80), 
	SERVICE_SITE_EMAIL VARCHAR2(360), 
	SERVICE_SITE_PHONE VARCHAR2(80), 
	SERVICE_SITE_NOTE VARCHAR2(360), 
	SERVICE_SITE_INTERNAL_NOTE VARCHAR2(360), 
	SERVICE_SITE_KEY CHAR(40), 
	CUSTOMER_CODE CHAR(10), 
	CUSTOMER_CODE_EXT CHAR(20), 
	CUSTOMER_MOBILE VARCHAR2(80), 
	CUSTOMER_PHONE VARCHAR2(80), 
	INVOICE_CUSTOMER_CODE CHAR(10), 
	INVOICE_CUSTOMER_EXTERNAL_CODE CHAR(20), 
	INVOICE_CUSTOMER_MOBILE VARCHAR2(80), 
	INVOICE_CUSTOMER_PHONE VARCHAR2(80), 
	MACHINE_ID NUMBER, 
	MACHINE_PRODUCT_CODE VARCHAR2(15), 
	MACHINE_SERIAL_NUMBER VARCHAR2(20), 
	MACHINE_QC_NUMBER VARCHAR2(20), 
	MACHINE_MODEL VARCHAR2(120), 
	CUSTOMER_ORDER_NUMBER VARCHAR2(13), 
	MACHINE_END_WARRANTY_SPARES_DATE DATE, 
	MACHINE_END_WARRANTY_ACTIVITIES DATE, 
	MACHINE_INSTALLATION_DATE DATE, 
	MACHINE_PHYSICAL_POSITION VARCHAR2(50), 
	MACHINE_PHYSICAL_AREA VARCHAR2(10), 
	MACHINE_NOTE VARCHAR2(360), 
	QES_ACTION_CODE VARCHAR2(3), 
	ENGINEER_NAME VARCHAR2(360), 
	MACHINE_FACTORY_CLASSIFICATION_CODE VARCHAR2(43), 
	MACHINE_FACTORY_CLASSIFICATION_DESCRIPTION VARCHAR2(403), 
	MACHINE_PM_CLASSIFICATION_CODE VARCHAR2(32), 
	MACHINE_PM_CLASSIFICATION_DESCRIPTION VARCHAR2(302), 
	MACHINE_CLASS_AND_SUBCLASS_CLASSIFICATION_CODE VARCHAR2(21), 
	MACHINE_CLASS_AND_SUBCLASS_CLASSIFICATION_DESCRIPTION VARCHAR2(201), 
	MACHINE_PNC VARCHAR2(20), 
	MACHINE_FACTORY_MODEL VARCHAR2(120), 
	TOTAL_COST_SPARES NUMBER, 
	TOTAL_COST_LABOUR NUMBER, 
	TOTAL_REVENUE_SPARES NUMBER, 
	TOTAL_REVENUE_LABOUR NUMBER, 
	CONTACT_TITLE VARCHAR2(100), 
	APPROVED_COUNTER NUMBER, 
	NOTAPPROVED_COUNTER VARCHAR2(1), 
	LAST_APPROVER_NAME VARCHAR2(16), 
	SITE_NAME VARCHAR2(481), 
	SITE_ADDRESS VARCHAR2(722), 
	SITE_CITY VARCHAR2(400), 
	SITE_ZIP VARCHAR2(40), 
	SITE_DISTRICT VARCHAR2(16), 
	SITE_COUNTRY VARCHAR2(4), 
	CUSTOMER_NAME VARCHAR2(481), 
	CUSTOMER_ADDRESS VARCHAR2(722), 
	CUSTOMER_CITY VARCHAR2(400), 
	CUSTOMER_DISTRICT VARCHAR2(16), 
	CUSTOMER_COUNTRY VARCHAR2(4), 
	INVOICE_CUSTOMER_NAME VARCHAR2(481), 
	INVOICE_CUSTOMER_ADDRESS VARCHAR2(722), 
	INVOICE_CUSTOMER_CITY VARCHAR2(400), 
	INVOICE_CUSTOMER_DISTRICT VARCHAR2(16), 
	INVOICE_CUSTOMER_COUNTRY VARCHAR2(4), 
	CONTACT_REFERENCE_NAME VARCHAR2(360), 
	CONTACT_REFERENCE_MOBILE VARCHAR2(80), 
	CONTACT_REFERENCE_EMAIL VARCHAR2(360), 
	CONTACT_REFERENCE_ADDRESS VARCHAR2(360), 
	CONTACT_REFERENCE_CITY VARCHAR2(360), 
	CONTACT_REFERENCE_ZIP_CODE VARCHAR2(40), 
	SPARE_CODE VARCHAR2(15), 
	SPARE_QTY NUMBER, 
	SPARE_COST_GROSS_UNIT NUMBER, 
	SPARE_COST_GROSS_TOTAL NUMBER, 
	SPARE_COST_NET_UNIT NUMBER, 
	SPARE_COST_NET_TOTAL NUMBER, 
	SPARE_REVENUE_GROSS_UNIT NUMBER, 
	SPARE_REVENUE_GROSS_TOTAL NUMBER, 
	SPARE_REVENUE_NET_UNIT NUMBER, 
	SPARE_REVENUE_NET_TOTAL NUMBER, 
	SPARE_STK3_UNIT NUMBER, 
	SPARE_STK3_TOTAL NUMBER, 
	SPARE_WARRANTY_CODE VARCHAR2(2), 
	SPARE_WARRANTY_DESCRIPTION VARCHAR2(100), 
	SPARE_WAREHOUSE VARCHAR2(3), 
	SPARE_SENT_TO_OTC_DATE DATE, 
	ACTIVITY_COUNTRY CHAR(10), 
	ACTIVITY_TYPE_DESCRIPTION VARCHAR2(100), 
	ACTIVITY_STATUS_DESCRIPTION VARCHAR2(100), 
	CURRENT_APPROVER VARCHAR2(16), 
	PTP_DATE DATE
);