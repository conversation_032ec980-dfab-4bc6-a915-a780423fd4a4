"""added column on sync documentation to store markdown document

Revision ID: f45c75922a78
Revises: 62b5c1523213
Create Date: 2025-02-05 12:39:49.418769

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'f45c75922a78'
down_revision = '62b5c1523213'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('sync_documentation', schema=None) as batch_op:
        batch_op.add_column(sa.Column('document_intelligence_result_json', sa.LargeBinary(), nullable=True)),
        batch_op.add_column(sa.Column('document_updated', sa.DATETIME(), autoincrement=False, nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('sync_documentation', schema=None) as batch_op:
        batch_op.drop_column('document_updated')
        batch_op.drop_column('document_intelligence_result_json')

    # ### end Alembic commands ###