"""question history columns fix

Revision ID: 265f3252a925
Revises: 1a1fff0955bc
Create Date: 2024-05-21 22:31:34.991962

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '265f3252a925'
down_revision = '1a1fff0955bc'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('question_history', schema=None) as batch_op:
        batch_op.add_column(sa.Column('conversation_id', sa.String(length=100), nullable=True))
        batch_op.add_column(sa.Column('dialog_id', sa.String(length=100), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('question_history', schema=None) as batch_op:
        batch_op.drop_column('dialog_id')
        batch_op.drop_column('conversation_id')

    # ### end Alembic commands ###
