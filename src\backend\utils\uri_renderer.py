import urllib.parse
from typing import Callable
from sqlalchemy import and_, or_
from src.backend.models import TBPU_Common
from utils.core import get_logger
from utils.clients import DatabaseClient

logger = get_logger(__file__)

def fetch_host_from_ptp(company_code: str):

    host_record = TBPU_Common.query.filter(and_(TBPU_Common.TABLE == "PU_DRAWING_SVR", TBPU_Common.CODE == company_code)).all()
    host = host_record[0][4]
    return host

def render_pride_uri_file_name(row) -> str:
    return f'<a href="/download-file?path=${urllib.parse.quote(row["FILE_PATH"])}">{row["FILE_PATH"]}</a>'

def render_tech_drawings(row):
    try:
        #host = fetch_host_from_ptp(row["COMPANY_CODE"])
        db_client = DatabaseClient()
        plant_code_row = db_client.execute(f"SELECT PLANT_CODE FROM DWH_PUBLIC.ITEM_MASTER WHERE TECH_DRAWING = '{row["TECH_DRAWING"]}'")
        plant_code = plant_code_row[1][0]

        tech_drawing_prefix = "RR" if plant_code == '02' else ''

        return f'<a href="/download-ptp?{f'prefix={tech_drawing_prefix}' if tech_drawing_prefix != '' else ''}&drawing={row["TECH_DRAWING"]}">{row["TECH_DRAWING"]}</a>'

    except Exception as e:
        logger.error("Error in rendering link")

uri_renderers: dict[str, Callable[[], str]] = {
    "PRIDE_FILE_NAME_RENDERER" : render_pride_uri_file_name,
    "PTP_TECH_DRAWING_RENDERER": render_tech_drawings
}

def get_uri_renderers(renderer_function_name: str):
    renderer = uri_renderers.get(renderer_function_name)
    return renderer

