/* @use postcss-nested; */

:root {
  --send-bg: var(--epr-flower-blue);
  --send-color: white;
  --receive-bg: var(--epr-magnolia);
  --receive-text: var(--epr-blue);
  --page-background: white;
}

.chat-box {
    font-size: 20px;
    font-weight: normal;
    min-height: 30px;
    padding: 30px;
    display: flex;
    flex-direction: column;
    background-color: var(--page-background);
}

div.bubble {
    max-width: 438px;
    word-wrap: break-word;
    margin-bottom: 12px;
    line-height: 24px;
    position: relative;
	padding: 10px 20px;
    border-radius: 25px;
  
    &:before, &:after {
        content: "";
        position: absolute;
        bottom: 0;
        height: 25px;
    }
}

.send {
	color: var(--send-color); 
	background: var(--send-bg);
	align-self: flex-end;
		
	&:before {
		right: -7px;
        width: 20px;
        background-color: var(--send-bg);
		border-bottom-left-radius: 16px 14px;
	}

	&:after {
		right: -26px;
        width: 26px;
        background-color: var(--page-background);
		border-bottom-left-radius: 10px;
	}
}
.receive {
    background: var(--receive-bg);
    color: var(--receive-text);
    align-self: flex-start;
        
    &:before {
        left: -7px;
        width: 20px;
        background-color: var(--receive-bg);
        border-bottom-right-radius: 16px 14px;
    }

    &:after {
        left: -26px;
        width: 26px;
        background-color: var(--page-background);
        border-bottom-right-radius: 10px;
    }
}

.jumping-dots span {
    position: relative;
    margin-left: auto;
    margin-right: auto;
    animation: jump 1s infinite;
    display: inline-block;
}

.jumping-dots .dotsend {
    background-color: #E5E5EA;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 3px;
}
.jumping-dots .dotreceive {
    background-color: #0B93F6;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 3px;
}
.jumping-dots .dot-1 {
    animation-delay: 200ms;
}

.jumping-dots .dot-2 {
    animation-delay: 400ms;
}

.jumping-dots .dot-3 {
    animation-delay: 600ms;
}

@keyframes expand-bounce {
    0%   { transform: scale(0); } 
    50%  { transform: scale(1.25); } 
    100% { transform: scale(1); }
}

div.bubble::before .send::before .receive::before {
    content: "";
    display: block;
    width: 0;
    position: absolute;
    bottom: -25px;
    left: 5px;
    transform: rotate(10deg);
}

@keyframes jump {
    0% { bottom: 0px; } 20% { bottom: 5px; } 40% { bottom: 0px; }
}
