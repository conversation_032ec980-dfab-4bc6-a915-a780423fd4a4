"""table for sync pride documentation processing

Revision ID: d161c66234c9
Revises: 12adc830c3ae
Create Date: 2024-10-02 09:59:39.909089

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'd161c66234c9'
down_revision = '12adc830c3ae'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('sync_documentation',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('doc_name', sa.String(length=1000), nullable=True),
    sa.Column('status', sa.String(length=1000), nullable=True),
    sa.Column('created', sa.DateTime(), nullable=False),
    sa.Column('updated', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    
    op.execute("ALTER TABLE sync_documentation ADD CONSTRAINT status CHECK (status IN ('Elab', 'OK'))")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('sync_documentation')
    # ### end Alembic commands ###
