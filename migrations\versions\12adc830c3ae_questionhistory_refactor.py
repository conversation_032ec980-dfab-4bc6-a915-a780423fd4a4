"""QuestionHistory refactor

Revision ID: 12adc830c3ae
Revises: 6f449162ab43
Create Date: 2024-05-28 00:08:05.675936

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '12adc830c3ae'
down_revision = '6f449162ab43'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('question_history', schema=None) as batch_op:
        batch_op.add_column(sa.Column('response', sa.JSON(), nullable=True))
        batch_op.drop_column('answer')

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('question_history', schema=None) as batch_op:
        batch_op.add_column(sa.Column('answer', sa.NVARCHAR(collation='SQL_Latin1_General_CP1_CI_AS'), autoincrement=False, nullable=True))
        batch_op.drop_column('response')

    # ### end Alembic commands ###
