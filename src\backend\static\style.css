/* finally, import Bootstrap */
:root {
    --epr-blue: #14133B;
    --epr-gold: #cea941;
    --epr-spartan-blue: #7b8a9c;
    --epr-mushroom-forest: #8d8060;
    --epr-trailblazer: #bfb08f;
    --epr-cotton-seed: #bfbab0;
    --epr-fog: #d9d5d2;
    --epr-danger: #ee6a1f;
    --epr-secondary: #e1edf4;

    --epr-green: #00C89A;
    --epr-flower-blue: #433D6B;
    --epr-magnolia: #F4ECFF;

    /* New color palette from design specs */
    --corporate-primary: #1E1F41;
    --corporate-secondary: #7B869C;
    --corporate-light: #DFE7EA;
    --golden-primary: #7D653F;
    --golden-secondary: #9E8664;
    --golden-light: #BFAF8F;
    --warm-grey-primary: #85827A;
    --warm-grey-secondary: #BFBAB0;
    --warm-grey-light: #DFD5D2;
    --deep-teal-primary: #24639F;
    --deep-teal-secondary: #526D70;
    --deep-teal-light: #BCD4D2;
    --sustainability-primary: #6E826F;
    --sustainability-secondary: #A8B580;
    --sustainability-light: #C4D69A;
}

.typewriter {
    font-size: 2.5rem;
    overflow: hidden;
    margin-top: 6%;
    border-right: .15em solid orange;
    white-space: nowrap;
    width: 0;
    animation:
        typing 2s steps(30, end) forwards,
        blink .75s infinite;
    animation-delay: 5.5s;
}

@keyframes typing {
    from {
        width: 0
    }

    to {
        width: 100%
    }
}

@keyframes blink {
    from {
        border-color: transparent
    }

    to {
        border-color: orange;
    }
}


main {
    padding-bottom: 76px; /*footer height */
}

main>.container {
    padding: 60px 15px 0;
}

.bg-epr {
    --bs-bg-opacity: 1;
    background-color: var(--epr-blue) !important;
}

.btn-primary {
    background-color: var(--epr-blue) !important;
    border-color: var(--epr-blue) !important;
    transition: opacity 0.2s ease-in-out;
}

.btn-primary:hover {
    opacity: 0.76;
}

.btn-warning {
    background-color: var(--epr-gold) !important;
    border-color: var(--epr-gold) !important;
    transition: opacity 0.2s ease-in-out;
    color: white !important;
}

.btn-warning:hover {
    opacity: 0.76;
}

.btn-secondary {
    background-color: var(--epr-secondary) !important;
    border-color: var(--epr-secondary) !important;
    transition: opacity 0.2s ease-in-out;
    color: var(--epr-blue);
}

.btn-secondary:hover {
    opacity: 0.76;
}

.btn-danger {
    background-color: var(--epr-danger) !important;
    border-color: var(--epr-danger) !important;
    transition: opacity 0.2s ease-in-out;
}

.btn-danger:hover {
    opacity: 0.76;
}

.active>.page-link,
.page-link.active {
    background-color: var(--epr-blue);
    border-color: var(--epr-blue);
}

.page-link {
    color: var(--epr-blue);
}

.page-link:hover {
    color: var(--epr-blue);
}

/* Translation Tool Styles */
.translation-container {
    background: transparent;
    min-height: calc(100vh - 140px);
    padding: 2rem 0;
}

.translation-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(30, 31, 65, 0.1);
    border: 1px solid rgba(30, 31, 65, 0.08);
    overflow: hidden;
}

.card-header-custom {
    background: linear-gradient(135deg, var(--corporate-primary) 0%, var(--deep-teal-primary) 100%);
    color: white;
    padding: 2rem;
    border: none;
}

.upload-area {
    border: 2px dashed var(--deep-teal-secondary);
    border-radius: 12px;
    padding: 3rem 2rem;
    text-align: center;
    background: linear-gradient(135deg, rgba(36, 99, 159, 0.05) 0%, rgba(82, 109, 112, 0.05) 100%);
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-area:hover {
    border-color: var(--deep-teal-primary);
    background: linear-gradient(135deg, rgba(36, 99, 159, 0.1) 0%, rgba(82, 109, 112, 0.1) 100%);
    transform: translateY(-2px);
}

.upload-area.dragover {
    border-color: var(--sustainability-primary);
    background: linear-gradient(135deg, rgba(110, 130, 111, 0.1) 0%, rgba(168, 181, 128, 0.1) 100%);
}

.upload-icon {
    font-size: 3rem;
    color: var(--deep-teal-primary);
    margin-bottom: 1rem;
}

.file-info {
    background: linear-gradient(135deg, rgba(110, 130, 111, 0.1) 0%, rgba(196, 214, 154, 0.1) 100%);
    border: 1px solid var(--sustainability-light);
    border-radius: 8px;
    padding: 1rem;
    margin-top: 1rem;
}

.language-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin: 1.5rem 0;
}

.form-select-custom {
    border: 2px solid var(--warm-grey-secondary);
    border-radius: 8px;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-select-custom:focus {
    border-color: var(--deep-teal-primary);
    box-shadow: 0 0 0 0.2rem rgba(36, 99, 159, 0.25);
}

.excel-options {
    background: linear-gradient(135deg, rgba(125, 102, 63, 0.05) 0%, rgba(191, 175, 143, 0.05) 100%);
    border: 1px solid var(--golden-light);
    border-radius: 12px;
    padding: 1.5rem;
    margin-top: 1.5rem;
}

.column-checkbox {
    margin: 0.5rem 0;
}

.column-checkbox input[type="checkbox"] {
    margin-right: 0.5rem;
    transform: scale(1.2);
}

.btn-translate {
    background: linear-gradient(135deg, var(--sustainability-primary) 0%, var(--sustainability-secondary) 100%);
    border: none;
    color: white;
    padding: 0.75rem 2rem;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 16px rgba(110, 130, 111, 0.3);
}

.btn-translate:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(110, 130, 111, 0.4);
    background: linear-gradient(135deg, var(--sustainability-secondary) 0%, var(--sustainability-light) 100%);
}

.btn-translate:disabled {
    background: var(--warm-grey-secondary);
    box-shadow: none;
    cursor: not-allowed;
}

.progress-container {
    margin-top: 2rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, rgba(36, 99, 159, 0.05) 0%, rgba(82, 109, 112, 0.05) 100%);
    border-radius: 12px;
    border: 1px solid var(--deep-teal-light);
}

.progress-custom {
    height: 8px;
    border-radius: 4px;
    background-color: var(--warm-grey-light);
}

.progress-bar-custom {
    background: linear-gradient(90deg, var(--deep-teal-primary) 0%, var(--sustainability-primary) 100%);
    border-radius: 4px;
    transition: width 0.3s ease;
}

.status-text {
    color: var(--corporate-primary);
    font-weight: 500;
    margin-top: 0.5rem;
}

/* Version display styles */
#runningEnvDisplay {
    font-size: 0.75rem;
    font-weight: bold;
    letter-spacing: 0.5px;
}

#runningEnvDisplay.bg-secondary {
    background-color: var(--epr-spartan-blue) !important;
}

#versionDisplay {
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.8) !important;
    transition: color 0.2s ease;
}

#versionDisplay:hover {
    color: white !important;
    text-decoration: underline !important;
}

/* Environment-specific colors */
#runningEnvDisplay[data-env="LOCAL"] {
    background-color: var(--epr-green) !important;
}

#runningEnvDisplay[data-env="DEVEL"] {
    background-color: var(--epr-gold) !important;
    color: var(--epr-blue) !important;
}

#runningEnvDisplay[data-env="STAGING"] {
    background-color: var(--epr-danger) !important;
}

#runningEnvDisplay[data-env=""] {
    background-color: var(--epr-blue) !important;
}

/* Translator-specific styles */
.translation-container #runningEnvDisplay {
    position: fixed;
    top: 10px;
    right: 10px;
    z-index: 1050;
}

@media (max-width: 768px) {
    .language-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .translation-container {
        padding: 1rem 0;
    }

    .card-header-custom {
        padding: 1.5rem;
    }

    .upload-area {
        padding: 2rem 1rem;
    }
}