/* finally, import Bootstrap */
:root {
    --epr-blue: #14133B;
    --epr-gold: #cea941;
    --epr-spartan-blue: #7b8a9c;
    --epr-mushroom-forest: #8d8060;
    --epr-trailblazer: #bfb08f;
    --epr-cotton-seed: #bfbab0;
    --epr-fog: #d9d5d2;
    --epr-danger: #ee6a1f;
    --epr-secondary: #e1edf4;

    --epr-green: #00C89A;
    --epr-flower-blue: #433D6B;
    --epr-magnolia: #F4ECFF;
}

.typewriter {
    font-size: 2.5rem;
    overflow: hidden;
    margin-top: 6%;
    border-right: .15em solid orange;
    white-space: nowrap;
    width: 0;
    animation:
        typing 2s steps(30, end) forwards,
        blink .75s infinite;
    animation-delay: 5.5s;
}

@keyframes typing {
    from {
        width: 0
    }

    to {
        width: 100%
    }
}

@keyframes blink {
    from {
        border-color: transparent
    }

    to {
        border-color: orange;
    }
}


main {
    padding-bottom: 76px; /*footer height */
}

main>.container {
    padding: 60px 15px 0;
}

.bg-epr {
    --bs-bg-opacity: 1;
    background-color: var(--epr-blue) !important;
}

.btn-primary {
    background-color: var(--epr-blue) !important;
    border-color: var(--epr-blue) !important;
    transition: opacity 0.2s ease-in-out;
}

.btn-primary:hover {
    opacity: 0.76;
}

.btn-warning {
    background-color: var(--epr-gold) !important;
    border-color: var(--epr-gold) !important;
    transition: opacity 0.2s ease-in-out;
    color: white !important;
}

.btn-warning:hover {
    opacity: 0.76;
}

.btn-secondary {
    background-color: var(--epr-secondary) !important;
    border-color: var(--epr-secondary) !important;
    transition: opacity 0.2s ease-in-out;
    color: var(--epr-blue);
}

.btn-secondary:hover {
    opacity: 0.76;
}

.btn-danger {
    background-color: var(--epr-danger) !important;
    border-color: var(--epr-danger) !important;
    transition: opacity 0.2s ease-in-out;
}

.btn-danger:hover {
    opacity: 0.76;
}

.active>.page-link,
.page-link.active {
    background-color: var(--epr-blue);
    border-color: var(--epr-blue);
}

.page-link {
    color: var(--epr-blue);
}

.page-link:hover {
    color: var(--epr-blue);
}