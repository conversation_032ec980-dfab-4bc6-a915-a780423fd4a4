from os import walk
from os.path import join
from pathlib import Path
from typing import List
from tqdm import tqdm
from tqdm.contrib.logging import logging_redirect_tqdm
from sqlalchemy import desc, asc, select
from src.backend.contracts.chat_data import BotType
from src.backend.models import SyncDocumentation
from config.config import RAGConfig
from utils.core import SourceType, get_logger
from utils.clients import DatabaseClient
from datetime import datetime
from src import db, app




SUPPORTED_EXTENSIONS = [
    ".pdf",
    ".jpeg",
    ".jpg",
    ".png",
    ".bmp",
    ".tiff",
    ".heif",
    ".docx",
    ".xlsx",
    ".pptx",
    ".html",
]
logger = get_logger(__file__)

# TODO - Abstract this function based on documents' source
def fetch_uris(origin: SourceType, bot_name: str) -> List[str]:
    config = RAGConfig()
    temporary_pdf = config.temporary_pdf
    
    if bot_name == BotType.JDAILO.name or bot_name == BotType.SEO_BOT.name:
        if origin == SourceType.LOCAL_FILE_SYSTEM:
            root_selected = config.jdanallo_local_root if bot_name == BotType.JDAILO.name else config.seobot_local_root
            root = (root_selected)
            return walk_folders(root)
        else:
            root_selected = config.how_to_bot_pride_products_root

def get_uris_from_db(root_selected: str,pride_sub_folder: List[str]) -> List[str]:
    
    pride_sub_folder = ["'"+element+"'" for element in pride_sub_folder]
    formatted_sub_folder_for_query = ",".join(pride_sub_folder)
    uris = []
    with app.app_context():
        document_processed = db.session.query(SyncDocumentation).filter(SyncDocumentation.document_type.in_(set([element.replace("'","") for element in pride_sub_folder]))).order_by(asc(SyncDocumentation.document_updated)).all()

    if len(document_processed) == 0:
        query = f"""SELECT DISTINCT FILE_PATH, DOCUMENT_DATE FROM DWH_PUBLIC.COMMERCIAL_PRODUCTS_DOCUMENTS WHERE DOCUMENT_TYPE IN ({formatted_sub_folder_for_query}) ORDER BY DOCUMENT_DATE"""    
    else:
        uris.extend([(root_selected + document.doc_name.replace('/', '\\'), document.document_updated.strftime("%Y-%m-%d %H:%M:%S")) for document in document_processed if document.status == 'Elab'])
        last_document_processed_date = document_processed[-1].document_updated
        documents_ok_processed_with_same_date = ",".join(["'"+document.doc_name.replace("'","''")+"'" for document in document_processed if document.status == 'OK' and document.document_updated == last_document_processed_date])
        if documents_ok_processed_with_same_date == "":
            query = f"""SELECT DISTINCT FILE_PATH, DOCUMENT_DATE FROM DWH_PUBLIC.COMMERCIAL_PRODUCTS_DOCUMENTS WHERE DOCUMENT_TYPE IN ({formatted_sub_folder_for_query}) AND DOCUMENT_DATE >= TO_DATE('{last_document_processed_date}', 'YYYY-MM-DD HH24:MI:SS') ORDER BY DOCUMENT_DATE"""
        else:
            query = f"""SELECT DISTINCT FILE_PATH, DOCUMENT_DATE FROM DWH_PUBLIC.COMMERCIAL_PRODUCTS_DOCUMENTS WHERE DOCUMENT_TYPE IN ({formatted_sub_folder_for_query}) AND DOCUMENT_DATE >= TO_DATE('{last_document_processed_date}', 'YYYY-MM-DD HH24:MI:SS') AND FILE_PATH NOT IN ({documents_ok_processed_with_same_date}) ORDER BY DOCUMENT_DATE"""

    
    db_client = DatabaseClient()
    files = db_client.execute(query, numrows=None)
    
    files = files[1:] #remove columns from the result
    files = [(root_selected + file[0].replace('/', '\\'), file[1].strftime("%Y-%m-%d %H:%M:%S")) if isinstance(file[1], datetime) else datetime.now().strftime("%Y-%m-%d %H:%M:%S") for file in files]
    uris.extend(files)

    db_client.shutdown()
    return uris

def walk_folders(root: str ):

    file_urls = []
    files_to_convert_in_pdf = []
    for current_dir, dirs, files in walk(root):
        with logging_redirect_tqdm():
            for file in tqdm(files, desc=f"Processing folder {current_dir}..."):
                logger.info(f"Processing file {file}")
                file_path = join(current_dir, file)

                if Path(file_path).suffix in SUPPORTED_EXTENSIONS:
                    file_urls.append((file_path, datetime.now().strftime('%d/%m/%Y, %H:%M:%S')))
    return file_urls

