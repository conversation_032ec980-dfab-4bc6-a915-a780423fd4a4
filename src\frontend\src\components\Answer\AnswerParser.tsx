import { renderToStaticMarkup } from "react-dom/server";
import { ApproachType, ChatResponse, getCitationFilePath } from "../../api";

export type HtmlParsedAnswer = {
    answerHtml: string;
    citations: string[];
    followupQuestions: string[];
};

export function parseAnswerToHtml(chatResponse: ChatResponse, onCitationClicked: (citationFilePath: string) => void): HtmlParsedAnswer {
    const citations: string[] = [];
    const followupQuestions: string[] = [];

    // Extract any follow-up questions that might be in the answer
    let parsedAnswer = chatResponse.answer.formatted_answer.replace(/<<<([^>>>]+)>>>/g, (match, content) => {
        followupQuestions.push(content);
        return "";
    });

    // trim any whitespace from the end of the answer after removing follow-up questions
    parsedAnswer = parsedAnswer.trim();

    var fragments: string[] = [];
    const parts = chatResponse.classification == ApproachType.Unstructured ? parsedAnswer.split(/\{([^}]+)\}/g) : [parsedAnswer];
    fragments = parts.map((part, index) => {
        if (index % 2 === 0) {
            return part;
        } else {
            // Extract citations from answer
            let citationIndex: number;
            if (citations.indexOf(part) !== -1) {
                citationIndex = citations.indexOf(part) + 1;
            } else {
                citations.push(part);
                citationIndex = citations.length;
            }

            const path = getCitationFilePath(part);

            return renderToStaticMarkup(
                <a className="supContainer" title={part} onClick={() => onCitationClicked(path)}>
                    <sup>{citationIndex}</sup>
                </a>
            );
        }
    });

    

    return {
        answerHtml: fragments.join(""),
        citations,
        followupQuestions
    };
}

export async function* parseStreamAnswerToHtml(chatResponse: any) {
    
    const decoder =  new TextDecoder();
    let buffer: string = '';
    let isFirstMessage = true
    let firstMessageComplete = false
    try{
        while(true){
            
            const { done, value } = await chatResponse.read();
            if (done) break;

            // Decode the chunk and add it to our buffer
            buffer += decoder.decode(value, { stream: true });
            

            // For the first message, wait until we have a complete message
            if (isFirstMessage) {
               
                if (!buffer?.includes('\n\n')) {
                    continue; // Keep accumulating data until we have a complete message
                    
                }
                // await new Promise(f => setTimeout(f, 1500));
                isFirstMessage = false;
            }

            // Split on double newlines which separate SSE messages
            const messages: string[] = buffer?.split('\n\n');
            
            // Keep the last chunk if it's incomplete (no double newline at the end)
            buffer = messages.pop() || '';

            for (const message of messages){
                if(message.trim()){
                    try {
                        const parsed: any = JSON.parse(message);
                        yield parsed;
                        await new Promise(r => setTimeout(r, 40));
                    } catch (e) {
                        console.error('Failed to parse message:', message, e);
                        if (isFirstMessage || !firstMessageComplete){
                            buffer = message + '\n\n' + buffer;
                            continue;
                        }
                    }
                    
                }
            }
        }

        if (buffer?.trim()){
            
            try {
                const parsed: any = JSON.parse(buffer);
                yield parsed;
            } catch (e) {
                console.error('Failed to parse final message:', buffer, e);
            }
        } 
        
    }finally {
        chatResponse.releaseLock();
    }
}
