import argparse
from json import dump
from os.path import exists, isfile
from re import sub


def create_dictionary(from_file: str, to_file: str) -> None:
    """
    Creates a JSON file enumerating the list of words contained in `from_file`.
    """
    assert isfile(from_file), f"Source file not a file. Path given:\n{from_file}"

    with open(from_file) as file:
        keywords = file.read().strip()
        keywords = sub(r"[ ;,:\n]+", " ", keywords)
        keywords = keywords.split(" ")
        keywords.sort()

    dictionary = {key: num for num, key in enumerate(keywords)}

    if not exists(to_file):
        dest = open(to_file, "x")
        dest.close()

    with open(to_file, "w") as file:
        dump(dictionary, file, indent=4)


def get_args() -> argparse.Namespace:
    """
    Acquires the arguments passed by the user. Returns a `Namespace` with two fields: `from_file` and `to_file`.
    """
    parser = argparse.ArgumentParser(
        description="Converts a list of words into a dictionary enumerating them."
    )
    parser.add_argument(
        "from_file", type=str, help="The file containing the list of words."
    )
    parser.add_argument(
        "to_file",
        type=str,
        help="The destination file, if not empty will be overwritten.",
    )
    return parser.parse_args()


if __name__ == "__main__":
    args = get_args()

    create_dictionary(args.from_file, args.to_file)
