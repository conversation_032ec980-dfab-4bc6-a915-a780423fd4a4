<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Document Translation Tool</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .main-content {
            padding: 40px;
        }

        .upload-section {
            border: 3px dashed #e0e6ed;
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            margin-bottom: 30px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-section:hover {
            border-color: #667eea;
            background-color: #f8f9ff;
        }

        .upload-section.dragover {
            border-color: #667eea;
            background-color: #f0f4ff;
        }

        .upload-icon {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 20px;
        }

        .upload-text {
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 10px;
        }

        .upload-subtext {
            color: #999;
            font-size: 0.9rem;
        }

        .file-input {
            display: none;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e0e6ed;
            border-radius: 10px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
        }

        .language-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .excel-options {
            display: none;
            background: #f8f9ff;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
        }

        .excel-options h3 {
            color: #333;
            margin-bottom: 15px;
        }

        .column-selection {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            padding: 8px;
            background: white;
            border-radius: 5px;
            border: 1px solid #e0e6ed;
        }

        .checkbox-group input[type="checkbox"] {
            margin-right: 8px;
            transform: scale(1.2);
        }

        .translate-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 40px;
            font-size: 1.1rem;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 20px;
        }

        .translate-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .translate-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .progress-section {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e0e6ed;
            border-radius: 4px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s ease;
        }

        .file-info {
            display: none;
            background: #e8f5e8;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            border-left: 4px solid #4caf50;
        }

        .file-info h4 {
            color: #2e7d32;
            margin-bottom: 5px;
        }

        .file-info p {
            color: #388e3c;
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .language-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .main-content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌐 AI Document Translator</h1>
            <p>Upload Excel, PowerPoint, or Word files and translate them instantly</p>
        </div>

        <div class="main-content">
            <div class="upload-section" id="uploadSection">
                <div class="upload-icon">📁</div>
                <div class="upload-text">Drop your files here or click to browse</div>
                <div class="upload-subtext">Supports .xlsx, .pptx, .docx files (Max 50MB)</div>
                <input type="file" id="fileInput" class="file-input" accept=".xlsx,.pptx,.docx" multiple>
            </div>

            <div class="file-info" id="fileInfo">
                <h4 id="fileName">Selected File</h4>
                <p id="fileDetails">File details will appear here</p>
            </div>

            <div class="language-grid">
                <div class="form-group">
                    <label for="sourceLanguage">Source Language</label>
                    <select id="sourceLanguage" class="form-control">
                        <option value="auto">Auto-detect</option>
                        <option value="en">English</option>
                        <option value="es">Spanish</option>
                        <option value="fr">French</option>
                        <option value="de">German</option>
                        <option value="it">Italian</option>
                        <option value="pt">Portuguese</option>
                        <option value="ru">Russian</option>
                        <option value="ja">Japanese</option>
                        <option value="ko">Korean</option>
                        <option value="zh">Chinese (Simplified)</option>
                        <option value="ar">Arabic</option>
                        <option value="hi">Hindi</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="targetLanguage">Target Language</label>
                    <select id="targetLanguage" class="form-control">
                        <option value="">Select target language</option>
                        <option value="en">English</option>
                        <option value="es">Spanish</option>
                        <option value="fr">French</option>
                        <option value="de">German</option>
                        <option value="it">Italian</option>
                        <option value="pt">Portuguese</option>
                        <option value="ru">Russian</option>
                        <option value="ja">Japanese</option>
                        <option value="ko">Korean</option>
                        <option value="zh">Chinese (Simplified)</option>
                        <option value="ar">Arabic</option>
                        <option value="hi">Hindi</option>
                    </select>
                </div>
            </div>

            <div class="excel-options" id="excelOptions">
                <h3>📊 Excel Translation Options</h3>
                <p>Select which columns you want to translate:</p>
                <div class="column-selection" id="columnSelection">
                    <!-- Columns will be populated dynamically -->
                </div>
            </div>

            <button class="translate-btn" id="translateBtn" disabled>
                🚀 Start Translation
            </button>

            <div class="progress-section" id="progressSection">
                <h3>Translating your document...</h3>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <p id="progressText">Preparing translation...</p>
            </div>
        </div>
    </div>

    <script>
        const uploadSection = document.getElementById('uploadSection');
        const fileInput = document.getElementById('fileInput');
        const fileInfo = document.getElementById('fileInfo');
        const fileName = document.getElementById('fileName');
        const fileDetails = document.getElementById('fileDetails');
        const excelOptions = document.getElementById('excelOptions');
        const columnSelection = document.getElementById('columnSelection');
        const translateBtn = document.getElementById('translateBtn');
        const progressSection = document.getElementById('progressSection');
        const progressFill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');
        const targetLanguage = document.getElementById('targetLanguage');

        let selectedFiles = [];
        let currentFileType = '';

        // Upload section click handler
        uploadSection.addEventListener('click', () => {
            fileInput.click();
        });

        // Drag and drop handlers
        uploadSection.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadSection.classList.add('dragover');
        });

        uploadSection.addEventListener('dragleave', () => {
            uploadSection.classList.remove('dragover');
        });

        uploadSection.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadSection.classList.remove('dragover');
            handleFiles(e.dataTransfer.files);
        });

        // File input change handler
        fileInput.addEventListener('change', (e) => {
            handleFiles(e.target.files);
        });

        // Target language change handler
        targetLanguage.addEventListener('change', updateTranslateButton);

        function handleFiles(files) {
            selectedFiles = Array.from(files);
            if (selectedFiles.length > 0) {
                const file = selectedFiles[0];
                currentFileType = getFileType(file.name);
                
                fileName.textContent = file.name;
                fileDetails.textContent = `${currentFileType.toUpperCase()} file • ${formatFileSize(file.size)}`;
                fileInfo.style.display = 'block';

                if (currentFileType === 'excel') {
                    showExcelOptions();
                } else {
                    hideExcelOptions();
                }

                updateTranslateButton();
            }
        }

        function getFileType(filename) {
            const extension = filename.split('.').pop().toLowerCase();
            switch (extension) {
                case 'xlsx': return 'excel';
                case 'pptx': return 'powerpoint';
                case 'docx': return 'word';
                default: return 'unknown';
            }
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function showExcelOptions() {
            excelOptions.style.display = 'block';
            // Simulate column detection
            const sampleColumns = ['Name', 'Description', 'Comments', 'Notes', 'Address', 'Title'];
            columnSelection.innerHTML = '';
            
            sampleColumns.forEach(column => {
                const checkboxGroup = document.createElement('div');
                checkboxGroup.className = 'checkbox-group';
                checkboxGroup.innerHTML = `
                    <input type="checkbox" id="col_${column}" value="${column}" checked>
                    <label for="col_${column}">${column}</label>
                `;
                columnSelection.appendChild(checkboxGroup);
            });
        }

        function hideExcelOptions() {
            excelOptions.style.display = 'none';
        }

        function updateTranslateButton() {
            const hasFile = selectedFiles.length > 0;
            const hasTargetLanguage = targetLanguage.value !== '';
            translateBtn.disabled = !(hasFile && hasTargetLanguage);
        }

        // Translation button click handler
        translateBtn.addEventListener('click', startTranslation);

        function startTranslation() {
            progressSection.style.display = 'block';
            translateBtn.disabled = true;
            
            // Simulate translation progress
            let progress = 0;
            const progressSteps = [
                'Analyzing document structure...',
                'Extracting text content...',
                'Translating content...',
                'Applying translations...',
                'Finalizing document...'
            ];
            
            const progressInterval = setInterval(() => {
                progress += Math.random() * 20;
                if (progress > 100) progress = 100;
                
                progressFill.style.width = progress + '%';
                
                const stepIndex = Math.floor((progress / 100) * progressSteps.length);
                if (stepIndex < progressSteps.length) {
                    progressText.textContent = progressSteps[stepIndex];
                }
                
                if (progress >= 100) {
                    clearInterval(progressInterval);
                    completeTranslation();
                }
            }, 500);
        }

        function completeTranslation() {
            progressText.textContent = 'Translation completed! Download will start shortly...';
            
            setTimeout(() => {
                // Simulate download
                alert('Translation completed! Your translated document would be downloaded now.');
                
                // Reset UI
                progressSection.style.display = 'none';
                translateBtn.disabled = false;
                progressFill.style.width = '0%';
            }, 2000);
        }
    </script>
</body>
</html>
