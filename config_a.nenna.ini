[rag.parameters]
embedding_resource = https://epr-openai-sandbox-plus.openai.azure.com/
embedding_model = embedding-test-rag

[txt2sql.oracle_oci]
oci_client_path = C:\oracle\instantclient_21_14
oci_dsn = dwhtest_high


[common]
run_mode = TEST
logout_url = https://electroluxprofessional.unily.com

[log]
loglevel = DEBUG
log_folder = .

[ad]
ad_schema_callback=http

[call_center_bot.rag.fetcher]
local_root = C:/Users/<USER>/Desktop/PROJECTS/ELETTROLUX PROFESSIONAL/compli-bot/pride_indexer/test/documents/Pride
pride_products_root = 

[how_to_bot.rag.fetcher]
local_root = C:/Users/<USER>/Desktop/PROJECTS/ELETTROLUX PROFESSIONAL/compli-bot/pride_indexer/test/documents/IT-learning
pride_products_root = 

[db]
db_driver = ODBC Driver 17 for SQL Server

[call_center_bot.azure_blob_storage]
account_url = https://epraisandboxsa.blob.core.windows.net/
account_name = epraisandboxsa
table_container = pride-tables-rag-dev
image_container = pride-images-rag-dev

[call_center_bot.rag.storage]
endpoint = https://epr-ai-srch-rag.search.windows.net
index_name = it-documents-pride-dev