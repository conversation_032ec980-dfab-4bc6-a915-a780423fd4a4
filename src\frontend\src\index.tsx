import React from "react";
import ReactDOM from "react-dom/client";
import { FluentProvider, webLightTheme, makeStyles } from '@fluentui/react-components';
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { initializeIcons } from "@fluentui/react";
import './utils/fetchInterceptor'; // Aggiungi questa riga

import "./index.css";

import Layout from "./pages/layout/Layout";
import NoPage from "./pages/NoPage";
import PageContainer from "./pages/PageContainer";
import MaintenancePage from "./pages/MaintenancePage";

initializeIcons();

const eprTheme = {
    ...webLightTheme,
    colorNeutralBackground1: "#f2f2f2",
};

export default function App() {
    return (
        <BrowserRouter>
            <Routes>
                <Route path="/" element={<Layout />}>
                    <Route index element={<PageContainer />} />
                    <Route path="/maintenance" element={<MaintenancePage />} />
                    <Route path="*" element={<NoPage />} />
                </Route>
            </Routes>
        </BrowserRouter>
    );
}

ReactDOM.createRoot(document.getElementById("root") as HTMLElement).render(
    <FluentProvider theme={webLightTheme}>
        <div style={{ minHeight: "100vh", backgroundColor: eprTheme.colorNeutralBackground1 }}>
            <App />
        </div>
    </FluentProvider>
);
