import pymupdf
import re
from utils.core import get_logger

logger = get_logger(__file__)

def image_extractor(source: str, image_polygons: list, output_image_path: str):
    document_images = []
    #TO DO manage different source file, now is only for local files 
    pdf_file = pymupdf.open(source)

    image_count = 1
    for page_number in range (0, pdf_file.page_count):
        
        images_in_page = [item for item in image_polygons if any(region["pageNumber"] == page_number + 1 for region in item["boundingRegions"])]

        if not images_in_page:
            continue
        
        page = pdf_file.load_page(page_number)
        
        for image in images_in_page:
            pixel_polygon = convert_normalized_to_pixel_coordinates(image["boundingRegions"][0]["polygon"])
            
            if int(image["boundingRegions"][0]["polygon"][1]) != int(image["boundingRegions"][0]["polygon"][3]):
                clip_rect = pymupdf.Rect(pixel_polygon[1][0], pixel_polygon[1][1], pixel_polygon[3][0], pixel_polygon[3][1])
            else:
                clip_rect = pymupdf.Rect(pixel_polygon[0][0], pixel_polygon[0][1], pixel_polygon[2][0], pixel_polygon[2][1])
                
            try:    
                image_name = output_image_path[:-4]+ f"_image_{image_count}.jpg"
                pix = page.get_pixmap(clip=clip_rect, dpi=300)  # clip=clip_rect
                image_byte = pix.tobytes("jpg")
                
                image_object = {'image_name': image_name, 'image': image_byte}
                if pix.height > 200:
                    document_images.append(image_object)
                    #pix.save(image_name)
                image_count+=1

                logger.info(f"Image {image_name} extracted")
            except Exception as e:
                image_count+=1
                logger.error(e)
                logger.error(f"Image {image_name} not uploaded")
    
    pdf_file.close()
    return document_images
    

    

def convert_normalized_to_pixel_coordinates(polygon: list[int]):
    pixel_polygon = []
    
    
    for i in range(0, len(polygon), 2):
        x = polygon[i] * 72
        y = polygon[i + 1] * 72 # pyMuPDF ha come orientamento dell'asse y dall'alto verso il basso, questo calcolo ottiene la coordinata per questo orientamento. 

        pixel_polygon.append((x, y))
    
    return pixel_polygon





def add_number_image(page_content: str):
    """Questa funzione serve per la migrazione alla nuova API version di Azure Document Intelligence, poichè le immagini
       nel contenuto markdown non riportano più la stringa "figures/12", necessaria per il mapping corretto delle immagini
       nel documento, vengono aggiunte manualmente per mantenere il precedente funzionamento di estrazione delle immagini. """
       
    # Pattern per catturare <figure> ... </figure> con qualsiasi contenuto arbitrario all'interno
    figure_pattern = re.compile(r'(<figure>.*?</figure>)', re.DOTALL)

    # Trova tutte le occorrenze di <figure> ... </figure>
    figure_tags = figure_pattern.findall(page_content)
    
    # Conta il numero di occorrenze
    count = len(figure_tags)
    
    # Sostituisce le occorrenze con il formato richiesto
    for i in range(count):
        # Aggiunge alla stringa ![]figures/n dove N è il numero dell'immagine nell'iterazione
        text_to_replace = figure_tags[i].replace("</figure>", f" ![]figures/{i} </figure>") #step necessario per mantenere il contenuto all'interno del tag e non sostituirlo del tutto
        
        page_content = page_content.replace(figure_tags[i], text_to_replace, 1 )
    
    return page_content