:root {
    --epr-blue: #14133B;
    --epr-gold: #cea941;
    --epr-green: #00C89A;
    --epr-flower-blue: #433D6B;
    --epr-magnolia: #F4ECFF;
    --epr-golden-element: #BFAF8F;
}

.layout {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.header {
    background-color: var(--epr-blue);
    color: #f2f2f2;
}

.headerContainer {
    display: flex;
    align-items: center;
    justify-content: space-around;
    margin-right: 12px;
    margin-left: 12px;
}

.headerTitleContainer {
    display: flex;
    align-items: center;
    margin-right: 40px;
    color: #f2f2f2;
    text-decoration: none;
}

.headerLogo {
    height: 40px;
}

.headerTitle {
    margin-left: 12px;
    font-weight: 600;
}

.headerNavList {
    display: flex;
    list-style: none;
    padding-left: 0;
}

.headerNavPageLink {
    color: #f2f2f2;
    text-decoration: none;
    opacity: 0.75;

    transition-timing-function: cubic-bezier(0.16, 1, 0.3, 1);
    transition-duration: 500ms;
    transition-property: opacity;
}

.headerNavPageLink:hover {
    opacity: 1;
}

.headerNavPageLinkActive {
    color: #f2f2f2;
    text-decoration: none;
}

.headerNavLeftMargin {
    margin-left: 20px;
}

.headerRightText {
    font-weight: normal;
    margin-left: 40px;
}

.microsoftLogo {
    height: 23px;
    font-weight: 600;
}

.eprLink {
    display: flex;
    align-items: center;
    text-decoration: none;
}

.eprLogo {
    height: 40px;
}

.eprText {
    margin-left: 12px;
    font-size: 12px;
    color: #f2f2f2;
}

.runningEnv {
    margin-left: 10px;
    font-weight: bold;
    color: var(--epr-gold);
    letter-spacing: 1px;
    font-size: 2rem;
}
