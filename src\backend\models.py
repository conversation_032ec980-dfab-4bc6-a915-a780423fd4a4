from flask_login import UserMixin
from sqlalchemy import Index
from sqlalchemy.orm import relationship, deferred
from src import db
from src.backend.contracts.user_settings import SearchSettings
from datetime import datetime, timezone
from sqlalchemy.orm import Mapped, mapped_column


class TimestampModel(db.Model):
    __abstract__ = True
    created: Mapped[datetime] = mapped_column(default=lambda: datetime.now(timezone.utc))
    updated: Mapped[datetime] = mapped_column(default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))


# Define User model
class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True) # primary keys are required by SQLAlchemy
    email = db.Column(db.String(100), unique=True)
    password = db.Column(db.String(100))
    name = db.<PERSON>umn(db.String(1000))
    surname = db.<PERSON>umn(db.String(1000))
    ad_data = db.Column(db.J<PERSON>N, nullable=True)
    user_settings = db.Column(db.JSON, nullable=True)

    roles = relationship("Role", secondary="user_roles", backref="users")

    def to_json(self):
        return {
            'id': self.id,
            'email': self.email,
            'username': self.email.split("@")[0],
            'name': self.name,
            'surname': self.surname,
            'ad_data': self.ad_data,
            "user_settings": self.user_settings,
            'roles': [role.to_json() for role in self.roles] if self.roles else []
        }

    def get_user_settings_json(self):
        if self.user_settings is None:
            return None

        search_settings_data = self.user_settings.get("search_settings")
        if search_settings_data is None:
            return None

        search_settings = SearchSettings(**search_settings_data)  # Unpack dict as arguments
        return search_settings.to_json()


# Define Role model
class Role(db.Model):
    id = db.Column(db.Integer(), primary_key=True)
    name = db.Column(db.String(50), unique=True)

    def to_json(self):
        return {
            'name': self.name,
        }


# Define UserRoles model
class UserRoles(db.Model):
    id = db.Column(db.Integer(), primary_key=True)
    user_id = db.Column(db.Integer(), db.ForeignKey('user.id', ondelete='CASCADE'))
    role_id = db.Column(db.Integer(), db.ForeignKey('role.id', ondelete='CASCADE'))


# Define SampleQuestions model
class SampleQuestions(db.Model):
    id = db.Column(db.Integer(), primary_key=True)
    question = db.Column(db.String(500))
    details = db.Column(db.JSON, nullable=True)


# Define QuestionHistory model
class QuestionHistory(TimestampModel):
    id = db.Column(db.Integer(), primary_key=True)
    user_id = db.Column(db.Integer(), db.ForeignKey('user.id', ondelete='CASCADE'))
    conversation_id = db.Column(db.String(100))
    dialog_id = db.Column(db.String(100))
    question = db.Column(db.JSON, nullable=True)
    response = db.Column(db.JSON, nullable=True)
    extra_details = db.Column(db.JSON, nullable=True) # e.g. 👍🏼👎🏼 thumbs up/down

Index("QuestionHistory_index", QuestionHistory.user_id, QuestionHistory.conversation_id, QuestionHistory.dialog_id)

# Define Sync_documentation model
class SyncDocumentation(db.Model):
    __tablename__ = 'sync_documentation'
    __table_args__ = {'schema': 'dbo'}  # optional if using schemas

    id = db.Column(db.Integer(), primary_key=True, autoincrement=True)
    doc_name = db.Column(db.String(1000))
    status = db.Column(db.String(1000))
    document_updated = db.Column(db.DateTime)
    document_type = db.Column(db.String(1000))
    document_intelligence_result_json = db.Column(db.LargeBinary)

    # deferred loading of large binary field
    document_intelligence_result_json = deferred(
        db.Column(db.LargeBinary)
    )


# Configure Flask-Session with SQLAlchemy
# class Session(db.Model):
#     id = db.Column(db.Integer, primary_key=True)
#     session_data = db.Column(db.LargeBinary)
#     user_id = db.Column(db.Integer, db.ForeignKey('user.id'))  # Optional, for user association

#     def __repr__(self):
#         return f'<Session {self.id}>'

# Define AppConfig model
class AppConfigDB(db.Model):
    __tablename__ = 'app_config'

    id = db.Column(db.Integer(), primary_key=True)
    key_name = db.Column(db.String(255))
    value = db.Column(db.String(255))

## ORACLE MODELS

class CommercialProductDocument(db.Model):
    __tablename__ = "COMMERCIAL_PRODUCTS_DOCUMENTS"
    __table_args__= {"schema": "DWH_PUBLIC", "extend_existing": True}
    __bind_key__ = "oracle"


    DOCUMENT_PREFIX = db.Column(db.String(25), primary_key = True)
    INTERNAL_CODE = db.Column(db.String(25), primary_key = True)
    DOCUMENT_NUMBER = db.Column(db.String(150), primary_key = True)
    PRODUCT_NUMBER = db.Column(db.String(30), primary_key = True)
    FACTORY_MODEL = db.Column(db.String(60))
    COMMERCIAL_MODEL = db.Column(db.String(30))
    DOCUMENT_TYPE_DESCR = db.Column(db.String(50))
    DOCUMENT_TYPE = db.Column(db.String(7), primary_key = True)
    DOCUMENT_EDITION = db.Column(db.String(15), primary_key = True)
    DOCUMENT_DATE = db.Column(db.DateTime)
    ID_FILE = db.Column(db.Numeric(38, 0), primary_key = True)
    FILE_DESCR = db.Column(db.String(200))
    FILE_PATH = db.Column(db.String(255))
    FILE_SIZE = db.Column(db.Numeric(38, 0))
    LANGUAGE = db.Column(db.Numeric(38, 0))
    LANGUAGE_DESCR = db.Column(db.String(30))
    DOCUMENT_BRAND = db.Column(db.String(30))
    DOCUMENT_STATUS = db.Column(db.Numeric(38, 0))
    DOCUMENT_TITLE = db.Column(db.String(255))
    IS_DOCUMENT_PUBLIC = db.Column(db.Numeric(38, 0))
    DOCUMENT_DESCR = db.Column(db.String(2000))
    ALIAS = db.Column(db.String(30))
    CERTIFICATE_TYPE = db.Column(db.String(30))
    FACTORY = db.Column(db.String(5))
    PLATFORM = db.Column(db.String(50))
    OWNER = db.Column(db.String(30))
    SUPPLIERCODE = db.Column(db.String(50))
    SUPPLIER = db.Column(db.String(100))
    CLASS = db.Column(db.String(50))
    FAMILY = db.Column(db.String(50))
    TYPE = db.Column(db.String(50))
    ELSCODE = db.Column(db.String(20))
    PRODUCT_LINE = db.Column(db.String(100))

    def to_json_list(self):
        return {
            'file_name': self.FILE_PATH,
            'document_descr': self.DOCUMENT_DESCR,
            'internal_code': self.INTERNAL_CODE,
            'product_number': self.PRODUCT_NUMBER,
            'factory_model': self.FACTORY_MODEL,
            'doc_type': self.DOCUMENT_TYPE,
            'document_edition': self.DOCUMENT_EDITION,
            'language_id': self.LANGUAGE
        }

class TBPU_Common(db.Model):
    __tablename__ = "TBPU_COMMON"
    __table_args__= {"schema": "DWH_PUBLIC", "extend_existing": True}
    __bind_key__ = "oracle"


    COMPANY = db.Column(db.String(25), primary_key=True)
    TABLE = db.Column(db.String(25), primary_key=True)
    CODE = db.Column(db.String(150), primary_key=True)
    DESCRIPTION = db.Column(db.String(30))
    OTHER_1 = db.Column(db.String(30), primary_key=True)
    OTHER_2 = db.Column(db.String(30))
    OTHER_3 = db.Column(db.String(30))

    def to_json_list(self):
        return {
            'file_name': self.COMPANY,
            'document_descr': self.TABLE,
            'internal_code': self.CODE,
            'product_number': self.DESCRIPTION,
            'factory_model': self.OTHER_1,
            'doc_type': self.OTHER_2,
            'document_edition': self.OTHER_3
        }