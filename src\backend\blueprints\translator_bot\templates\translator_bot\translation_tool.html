<!-- templates/translator_bot/translation_tool.html -->

{% extends "base.html" %}

{% block head_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
<style>
    #toggleAllColumns {
        transition: all 0.3s ease;
        border-color: var(--warm-grey-secondary, #BFBAB0);
        color: var(--corporate-primary, #1E1F41);
    }

    #toggleAllColumns:hover {
        background-color: var(--warm-grey-light, #DFD5D2);
        border-color: var(--corporate-secondary, #7B869C);
        transform: translateY(-1px);
    }

    #toggleAllColumns i {
        transition: transform 0.2s ease;
    }

    #toggleAllColumns:hover i {
        transform: scale(1.1);
    }
</style>
{% endblock %}

{% block content %}
<div class="translation-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10 col-xl-8">
                <div class="translation-card">
                    <div class="card-header-custom">
                        <h1 class="mb-0">
                            <i class="fas fa-language me-3"></i>
                            Document Translation Tool
                        </h1>
                        <p class="mb-0 mt-2 opacity-75">
                            Translate Excel, PowerPoint, and Word documents with AI
                        </p>
                    </div>
                    
                    <div class="card-body p-4">
                        <!-- File Upload Section -->
                        <div class="mb-4">
                            <h5 class="mb-3">
                                <i class="fas fa-upload me-2"></i>
                                Upload Document
                            </h5>
                            
                            <div class="upload-area" id="uploadArea">
                                <div class="upload-icon">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                </div>
                                <h6>Drag & drop your file here</h6>
                                <p class="text-muted mb-3">or click to browse</p>
                                <p class="small text-muted">
                                    Supported formats: Excel (.xlsx), PowerPoint (.pptx), Word (.docx)
                                    <br>Maximum file size: 50MB
                                </p>
                                <input type="file" id="fileInput" accept=".xlsx,.pptx,.docx" style="display: none;">
                            </div>
                            
                            <div id="fileInfo" class="file-info" style="display: none;">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-file me-3 text-success"></i>
                                    <div class="flex-grow-1">
                                        <div class="fw-bold" id="fileName"></div>
                                        <div class="small text-muted" id="fileDetails"></div>
                                    </div>
                                    <button class="btn btn-sm btn-outline-danger" id="removeFile">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Language Selection -->
                        <div class="mb-4">
                            <h5 class="mb-3">
                                <i class="fas fa-globe me-2"></i>
                                Language Settings
                            </h5>
                            
                            <div class="language-grid">
                                <div>
                                    <label for="sourceLanguage" class="form-label fw-semibold">Source Language</label>
                                    <select class="form-select form-select-custom" id="sourceLanguage">
                                        <option value="auto">Auto-detect</option>
                                        <option value="en">English</option>
                                        <option value="es">Spanish</option>
                                        <option value="fr">French</option>
                                        <option value="de">German</option>
                                        <option value="it">Italian</option>
                                        <option value="pt">Portuguese</option>
                                        <option value="ru">Russian</option>
                                        <option value="ja">Japanese</option>
                                        <option value="ko">Korean</option>
                                        <option value="zh">Chinese</option>
                                    </select>
                                </div>
                                
                                <div>
                                    <label for="targetLanguage" class="form-label fw-semibold">Target Language</label>
                                    <select class="form-select form-select-custom" id="targetLanguage">
                                        <option value="">Select target language</option>
                                        <option value="en">English</option>
                                        <option value="es">Spanish</option>
                                        <option value="fr">French</option>
                                        <option value="de">German</option>
                                        <option value="it">Italian</option>
                                        <option value="pt">Portuguese</option>
                                        <option value="ru">Russian</option>
                                        <option value="ja">Japanese</option>
                                        <option value="ko">Korean</option>
                                        <option value="zh">Chinese</option>
                                        <option value="ar">Arabic</option>
                                        <option value="hi">Hindi</option>
                                        <option value="th">Thai</option>
                                        <option value="vi">Vietnamese</option>
                                        <option value="nl">Dutch</option>
                                        <option value="sv">Swedish</option>
                                        <option value="no">Norwegian</option>
                                        <option value="da">Danish</option>
                                        <option value="fi">Finnish</option>
                                        <option value="pl">Polish</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Excel Column Selection (hidden by default) -->
                        <div id="excelOptions" class="excel-options" style="display: none;">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5 class="mb-0">
                                    <i class="fas fa-table me-2"></i>
                                    Excel Column Selection
                                </h5>
                                <button type="button" class="btn btn-sm btn-outline-secondary" id="toggleAllColumns">
                                    <i class="fas fa-check-square me-1"></i>
                                    Select All
                                </button>
                            </div>
                            <p class="text-muted mb-3">Select which columns you want to translate:</p>

                            <div class="row" id="columnCheckboxes">
                                <!-- Checkboxes will be populated dynamically -->
                            </div>
                        </div>

                        <!-- Translation Button -->
                        <div class="text-center mt-4">
                            <button class="btn btn-translate btn-lg" id="translateBtn" disabled>
                                <i class="fas fa-magic me-2"></i>
                                Start Translation
                            </button>
                        </div>

                        <!-- Preview Button for Excel -->
                        <div class="text-center mt-3" id="previewSection" style="display: none;">
                            <button class="btn btn-outline-primary" id="previewBtn">
                                <i class="fas fa-eye me-2"></i>
                                Preview Translation
                            </button>
                        </div>

                        <!-- Progress Section (hidden by default) -->
                        <div id="progressSection" class="progress-container" style="display: none;">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="fw-semibold">Translation Progress</span>
                                <span id="progressPercent">0%</span>
                            </div>
                            <div class="progress progress-custom">
                                <div class="progress-bar progress-bar-custom" id="progressBar" role="progressbar" style="width: 0%"></div>
                            </div>
                            <div class="status-text" id="statusText">Initializing...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block tail_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const uploadArea = document.getElementById('uploadArea');
    const fileInput = document.getElementById('fileInput');
    const fileInfo = document.getElementById('fileInfo');
    const fileName = document.getElementById('fileName');
    const fileDetails = document.getElementById('fileDetails');
    const removeFile = document.getElementById('removeFile');
    const excelOptions = document.getElementById('excelOptions');
    const columnCheckboxes = document.getElementById('columnCheckboxes');
    const toggleAllColumns = document.getElementById('toggleAllColumns');
    const translateBtn = document.getElementById('translateBtn');
    const targetLanguage = document.getElementById('targetLanguage');
    const progressSection = document.getElementById('progressSection');
    const progressBar = document.getElementById('progressBar');
    const progressPercent = document.getElementById('progressPercent');
    const statusText = document.getElementById('statusText');
    const previewSection = document.getElementById('previewSection');
    const previewBtn = document.getElementById('previewBtn');
    
    let selectedFile = null;

    // Helper function to show alerts
    function showAlert(message, type = 'info') {
        // Create a simple alert div
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.body.appendChild(alertDiv);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 5000);
    }

    // Upload area click handler
    uploadArea.addEventListener('click', () => {
        fileInput.click();
    });

    // Drag and drop handlers
    uploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });

    uploadArea.addEventListener('dragleave', () => {
        uploadArea.classList.remove('dragover');
    });

    uploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleFileSelect(files[0]);
        }
    });

    // File input change handler
    fileInput.addEventListener('change', (e) => {
        if (e.target.files.length > 0) {
            handleFileSelect(e.target.files[0]);
        }
    });

    // Remove file handler
    removeFile.addEventListener('click', () => {
        selectedFile = null;
        fileInfo.style.display = 'none';
        uploadArea.style.display = 'block';
        excelOptions.style.display = 'none';
        fileInput.value = '';
        updateTranslateButton();
    });

    // Target language change handler
    targetLanguage.addEventListener('change', updateTranslateButton);

    // Translate button handler
    translateBtn.addEventListener('click', startTranslation);

    // Preview button handler
    previewBtn.addEventListener('click', showExcelPreview);

    // Toggle all columns button handler
    toggleAllColumns.addEventListener('click', function() {
        const checkboxes = columnCheckboxes.querySelectorAll('input[type="checkbox"]');
        const allChecked = Array.from(checkboxes).every(cb => cb.checked);

        checkboxes.forEach(checkbox => {
            checkbox.checked = !allChecked;
        });

        // Update button text and icon
        const icon = this.querySelector('i');
        const text = this.querySelector('span') || this.childNodes[this.childNodes.length - 1];

        if (allChecked) {
            icon.className = 'fas fa-square me-1';
            this.innerHTML = '<i class="fas fa-square me-1"></i>Select All';
        } else {
            icon.className = 'fas fa-check-square me-1';
            this.innerHTML = '<i class="fas fa-check-square me-1"></i>Deselect All';
        }

        updateTranslateButton();
    });

    function handleFileSelect(file) {
        // Validate file type
        const allowedTypes = ['.xlsx', '.pptx', '.docx'];
        const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
        
        if (!allowedTypes.includes(fileExtension)) {
            showAlert('Please select a valid file type: Excel (.xlsx), PowerPoint (.pptx), or Word (.docx)', 'warning');
            return;
        }

        // Validate file size (50MB limit)
        if (file.size > 50 * 1024 * 1024) {
            showAlert('File size must be less than 50MB', 'warning');
            return;
        }

        selectedFile = file;
        
        // Update file info display
        fileName.textContent = file.name;
        fileDetails.textContent = `${(file.size / 1024 / 1024).toFixed(2)} MB • ${fileExtension.toUpperCase().substring(1)} file`;
        
        uploadArea.style.display = 'none';
        fileInfo.style.display = 'block';

        // Upload file and get column information for Excel files
        if (fileExtension === '.xlsx') {
            uploadFileToServer(file);
        } else {
            excelOptions.style.display = 'none';
            updateTranslateButton();
        }
    }

    function uploadFileToServer(file) {
        const formData = new FormData();
        formData.append('file', file);
        
        fetch('/translator/api/upload', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                if (data.columns) {
                    showExcelOptions(data.columns, data.preview);
                }
                updateTranslateButton();
            } else {
                alert('Error uploading file: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Upload error:', error);
            alert('Error uploading file');
        });
    }

    function showExcelOptions(columns, preview) {
        columnCheckboxes.innerHTML = '';
        
        // Show preview section
        if (preview && preview.length > 0) {
            const previewDiv = document.createElement('div');
            previewDiv.className = 'mb-3';
            previewDiv.innerHTML = `
                <h6>File Preview:</h6>
                <div class="table-responsive">
                    <table class="table table-sm table-striped">
                        <thead>
                            <tr>${columns.map(col => `<th>${col}</th>`).join('')}</tr>
                        </thead>
                        <tbody>
                            ${preview.slice(0, 3).map(row => 
                                `<tr>${columns.map(col => `<td>${row[col] || ''}</td>`).join('')}</tr>`
                            ).join('')}
                        </tbody>
                    </table>
                </div>
            `;
            columnCheckboxes.appendChild(previewDiv);
        }
        
        // Show column checkboxes
        const checkboxContainer = document.createElement('div');
        checkboxContainer.className = 'row';
        
        columns.forEach((column, index) => {
            const colDiv = document.createElement('div');
            colDiv.className = 'col-md-6 column-checkbox';
            
            colDiv.innerHTML = `
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" value="${column}" id="col${index}" checked>
                    <label class="form-check-label" for="col${index}">
                        ${column}
                    </label>
                </div>
            `;
            
            checkboxContainer.appendChild(colDiv);
        });
        
        columnCheckboxes.appendChild(checkboxContainer);

        // Initialize toggle button state (all checkboxes are checked by default)
        toggleAllColumns.innerHTML = '<i class="fas fa-check-square me-1"></i>Deselect All';

        excelOptions.style.display = 'block';
        document.getElementById('previewSection').style.display = 'block';

        // Add preview button event listener
        document.getElementById('previewBtn').addEventListener('click', showTranslationPreview);
    }
    
    function showTranslationPreview() {
        if (!selectedFile || !targetLanguage.value) {
            alert('Please select a file and target language');
            return;
        }
        
        // Get selected columns
        const checkboxes = columnCheckboxes.querySelectorAll('input[type="checkbox"]:checked');
        const selectedColumns = Array.from(checkboxes).map(cb => cb.value);
        
        if (selectedColumns.length === 0) {
            alert('Please select at least one column to translate');
            return;
        }
        
        // Build translation request
        let translationRequest;
        if (selectedColumns.length === 1) {
            translationRequest = `Translate the column ${selectedColumns[0]} in ${getLanguageName(targetLanguage.value)}`;
        } else {
            translationRequest = `Translate the columns ${selectedColumns.join(', ')} in ${getLanguageName(targetLanguage.value)}`;
        }
        
        // Call preview API
        fetch('/translator/api/preview', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                translation_request: translationRequest
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showPreviewModal(data.preview_html, data.total_rows, data.columns);
            } else {
                alert('Preview failed: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Preview error:', error);
            alert('Preview failed');
        });
    }
    
    function showPreviewModal(previewHtml, totalRows, columns) {
        // Create modal for preview
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Translation Preview</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <p>Preview showing first 10 rows of ${totalRows} total rows</p>
                        ${previewHtml}
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <button type="button" class="btn btn-primary" onclick="startTranslation()">Proceed with Full Translation</button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();
        
        // Remove modal from DOM when hidden
        modal.addEventListener('hidden.bs.modal', () => {
            document.body.removeChild(modal);
        });
    }
    
    function getLanguageName(code) {
        const languages = {
            'en': 'English', 'es': 'Spanish', 'fr': 'French', 'de': 'German',
            'it': 'Italian', 'pt': 'Portuguese', 'ru': 'Russian', 'ja': 'Japanese',
            'ko': 'Korean', 'zh': 'Chinese', 'ar': 'Arabic', 'hi': 'Hindi',
            'th': 'Thai', 'vi': 'Vietnamese', 'nl': 'Dutch', 'sv': 'Swedish',
            'no': 'Norwegian', 'da': 'Danish', 'fi': 'Finnish', 'pl': 'Polish'
        };
        return languages[code] || code;
    }

    function updateTranslateButton() {
        const hasFile = selectedFile !== null;
        const hasTargetLanguage = targetLanguage.value !== '';
        
        translateBtn.disabled = !(hasFile && hasTargetLanguage);
    }

    function startTranslation() {
        if (!selectedFile || !targetLanguage.value) {
            alert('Please select a file and target language');
            return;
        }
        
        // Get selected columns for Excel files
        let selectedColumns = [];
        if (selectedFile.name.toLowerCase().endsWith('.xlsx')) {
            const checkboxes = columnCheckboxes.querySelectorAll('input[type="checkbox"]:checked');
            selectedColumns = Array.from(checkboxes).map(cb => cb.value);
        }
        
        progressSection.style.display = 'block';
        translateBtn.disabled = true;
        
        // Start translation
        const translationData = {
            target_language: targetLanguage.value,
            source_language: document.getElementById('sourceLanguage').value,
            selected_columns: selectedColumns,
            file_type: '.' + selectedFile.name.split('.').pop().toLowerCase()
        };
        
        fetch('/translator/api/translate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(translationData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                if (data.status === 'completed') {
                    // Translation completed immediately (Excel files)
                    progressBar.style.width = '100%';
                    progressPercent.textContent = '100%';
                    statusText.textContent = 'Translation complete!';
                    
                    // Show download button
                    const downloadLink = document.createElement('a');
                    downloadLink.href = `/translator/api/download/${data.translation_id}`;
                    downloadLink.className = 'btn btn-success btn-lg mt-3';
                    downloadLink.innerHTML = '<i class="fas fa-download me-2"></i>Download Translated File';
                    
                    progressSection.appendChild(downloadLink);
                    
                    // Show preview if available
                    if (data.data) {
                        showPreview(data.data);
                    }
                } else {
                    // Handle ongoing translation process
                    monitorTranslationProgress(data.translation_id);
                }
                translateBtn.disabled = false;
            } else {
                alert('Translation failed: ' + data.error);
                progressSection.style.display = 'none';
                translateBtn.disabled = false;
            }
        })
        .catch(error => {
            console.error('Translation error:', error);
            alert('Translation failed');
            progressSection.style.display = 'none';
            translateBtn.disabled = false;
        });
    }
    
    function showPreview(jsonData) {
        try {
            const data = JSON.parse(jsonData);
            const previewDiv = document.createElement('div');
            previewDiv.className = 'mt-4';
            previewDiv.innerHTML = `
                <h6>Translation Preview (First 5 rows):</h6>
                <div class="table-responsive">
                    <table class="table table-sm table-striped">
                        <thead>
                            <tr>${Object.keys(data[0] || {}).map(key => `<th>${key}</th>`).join('')}</tr>
                        </thead>
                        <tbody>
                            ${data.slice(0, 5).map(row => 
                                `<tr>${Object.values(row).map(val => `<td>${val}</td>`).join('')}</tr>`
                            ).join('')}
                        </tbody>
                    </table>
                </div>
            `;
            progressSection.appendChild(previewDiv);
        } catch (e) {
            console.error('Error showing preview:', e);
        }
    }
    
    function monitorTranslationProgress(translationId) {
        // For non-Excel files that require polling
        const interval = setInterval(() => {
            fetch(`/translator/api/status/${translationId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    progressBar.style.width = data.progress + '%';
                    progressPercent.textContent = data.progress + '%';
                    statusText.textContent = data.status;
                    
                    if (data.status === 'completed') {
                        clearInterval(interval);
                        const downloadLink = document.createElement('a');
                        downloadLink.href = data.download_url;
                        downloadLink.className = 'btn btn-success btn-lg mt-3';
                        downloadLink.innerHTML = '<i class="fas fa-download me-2"></i>Download Translated File';
                        progressSection.appendChild(downloadLink);
                    }
                }
            })
            .catch(error => {
                console.error('Status check error:', error);
                clearInterval(interval);
            });
        }, 2000);
    }

    function showExcelPreview() {
        if (!selectedFile) {
            alert('Please upload an Excel file first');
            return;
        }
        
        // Get selected columns for preview
        let selectedColumns = [];
        const checkboxes = columnCheckboxes.querySelectorAll('input[type="checkbox"]:checked');
        selectedColumns = Array.from(checkboxes).map(cb => cb.value);
        
        const previewData = {
            file_name: selectedFile.name,
            selected_columns: selectedColumns
        };
        
        fetch('/translator/api/preview', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(previewData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show preview in a new window
                const previewWindow = window.open('', '_blank');
                previewWindow.document.write(`
                    <html>
                        <head>
                            <title>Translation Preview</title>
                            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
                            <style>
                                body { font-family: Arial, sans-serif; margin: 0; padding: 0; }
                                .container { max-width: 800px; margin: 20px auto; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
                                h1 { font-size: 24px; margin-bottom: 20px; }
                                table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                                th, td { padding: 10px; text-align: left; border: 1px solid #ddd; }
                                th { background-color: #f4f4f4; }
                                .btn { display: inline-block; padding: 10px 20px; margin-top: 10px; background-color: #007bff; color: #fff; text-decoration: none; border-radius: 4px; }
                                .btn:hover { background-color: #0056b3; }
                            </style>
                        </head>
                        <body>
                            <div class="container">
                                <h1>Translation Preview</h1>
                                <table>
                                    <thead>
                                        <tr>${data.columns.map(col => `<th>${col}</th>`).join('')}</tr>
                                    </thead>
                                    <tbody>
                                        ${data.preview.map(row => 
                                            `<tr>${data.columns.map(col => `<td>${row[col] || ''}</td>`).join('')}</tr>`
                                        ).join('')}
                                    </tbody>
                                </table>
                                <a href="/translator/api/download_preview/${data.translation_id}" class="btn">
                                    <i class="fas fa-download me-2"></i>Download Preview
                                </a>
                            </div>
                        </body>
                    </html>
                `);
                previewWindow.document.close();
            } else {
                alert('Error generating preview: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Preview error:', error);
            alert('Error generating preview');
        });
    }
});
</script>
{% endblock %}
