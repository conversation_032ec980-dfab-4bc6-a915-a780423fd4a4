from typing import List, Optional
import re
from datetime import datetime
import uuid
from collections import OrderedDict
from langchain_core.documents.base import Document
from langchain_text_splitters import (
    MarkdownHeaderTextSplitter,
    RecursiveCharacterTextSplitter,
    TextSplitter
)
from rag_indexer.src.indexer.image_extractor import image_extractor, add_number_image
from rag_indexer.src.indexer.summary import Summary

from utils.core import get_logger
from utils.clients import AzureBlobClient
from config.config import BlobStorageConfig
from src.common_tools.embedders.rag_embedder import RAGEmbedder

logger = get_logger(__file__)

class MarkdownTagSplitter(TextSplitter):
    
    def __init__(self, separator:str):
        self.separator = separator
        
    
    def split_text(self, page_content: str, metadata) -> List[Document]: 
        # Split the text by the custom separator
        documents_chunk = []
        chunks = page_content.split(self.separator)
        for chunk in chunks:
            documents_chunk.append(Document(page_content=chunk, metadata= metadata))
        return documents_chunk 

class DocumentSplitter:
    """Provides methods to split documents into chunks of information."""

    def __init__(
        self,
        deepest_header_level: int,
        bot_name: str,
        max_chunk_size: Optional[int] = 2048,
    ) -> None:
        """Create a new DocumentSplitter

        Args:
            deepest_header_level (int): The maximum header granularity. Its value influences the depth of headers on which the splits must be executed. For instance, if `deepest_header_level` = 3, Header 1, Header 2, and Header 3 will be considered.
            chunk_size (Optional[int], optional): Defines how many characters should be included in each chunk. Defaults to 6144.
        """
        assert deepest_header_level > 0, "Must split on at least one header."
        headers = [("#" * i, f"Header {i}") for i in range(1, deepest_header_level + 1)]
        logger.debug(
            f"The following markdown headers will be use for splitting:\n{headers}"
        )

        logger.debug("Creating layout splitter...")
        self.layout_splitter = MarkdownHeaderTextSplitter(
            headers_to_split_on=headers, strip_headers=False
        )

        logger.debug("Creating Markdown tag splitter...")
        self.tag_splitter = MarkdownTagSplitter(separator="<!-- PageBreak -->")
        
        logger.debug("Creating section splitter...")
        self.section_splitter = RecursiveCharacterTextSplitter.from_tiktoken_encoder(
            encoding_name="cl100k_base", chunk_size=max_chunk_size, chunk_overlap=0
        )

        logger.info(
            f"Created splitter with {deepest_header_level} header depth and {max_chunk_size} characters chunk size."
        )
        blob_config = BlobStorageConfig()
        self.storage_client = AzureBlobClient(blob_config, bot_name)
        self.summary = Summary()
        self.embedder = RAGEmbedder()
        
        # the regular expression for separate rows of a markdown table
        #self.regex_row = r"\|.*\|" NOT USEFUL ANYMORE, NOW TABLES ARE TAGGED WITH <table></table> markdown
        
    def __group_tables(self, rows: List[List[str]]) -> List[str]:
        """Group the rows found in the layout chunk in a single markdown string representing the table

        Args:
            List[List[str]]: The list of the rows of the table found in the layout chunk

        Returns:
            List[str]: The list of tables found in the layout chunk
        """
        
        tables_groups = []
        current_table = []
        tables_found = []
        for row in rows:
            if len(row) != 0:  #if the length of the row is > 0, we have a row 
                current_table.extend(row)
            elif current_table:  #if the length of the row is = 0 and current_table is not empty, a table is completed and pushed to the list of tables
                tables_groups.append(current_table)
                current_table = []
    
        if current_table:
            tables_groups.append(current_table)
            
        for table in tables_groups:
            tables_found.append("\n".join(table))
        
        return tables_found
            
        
    def get_tables_from_chunk(self, page_content: str) -> List[str]:
        """Extract the rows of the tables contained in a single layout chunk.

        Args:
            document (Document): The document to split.

        Returns:
            List[str]: The list of tables found in the layout chunk
        """
        # content_splitted_by_line = document.page_content.split('\n')
        # rows_found = [] 
        # for line in content_splitted_by_line:
        #     row = re.findall(self.regex_row,line)
        #     rows_found.append(row)
        
        # return self.__group_tables(rows_found)
        
        tables = re.findall(r'<table>.*?</table>', page_content, re.DOTALL)
        return tables
         
    def __count_figure_tags(self, document_chunk:str) -> int:
         
        figure_tags = re.findall(r'<figure>.*?</figure>', document_chunk, re.DOTALL)
        if len(figure_tags) > 0:
            figure_numbers = re.findall(r"figures/\d+",document_chunk)
        else:
            figure_numbers = []
            
        logger.debug(f"Found {len(figure_tags)} images in this chunk")
        number_of_tags = len(figure_tags)
        return figure_numbers, number_of_tags
    
    
    async def split(self, document: Document) -> List[Document]:
        """Splits a given document into smaller chunks. Each of the chunks inherits its parent's metadata.

        Args:
            document (Document): The document to split.

        Returns:
            List[Document]: The chunks of `document` yielded by the splitting.
        """
        performance_metric_file = open('performance_metrics.log', 'a')
        file_name = document.metadata['url'].split("\\")[-1]

        if len(document.metadata['url'].split("\\")) == 1: #manage different path structure for PDF converted files
            file_name = document.metadata['url'].split("/")[-1]
            
        table_name_prefix = file_name.split(".")[0]
        
        
        #EXTRACT IMAGES
        images = document.metadata["images"]
        
        #vedere commento nella funzione add_number_image
        if images and file_name.endswith("pdf") :
            
            print(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - Image Extraction started", file=performance_metric_file)
            document.page_content = add_number_image(document.page_content)
            pages = self.tag_splitter.split_text(document.page_content, document.metadata)
            page_number = 1
            for page in pages:
                figure_tags, number_figure_in_page = self.__count_figure_tags(page.page_content)
                image_extracted_in_page = [image for image in images if int(image["id"].split(".")[0]) == page_number]
                if number_figure_in_page != len(image_extracted_in_page):
                    image_to_remove = min(image_extracted_in_page, key=lambda image: image["spans"][0]["length"])
                    images.remove(image_to_remove)
                page_number += 1
            document_images = image_extractor(document.metadata["url"], images, file_name)
            print(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - Image Extraction ended", file=performance_metric_file)
            
            
        
        del document.metadata["images"] #rimuove le informazioni provenienti da document intelligence sulle immagini, non sono necessarie per l'indice.
        
         
        # if file_name.endswith(".pptx"): #Splitting per slide per i powerpoint, per poter tenere le informazioni di una slide in un singolo chunk
        #     layout = self.tag_splitter.split_text(document.page_content, document.metadata)
        # else:
        #     layout = self.layout_splitter.split_text(document.page_content)
        
        layout = self.layout_splitter.split_text(document.page_content)
        
        #Extract tables in a layout chunk
        print(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - Table Extraction and storage started", file=performance_metric_file)
        tables_to_upload = []
        for layout_chunk in layout:  
            layout_chunk.metadata.update({"tables": []})
            table_ids, tables = self.extract_table(layout_chunk, table_name_prefix)              
            #update metadata of the chunk with a list of the blob name of the tables
            for table in tables:
                update_table_data = {
                    'summary': table["table_summary"],
                    # 'embedding_summary': table["table_embedddings"],
                    'table_name': table["table_name"]
                }
                layout_chunk.metadata["tables"].append(update_table_data)
            tables_to_upload.extend(tables)
            
        #batch upload tables
        await self.storage_client.write_table_batch(tables_to_upload)
        print(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - Table Extraction and storage ended", file=performance_metric_file)
        
        # Image upload intok blob
        if file_name.endswith(".pptx"): # Se è un powerpoint, non viene fatto lo splitting per token
            chunks = layout
        else:        
            chunks = self.section_splitter.split_documents(layout)

            print(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - Image storage started", file=performance_metric_file)
            images_to_upload = []
            for chunk in chunks:
                # chunk.metadata.update(document.metadata) #To delete overwrite the table data
                chunk.metadata.update({'url': document.metadata['url']})
                chunk.metadata.update({'file_name': file_name})
                chunk.metadata.update({'images':[]})
                if file_name.endswith("pdf"):
                    #adding image url to blob in the metadata
                    images_id = []
                    figure_numbers, number_images_in_chunk = self.__count_figure_tags(chunk.page_content)
                    if number_images_in_chunk > 0:
                        for figure_number in figure_numbers:
                            if len(document_images) > 0:
                                number = int(figure_number.split("/")[1]) + 1
                                image_name = table_name_prefix + "_image_" + str(number) + ".jpg"  #rinominare table_name_prefix
                                image = [element for element in document_images if element["image_name"] == image_name ]
                                if len(image) > 0:
                                    #self.storage_client.write_image_blob(image[0]["image_name"], image[0]["image"])
                                    random_uid = str(uuid.uuid4())
                                    image_name_to_upload = random_uid + "_" + image[0]["image_name"]
                                    images_to_upload.append({
                                        'image_name':image_name_to_upload,
                                        'image': image[0]["image"],
                                        'summary': image[0]["image_summary"],
                                        # 'embeddings_summary': image[0]["imgage_embeddings"]
                                    })
                                    images_id.append({
                                        'image_name':image_name_to_upload,
                                        # 'image': image[0]["image"],
                                        'summary': image[0]["image_summary"],
                                        # 'embeddings_summary': image[0]["imgage_embeddings"]
                                    })

                        if len(images_id) > 0:
                            # chunk.metadata.update({"images": str(images_id)})
                            chunk.metadata["images"]=(images_id)
                            # chunk.metadata.update({"images": [
                            #     {
                            #         'summary': tables["image_summary"],
                            #         'embedding_summary': tables["image_embedddings"],
                            #         'image_id': str(images_id)
                            #     }
                            # ]})
                            logger.info("Image saved in blob")
                
                chunk.metadata = self.move_url_metadata(chunk.metadata, 'url')
                # Summary and Embeddings of text of a chunk
                text_summary = self.summary.text_summary(chunk.page_content)
                text_embeddings = self.embedder.embed(text_summary)
                chunk.metadata.update({"text-chunk": chunk.page_content})
                chunk.metadata.update({"text_summary": text_summary})
                chunk.metadata.update({'text_embeddings': text_embeddings})

            #upload all images
            await self.storage_client.write_image_batch(images_to_upload)
            print(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - Image storage ended", file=performance_metric_file)

        performance_metric_file.close()
        return chunks
    
    
    def extract_table(self, chunk: Document, table_name_prefix: str): 
        tables_in_chunk = self.get_tables_from_chunk(chunk.page_content) # CAMBIARE LA REGEX IN <table>.*?</table>
            
        table_ids = []
        tables = []
        table_index_without_paragraph = 0  #in order to manage tables without a header metadata, if it's not managed, a blob with same name is created causing an error
        if len(tables_in_chunk) > 0:
            table_index = 0
            for table in tables_in_chunk:
                
                if 'Header 3' in chunk.metadata:
                  table_name = table_name_prefix + "_" + chunk.metadata['Header 3'].replace("/", "-").replace("\\", "-") + "_" + str(table_index)
                  table_index+=1
                elif 'Header 2' in chunk.metadata:
                  table_name = table_name_prefix + "_" + chunk.metadata['Header 2'].replace("/", "-").replace("\\", "-") + "_" + str(table_index)
                  table_index+=1
                elif 'Header 1' in chunk.metadata:
                  table_name = table_name_prefix + "_" + chunk.metadata['Header 1'].replace("/", "-").replace("\\", "-") + "_" + str(table_index)
                  table_index+=1
                else:
                    table_name = table_name_prefix + "_" + str(table_index_without_paragraph)
                    table_index_without_paragraph+= 1
                #self.storage_client.write_table_blob(table_name, table) # [{table_name: , table:}]
                random_uid = str(uuid.uuid4())
                table_name = random_uid + "_" + table_name
                tab_summary = self.summary.table_summary(table)
                # tab_embed = self.embedder.embed(tab_summary)
                table_ids.append(table_name)
                tables.append({'table_name': table_name, 'table': table, 'table_summary': tab_summary})
        return table_ids, tables
    

    def move_url_metadata(self, metadata: dict, key_url: str):
        
        if key_url not in metadata:
            raise KeyError(f"Key '{key_url}' not present.")

        
        new_dict = OrderedDict()
        new_dict[key_url] = metadata[key_url]

        
        for key, value in metadata.items():
            if key != key_url:
                new_dict[key] = value

        return dict(new_dict)