import logging
from datetime import date
from json import load
from re import sub
from typing import Dict, List, Tuple
from os.path import dirname, join


from pydantic import BaseModel
from langchain_core.output_parsers import CommaSeparatedListOutputParser
from langchain_core.prompts import ChatPromptTemplate
from langchain_openai import AzureOpenAI
 
from src.agents.text_to_sql_agent.text_to_sql import text_to_sql
from src.agents.rag_agent.rag import rag
from src.agents.text_to_sql_agent.database.schema import DatabaseSchema
from utils.core import Singleton, get_logger

logger = get_logger(__file__)
FILE_DIR = dirname(__file__)

class IntentDetector(metaclass= Singleton):
    def __init__(self, model, tools: List[BaseModel]) -> None:
        """ This class is an helper that, by the help of the LLM, discover the intent of the user in order to select the agent that need to be called.
        """
        self.prompt_skeleton_path = join(FILE_DIR, "./intent_prompt_skeleton.json")
        logger.info("Environment variables loaded")
        
        self.llm = model._client

    def detect_intent(self, query: str) -> Dict:
        """Call to the llm in order to understand the agent to use
        
        Args:
            query (str): question of the user

        Returns:
            answer (Dict): data structure of the LLM response. 
        """
        
        prompt = ChatPromptTemplate.from_messages(
            [("user","""# Task

            Determine the intent of the user's input among the following defined intents:

            - **text_to_sql**: Queries that involve structured retrieval of information from a database, typically when the user requests a list of objects or an aggregation operation. It is referred typically to products or lists of documents. If the user asks about the presence of codes, IDs, or counts of elements, it is always **text_to_sql**.
            - **rag_document**: Requests to download or search complete documents or catalogs, usually associated with metadata linked to the document. For example: "I want this catalogue with document number 123", "search the document installation manual for this product code".
            - **rag**: Questions that require searching for content within a document or catalog and providing an answer. These contents can be specific or more generic, such as "I want all the information for installation." If the user requests guidance, instructions, or explanations, classify it as **rag**.

            Analyze the user's query, assign probabilities to each identified intent, and if you are not 100% certain of a single intent, include the relevant intents along with their probabilities.

            # Steps

            1. Analyze the input, identifying key elements that may correspond to one or more intents.
            2. Evaluate each intent based on the provided definition, assigning a confidence percentage.
            3. If only one intent is 100% certain, return it as the sole result. If there is ambiguity, include multiple intents with associated confidence percentages.

            # Output Format

            Return only a list of the detected intents, followed by their respective percentages. For example:

            text_to_sql, rag

            Or, if there is a single intent with 100% certainty:

            rag_document

            # Notes
    
            - Exclude any explanations, context, or reasoning from the final output.
            - Do not include any text other than the requested list.
            - A table related to documents is present in the database; if the user asks for a list or any count, it is most likely a **text_to_sql** intent.
            - Return an empty string if the user input regards everything not classifiable inside those three intents.
            - PNC stands for "Product Number Code".
            - If the user is asking for a list of documents (should be referred to as manuals or synonyms of the word "documents"), the answer is always **text_to_sql**.
            - **rag** or **rag_document** always refers to questions that can be answered by referring to a **single document**. If the question involves multiple documents, always use **text_to_sql**.
            - If the query involves instructions, guidance, or an explanation related to a product, it is always **rag**, even if it mentions a product code.
            - If the query involves retrieving codes, internal codes, spare part codes, or any numerical identifiers without asking for instructions, it is **text_to_sql**.
            - If the query asks for installation instructions, troubleshooting, or guidance on using or repairing a product, classify it as **rag**.
            - If the query asks for the description of a product, classify it as **text_to_sql**.

            Query: {input}  
            Intent: """)],
        )

        intent_detection_chain = prompt | self.llm | CommaSeparatedListOutputParser()
        intent_prediction_list = intent_detection_chain.invoke({"input": query})
        logger.info(f"Intend detected : {",".join(intent_prediction_list)}")
        
        if '' in intent_prediction_list: #Since Chat GPT is not capable to understand that need to return an empty list, but it retunrs a string with 0 char, we need to remove this string so that the array is empty and next checks could go well 
            intent_prediction_list.remove('')

        if "" in intent_prediction_list: #Same reason as the above if
            intent_prediction_list.remove("")

        if '""' in intent_prediction_list:
            intent_prediction_list.remove('""')

        return intent_prediction_list
    
    def detect_query_intent(self, query: str) -> Dict:
        """Call to the llm in order to understand the type of query 
        
        Args:
            query (str): question of the user

        Returns:
            answer (Dict): data structure of the LLM response. 
        """
        
        prompt = ChatPromptTemplate.from_messages(
            [("user","""# Task

            Determine the intent of the user's input is the following defined intent:

            - **children_to_parent**: Queries that reference spare parts or item code; asks for internal codes of final products that uses those spares; 
              optionally asks for the related engineering changes and do NOT request information about the component of spares given the finished products.
              As a guide consider the following examples:
              - Based on the bill of materials info, extract the internal codes and engineering mods of complete products having spares <CODE1> and <CODE2>.
              - Give me the list of internal codes and engineering changes where it is used the spare part code <CODE>
              - Which is the most recent engeeniring change for each product reported in the BOM for spare part 0L2202?


            # Steps

            1. Analyze the input, identifying key elements that may correspond to the intent.
            2. Evaluate each intent based on the provided definition, assigning a confidence percentage.
            3. Return the intent associated with the greatest probability

            # Output Format

            Return only the intent. For example:

            children_to_parent
        
            # Notes
    
            - Exclude any explanations, context, or reasoning from the final output.
            - Do not include any text other than the requested list.
            - Return an empty string if the user input regards everything not classifiable inside the defined intent.
            

            Query: {input}  
            Intent: """)],
        )

        intent_detection_chain = prompt | self.llm | CommaSeparatedListOutputParser()
        intent_prediction_list = intent_detection_chain.invoke({"input": query})
        logger.info(f"Intend detected : {",".join(intent_prediction_list)}")
        
        if '' in intent_prediction_list: #Since Chat GPT is not capable to understand that need to return an empty list, but it retunrs a string with 0 char, we need to remove this string so that the array is empty and next checks could go well 
            intent_prediction_list.remove('')

        if "" in intent_prediction_list: #Same reason as the above if
            intent_prediction_list.remove("")

        if '""' in intent_prediction_list:
            intent_prediction_list.remove('""')

        return intent_prediction_list

    def _get_json_prompt(self) -> Dict:
        with open(self.prompt_skeleton_path) as file:
            return load(file)




if __name__ == "__main__":
    detector = IntentDetector([rag, text_to_sql])
    test = {}
    test_list = []
    with open("C:\\Users\\<USER>\\compli-bot\\example_uploader\\synonyms.json") as file:
        raw = load(file)
    for key, examples in raw.items():
        for example in examples:
            test = {}
            test["question"] = example["inquiry"]
            test["tool"] = "text_to_sql"
            test_list.append(test)
    
    question_count = len(test_list)
    correct_predictions = 0
    wrong_questions_predicted = []
    for question in test_list:
        response = detector.detect_intent(question["question"])
        tool_calls = response.tool_calls
        if len(tool_calls) != 1:
            logger.info("Tools not determined")
        else:
            prediction = tool_calls[0]["name"]
            if prediction == "text_to_sql":
                correct_predictions +=1
            else:
                wrong_questions_predicted.append(question)
    
    
    for question in wrong_questions_predicted:
        logger.info(f"Following question is wrong predicted: {question["question"]}")
    
    logger.info(f""" Predicted correctly {correct_predictions} out of {question_count} text_to_sql questions""")