# CHANGELOG
{% if context.history.unreleased | length > 0 %}
{# UNRELEASED #}
## Unreleased
{% for type_, commits in context.history.unreleased | dictsort %}
### {{ type_ | capitalize }}
{% for commit in commits %}{% if type_ != "unknown" %}
* {{ commit.message.rstrip() }}
{% else %}
* {{ commit.message.rstrip() }}
{% endif %}{% endfor %}{% endfor %}{% endif %}
{% for version, release in context.history.released.items() %}
{# RELEASED #}
## {{ version.as_semver_tag() }} ({{ release.tagged_date.strftime("%Y-%m-%d") }})
{% for type_, commits in release["elements"] | dictsort %}
### {{ type_ | capitalize }}
{% for commit in commits %}{% if type_ != "unknown" %}
* {{ commit.message.rstrip() }}
{% else %}
* {{ commit.message.rstrip() }}
{% endif %}{% endfor %}{% endfor %}{% endfor %}