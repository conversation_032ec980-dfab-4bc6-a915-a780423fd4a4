from json import load
from typing import Dict, List, Union

from src.agents.text_to_sql_agent.database.schema import Column, DatabaseSchema, Table
from utils.core import get_logger

logger = get_logger(__file__)

_local_schemas = {}


def fetch_database_from_file(path: str) -> Dict[str, Union[str, List[str], List[dict]]]:
    logger.debug(f"Fetching schema from local file ({path})...")
    # Load data from JSON file
    with open(path) as file:
        schema = load(file)

    return schema


def fetch_database_from_dbms(
    inquiry_embedding: List[float], n: int
) -> Dict[str, Union[str, List[str], List[dict]]]:
    """
    Gets the `n` most relevant tables for the inquiry based on its embedding.
    """
    logger.debug("Fetching schema from DBMS...")
    return inquiry_embedding


def get_relevant_schemas(
    inquiry_embedding: List[float], quantity: int = 10, path: str = None
) -> DatabaseSchema:
    """
    Returns the schema representation of the `quantity` most relevant tables, decided based on the inquiry's embedding.
    """
    logger.debug(
        f"Fetching most relevant schema based on a {len(inquiry_embedding)}-dimensions inquiry embedding..."
    )
    if path is None:
        raw_schema = fetch_database_from_dbms(inquiry_embedding, quantity)
    elif path in _local_schemas:
        logger.info(
            "The schema requested has already been fetched. Instance is recycled."
        )
        return _local_schemas[path]
    else:
        raw_schema = fetch_database_from_file(path)

    logger.info("Database schema has been fetched!")

    logger.debug("Init DB representation...")
    schema = DatabaseSchema(list(raw_schema.keys())[0], [])
    raw_schema = raw_schema[list(raw_schema.keys())[0]]

    logger.debug("Populating DB representation...")
    for table_name, raw_table in raw_schema["tables"].items():
        table = Table(
            table_name,
            [],
            raw_table["schema"],
            raw_table["aliases"],
            raw_table["description"],
        )

        for column_name, raw_column in raw_table["columns"].items():
            table.add_column(
                Column(column_name, raw_column["aliases"], raw_column["description"], raw_column["metadata"] if "metadata" in raw_column else {})
            )

        schema.add_table(table)

    logger.info("DB representation has been populated!")
    if path is not None:
        _local_schemas[path] = schema

    return schema
