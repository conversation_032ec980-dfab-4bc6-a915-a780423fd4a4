from abc import ABC, abstractmethod
from src.backend.contracts.chat_data import Agent<PERSON>ame
from utils.exceptions import AgentValueException

class Answer(ABC):
    agent_name: str = None

    def add_agent_name(self, agent_name:str):

        if agent_name not in AgentName.__members__:
            raise AgentValueException("Invalid Agent Name for the answer!")
        else:
            self.agent_name = agent_name

    @abstractmethod
    def to_json(self):
        pass  