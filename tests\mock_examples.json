[{"inquiry": "Hi! Can you fetch the ongoing agreement for GROSSKUECHENEINRICHTUNGEN with Electrolux (Japan) LTD please?", "sql": "select agreement_code,start_validity_date,stop_validity_date,plant_description  from agreement_header where  locked_code = '0' and CURRENT_DATE between start_validity_date and stop_validity_date and supplier_description = 'GROSSKUECHENEINRICHTUNGEN' and company_description = 'Electrolux (Japan) LTD'"}, {"inquiry": "I need a list of suppliers with 'GLOBAL' in their description that have a valid agreement across all companies.", "sql": "select company_description,plant_description, agreement_code,start_validity_date,stop_validity_date from agreement_header where  locked_code = '0' and CURRENT_DATE between start_validity_date and stop_validity_date and supplier_description like '%GLOBAL%'"}, {"inquiry": "Can you show me the inventory of all the products in Sursee that have 00963786 as the preferred supplier?", "sql": "SELECT COMPANY_CODE,COMPANY_DESCRIPTION,PLANT_CODE,PLANT_DESCRIPTION,ITEM_CODE,MAIN_ITEM_DESCRIPTION,ITEM_STOCKING_TYPE,item_preferred_supplier, CASE WHEN SYSTEM_SOURCE = 'ELFO' AND start_production_date <= CURRENT_DATE AND STOP_PRODUCTION_DATE>=CURRENT_DATE THEN 'Y' WHEN SYSTEM_SOURCE = 'ELFO' AND (start_production_date > CURRENT_DATE oR STOP_PRODUCTION_DATE<CURRENT_DATE) THEN 'N' WHEN SYSTEM_SOURCE = 'JDE' AND ITEM_STOCKING_TYPE <> 'O' THEN 'Y' WHEN SYSTEM_SOURCE = 'JDE' AND ITEM_STOCKING_TYPE = 'O' THEN 'N' END AS ITEM_ACTIVE FROM ITEM_MASTER WHERE item_stocking_type='B' and trim(item_preferred_supplier)='588123' and company_description='Sursee'"}, {"inquiry": "I need the valid agreement for supplier COSTRUZIONI ELETTROMECCANICHE for FFD", "sql": "select agreement_code,start_validity_date,stop_validity_date,plant_description  from agreement_header where  locked_code = '0' and CURRENT_DATE between start_validity_date and stop_validity_date and supplier_description = 'COSTRUZIONI ELETTROMECCANICHE' and company_code = 'FFD'"}]