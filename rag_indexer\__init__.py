# init.py
import os, socket
from logging.config import dictConfig

from config.config import CommonConfig

cfg = CommonConfig()
log_folder = os.path.join(cfg.log_folder, socket.gethostname())

# Create the log folder if it doesn't exist
os.makedirs(log_folder, exist_ok=True)

dictConfig(
    {
        "version": 1,
        "formatters": {
            "default": {
                "format": "[%(asctime)s] {%(threadName)s %(module)s.%(funcName)s:%(lineno)d} %(levelname)s: %(message)s",
            }
        },
        "handlers": {
            "wsgi": {
                "class": "logging.StreamHandler",
                "stream": "ext://flask.logging.wsgi_errors_stream",
                "formatter": "default",
                "level": "INFO",
            },
            "file": {
                "class": "concurrent_log_handler.ConcurrentRotatingFileHandler",
                "formatter": "default",
                "filename": os.path.join(log_folder, "epro-bot.log"),
                "maxBytes": 50000000,
                "backupCount": 2,
            },
        },
        "loggers": {
            "": {"level": "INFO", "handlers": ["wsgi", "file"]},
            "models": {"level": "INFO"},
            "database": {"level": "INFO"},
            "embedders": {"level": "INFO"},
            "clients": {"level": "INFO"},
            "src": {"level": "INFO"},
            "rag": {"level": "INFO"},
        },
    }
)