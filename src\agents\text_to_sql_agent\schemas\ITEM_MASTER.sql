CREATE TABLE DWH_PUBLIC.ITEM_MASTER (
	COMPANY_CODE VARCHAR2(12 BYTE),
	COMPANY_DESCRIPTION VARCHAR2(100 BYTE),
	PLANT_CODE VARCHAR2(12 BYTE),
	PLANT_DESCRIPTION VARCHAR2(50 BYTE),
	ITEM_CODE VARCHAR2(25 BYTE),
	ITEM_INTERNAL_CODE VARCHAR2(25 BYTE),
	SHORT_ITEM_CODE VARCHAR2(12 BYTE),
	MAIN_ITEM_DESCRIPTION VARCHAR2(160 BYTE),
	ITEM_STOCKING_TYPE VARCHAR2(2 BYTE), -- Either "M" for "manufacture" or "B" for "buy"
	ECO_NUMBER VARCHAR2(16 BYTE),
	ITEM_PREFERRED_SUPPLIER VARCHAR2(20 BYTE),
	ITEM_UNIT_OF_MEASURE_CODE VARCHAR2(4 BYTE),
	ITEM_UNIT_OF_MEASURE_DESCRIPTION VARCHAR2(100 BYTE),
	START_PRODUCTION_DATE DATE,
	STOP_PRODUCTION_DATE DATE,
	COUNTRY_OF_ORIGIN VARCHAR2(6 BYTE), -- Accepts international vehicle registration codes only
	EXPIRING_DATE DATE, -- Country of origin expiration date
	PERCENTAGE_NOT_UE NUMBER(5, 0),
	FOOD_CONTACT VARCHAR2(2 BYTE),
	CURRENCY VARCHAR2(6 BYTE),
	STANDARD_STK3_COST NUMBER(15, 5), -- Cost of production/manufacturing
	PURCHASING_COST NUMBER(15, 5),
	EXTERNAL_WORK_COST NUMBER(15, 5),
	ITEM_CODE_VALID VARCHAR2(1 BYTE),
	SYSTEM_SOURCE VARCHAR2(10 BYTE),
	ITEM_SPARE_PARTS_FLAG VARCHAR2(1 BYTE),
	DUAL_USE VARCHAR2(2 BYTE),
	ITEM_TYPE VARCHAR2(2 BYTE),
	ITEM_WAREHOUSE VARCHAR2(1 BYTE),
	TECH_DRAWING NVARCHAR2(40),
	PRIMARY KEY (COMPANY_CODE, PLANT_CODE, ITEM_CODE, SYSTEM_SOURCE)
);