import React, { useState } from "react";
import { Stack, Checkbox, PrimaryButton } from "@fluentui/react";
import styles from  "../Answer.module.css"
import { MANUALS_MAPPING, DocumentTypeMap } from "../../../api";
import { Agents } from "../../../api";

interface Props {
    choices: any;
    callAgent: (choice: Agents, document_types: Array<string> | undefined) => void;
    
}

export const MultipleSelection = ({ choices, callAgent }: Props) => {
    const [selectedChoices, setSelectedChoices] = useState<string[]>([]);
    
    // Funzione per gestire il cambio dello stato delle checkbox
    const handleCheckboxChange = (choice: string) => {
            setSelectedChoices((prev) =>
                prev.includes(choice)
                    ? prev.filter((item) => item !== choice) // Deseleziona se già selezionato
                    : [...prev, choice] // Aggiungi se non selezionato
            );
        };

    const handleConfirmSelection = () => {
        callAgent(Agents.rag, selectedChoices);
    };

    return (
        <Stack tokens={{ childrenGap: 10 }} className={styles.checkboxGroup}>
            {choices.map((choice: DocumentTypeMap, index:number) => (
                <Checkbox 
                    key={index} 
                    label={Object.values(choice)[0]} 
                    className={styles.checkboxCustom}
                    checked={selectedChoices.includes(Object.keys(choice)[0])}
                    onChange={() => handleCheckboxChange(Object.keys(choice)[0])}
                    />
                ))}

                <Checkbox 
                    key={500}
                    label={"Other"} 
                    className={styles.checkboxCustom}
                    checked={selectedChoices.includes("Other")}
                    onChange={() => handleCheckboxChange("Other")}
                    />
            <PrimaryButton
                style={{ width: "40%" }}
                onClick={handleConfirmSelection}
                className={styles.choiceButton}
                text="Search"
            />
        </Stack>
    );
};