from typing import <PERSON><PERSON>
from sqlalchemy import and_, func, desc
from sqlalchemy.sql import over
from utils.clients import  DatabaseClient
from utils.core import get_logger
from utils.exceptions import DocumentNotFoundException
from src import db
from src.agents.rag_agent.models import DocumentStatusMapping, DistributionStatus, ProductionStatus
from src.backend.blueprints.rest.models.document_models import DocumentResponse
from src.agents.rag_agent.document_type_filter import DocumentType
from src.backend.models import CommercialProductDocument

logger = get_logger(__file__)


def retrieve_document(internal_code: str, product_number: str, document_type: str, document_number: str, document_edition: str, language_id: int, id_file: int ) -> dict:
    logger.debug(f'Fetching documents...')

    #ulteriori parametri che possono essere implementati
    document_number = None
    production_status = None
    distribution_status = None

    db_client = DatabaseClient()

    try:
        product_code = internal_code if internal_code else product_number

        file_path = get_document_and_metadata(product_code, document_type, document_edition, document_number, language_id, id_file, production_status, distribution_status, db_client)
        db_client.shutdown()

        return file_path[1][0]

    except DocumentNotFoundException as e:
        logger.error(e.message)


def get_document_and_metadata(product_code: str, document_type: str, edition: str, document_number: str, language_id: int, id_file: int, production_status: str, distribution_status: str, db_client: DatabaseClient) -> Tuple:
    documents_fetched = _attempt_document_search_fallbacks(
        product_code, document_type, edition, document_number, production_status, distribution_status,
        [(str(language_id), "requested language"), ("1", "English"), ("100", "Multilingual")],
        id_file,
        db_client
    )

    if not documents_fetched or len(documents_fetched) == 1:
        raise DocumentNotFoundException(
            f"Sorry, the document for product you are asking for is not found in the database. "
            f"Please check if the product code provided is correct."
        )

    return documents_fetched


def _attempt_document_search_fallbacks( product_code: str, document_type: str, edition: str, document_number: str, production_status: str, distribution_status: str, filters: Tuple[str,str], id_file: int, db_client: DatabaseClient):
        """
        Attempt to fetch a document using a sequence of language filters.

        :param product_code: str, product identifier.
        :param document_type: str, type of document.
        :param edition: str, specific edition requested (can be None).
        :param document_number: str, document number for query.
        :param filters: list of tuples (filter_query, description)
        :return: tuple (edition, language) or (None, None) if not found.
        """
        for language_filter_query, description in filters:
            query = build_language_query(language_filter_query, product_code, document_type, edition, document_number, production_status, distribution_status, id_file)
            documents = db_client.execute(query, numrows=None)
            if documents and len(documents) > 1:
                return documents # Return edition and language

        return None  # Return nothing if all fallbacks fail


def build_language_query(language_filter, product_code, document_type, edition = None, document_number = None, production_status = None, distribution_status = None, id_file = None):

        document_type_filter = build_document_type_filter(document_type)
        production_distribution_filter = build_production_distribution_filter(production_status, distribution_status)

        query_db = f"""SELECT FILE_PATH FROM DWH_PUBLIC.COMMERCIAL_PRODUCTS_DOCUMENTS
                       WHERE {f"(INTERNAL_CODE = '{product_code}' OR PRODUCT_NUMBER = '{product_code}' OR FACTORY_MODEL = '{product_code}')" if product_code.lower() != "missing" else f"DOCUMENT_NUMBER='{document_number}'"}
                       {document_type_filter}
                       {production_distribution_filter}
                       {f"AND DOCUMENT_EDITION='{edition}'" if edition else ''}
                       {f"AND ID_FILE='{id_file}'" if id_file else ''}
                       AND (LANGUAGE = '{language_filter}' OR LANGUAGE = '100')
                       {f"ORDER BY CAST(DOCUMENT_EDITION AS DECIMAL(10,2))  DESC" if document_type != 'SPC' else f"ORDER BY DOCUMENT_DATE DESC"} """ #negli spare part catalog, l'edizione Ã¨ formattata in modo diverso, perciÃ² va gestito ordinando non per edizione ma per data di documento.

        return query_db


def build_document_type_filter(document_type: str) -> str:

        document_type_supported = DocumentType[document_type]
        document_type_supported_list = ",".join([f"'{element}'" for element in document_type_supported])
        document_type_filter = f"AND DOCUMENT_TYPE IN ({document_type_supported_list})"

        return document_type_filter


def build_production_distribution_filter(production_status, distribution_status) -> str:

        def no_one_status() -> bool:
            return not production_status and not distribution_status

        def only_production_status() -> bool:
            return production_status and not distribution_status

        def only_distribution_status() -> bool:
            return not production_status and distribution_status

        def both_status() -> bool:
            return production_status and distribution_status


        if no_one_status():
            return ''
        if only_production_status():
            production_status_code = ProductionStatus.from_code(production_status).description
            if production_status_code == ProductionStatus.IN_PRODUCTION.name:
                production_distribution_filter = f"AND DOCUMENT_STATUS IN ('{DocumentStatusMapping.from_code(1).code}','{DocumentStatusMapping.from_code(3).code}')"
            else:
                production_distribution_filter = f"AND DOCUMENT_STATUS IN ('{DocumentStatusMapping.from_code(2).code}','{DocumentStatusMapping.from_code(4).code}')"
        if only_distribution_status():
            distribution_status_code = DistributionStatus.from_code(distribution_status).description
            if distribution_status_code == DistributionStatus.IN_DISTRIBUTION.name:
                production_distribution_filter = f"AND DOCUMENT_STATUS IN ('{DocumentStatusMapping.from_code(1).code}','{DocumentStatusMapping.from_code(4).code}')"
            else:
                production_distribution_filter = f"AND DOCUMENT_STATUS IN ('{DocumentStatusMapping.from_code(2).code}','{DocumentStatusMapping.from_code(3).code}')"
        if both_status():
            production_status_code = str(ProductionStatus.from_code(production_status).code)
            distribution_status_code = str(DistributionStatus.from_code(distribution_status).code)

            status_map = production_status_code + distribution_status_code

            document_status_code_mapped = DocumentStatusMapping.from_map(status_map).code
            production_distribution_filter = f"AND DOCUMENT_STATUS = '{document_status_code_mapped}'"

        return production_distribution_filter


def get_document_list(internal_code: str = None, product_number: str = None, factory_model: str = None, **filters):

    where_conditions = create_where_conditions(internal_code, product_number, factory_model, filters)
    # if filters["document_edition_filter"] == "latest":
    #      return get_latest_edition_document_list(internal_code, product_number, factory_model, where_conditions)
    # else:
    document_list = CommercialProductDocument.query.filter(and_(*where_conditions)).order_by(CommercialProductDocument.DOCUMENT_DATE).distinct().all()
    return [DocumentResponse.create(document.INTERNAL_CODE,
                                    document.PRODUCT_NUMBER,
                                    document.FACTORY_MODEL,
                                    document.FILE_PATH,
                                    document.DOCUMENT_DESCR,
                                    document.DOCUMENT_TYPE,
                                    document.DOCUMENT_NUMBER,
                                    document.DOCUMENT_EDITION,
                                    document.LANGUAGE,
                                    document.DOCUMENT_DATE,
                                    document.DOCUMENT_STATUS,
                                    document.ID_FILE) for document in document_list]


def get_latest_edition_document_list(internal_code: str = None, product_number: str = None, factory_model: str = None,where_conditions: list = []):

    if internal_code:
        partition_code = CommercialProductDocument.INTERNAL_CODE
    elif product_number:
        partition_code = CommercialProductDocument.PRODUCT_NUMBER
    else:
        partition_code = CommercialProductDocument.FACTORY_MODEL

    row_ordered_by_edition = over(func.row_number(), partition_by=[partition_code, CommercialProductDocument.DOCUMENT_TYPE, CommercialProductDocument.LANGUAGE],
                                        order_by=[desc(CommercialProductDocument.DOCUMENT_DATE), desc(CommercialProductDocument.DOCUMENT_EDITION)]).label("rn")

    subquery = CommercialProductDocument.query.with_entities(CommercialProductDocument.INTERNAL_CODE,
                                                             CommercialProductDocument.PRODUCT_NUMBER,
                                                             CommercialProductDocument.FACTORY_MODEL,
                                                             CommercialProductDocument.LANGUAGE,
                                                             CommercialProductDocument.DOCUMENT_TYPE,
                                                             CommercialProductDocument.DOCUMENT_DESCR,
                                                             CommercialProductDocument.DOCUMENT_EDITION,
                                                             CommercialProductDocument.FILE_PATH,
                                                             row_ordered_by_edition).filter(and_(*where_conditions)).subquery()

    query = db.session.query(subquery).filter(subquery.c.rn == 1)
    document_list = query.all()

    return [ DocumentResponse.create(row[0], row[1], row[2], row[7], row[5], row[4], row[6], row[3]) for row in document_list]


def create_where_conditions(internal_code: str, product_number: str, factory_model: str, filters: dict):
    conditions = []

    if internal_code:
        conditions.append(CommercialProductDocument.INTERNAL_CODE == internal_code)
    elif product_number:
        conditions.append(CommercialProductDocument.PRODUCT_NUMBER == product_number)
    else:
        conditions.append(CommercialProductDocument.FACTORY_MODEL == factory_model)

    document_type_list = filters["document_type_filter"]
    document_type_filter = []

    for document_type in document_type_list:
         document_type_filter.extend(DocumentType[document_type])

    document_type_filter = list(set(document_type_filter))
    
    language_id_filter = filters["language_id_filter"]
    document_edition_filter = filters["document_edition_filter"]
    document_status_filter = filters["document_status_filter"]
    document_number_filter = filters["document_number_filter"]

    if document_type_filter is not None:
         conditions.append(CommercialProductDocument.DOCUMENT_TYPE.in_(document_type_filter))
    if language_id_filter is not None:
         conditions.append(CommercialProductDocument.LANGUAGE.in_(language_id_filter))
    if document_edition_filter is not None and  document_edition_filter != "latest":
         conditions.append(CommercialProductDocument.DOCUMENT_EDITION == document_edition_filter)
    if document_status_filter is not None:
         conditions.append(CommercialProductDocument.DOCUMENT_STATUS.in_(document_status_filter))
    if document_number_filter is not None:
         conditions.append(CommercialProductDocument.DOCUMENT_NUMBER == document_number_filter)

    return conditions