from enum import Enum
from typing import Callable

class DocumentType(list, Enum):
    SM = ['SM', 'SMA']
    IN = ['IN']
    SPC = ['SPC']
    TB = ['TBELS', 'TI', 'TB']
    HB = ['HB']
    II = ['II', 'IN']
    CPM = ['CPM', 'CPMA']
    PDS = ['PDS']
    SPPDS = ['SPPDS']
    SPPH = ['SPPH']
    SPOCS = ['SPOCS']
    OM = ['OM']
    OI = ['OI']
    GUI = ['GUI', 'GUL']
    DC = ['DC', 'DOC']
    UM = ['UM', 'UMG']
    COC = ['COC']
    PM = ['PM']
    TEVI = ['TEVI', 'TVID']
    SPGP = ['SPGP']
    SPVID = ['SPVID']
    SPIK = ['SPIK']
    IS = ['IS', 'II', 'OI', 'IN']
    WI = ['WI']
    PRF = ['PRF']
    EWD = ['EWD', 'PWD']
    PH = ['PH']

class DocumentTypeNameMapping(Enum):
    SM = 'Service Manual'
    IN = 'Installation Manual'
    SPC = 'Spare Part Catalog'
    HB = 'Handbooks'
    TB = 'Technical Bulletin'
    II = 'Installation Instruction'
    PDS = 'Product Data Sheet'
    SPPDS = 'Spare Part Product Data Sheet'
    SPPH = 'Spare Part Photo'
    SPOCS = 'Spare Part Original Component Sticker'
    OM = 'Operating Manual'
    OI = 'Operating Instruction'
    CPM = 'Commissioning & Performance Maintenance'
    UM = 'User Maintenance'
    COC = 'Conformity Certificates'
    DC = 'Conformity Declaration'
    PM = 'Programming Manual'
    TEVI = 'Technical Videos'
    SPGP = 'Spare Part General Purpose'
    SPI = 'Spare Part Instruction'
    SPVID = 'Spare Part Technical Videos'
    SPIK = 'Spare Parts Instructions for Kit'
    IS = 'Instructions'
    GUI = 'Guidelines'
    WI = 'Wall Instruction'
    PRF = 'Programming File'
    EWD = 'Electrical Wiring Diagram'
      

def sm_filter() -> str:
    return " and (document_type eq 'SM' or document_type eq 'SMA')"

def tb_filter() -> str:
    return " and (document_type eq 'TB' or document_type eq 'TI' or document_type eq 'TBELS')"

def hb_filter() -> str:
    return " and document_type eq 'HB'"

def cpm_filter() -> str:
    return " and (document_type eq 'CPM' or document_type eq 'CPMA')"

def gui_filter() -> str:
    return " and (document_type eq 'GUI' or document_type eq 'GUL')"

def dc_filter() -> str:
    return " and (document_type eq 'DC' or document_type eq 'DOC')"

def um_filter() -> str:
    return " and (document_type eq 'UM' or document_type eq 'UMG')"

def tevi_filter() -> str:
    return " and (document_type eq 'TEVI' or document_type eq 'TVID')"

def is_filter() -> str:
    return " and (document_type eq 'IS' or document_type eq 'II' or document_type eq 'OI' or document_type eq 'IN')"

def ii_filter() -> str:
    return " and (document_type eq 'II' or document_type eq 'IN')"

def ewd_filter() -> str:
    return " and (document_type eq 'EWD' or document_type eq 'PWD')"

def default_filter(document_type: str) -> str:
    return f" and document_type eq '{document_type}'"


filter_strategies: dict[str, Callable[[], str]] = {
    DocumentType.SM.name: sm_filter,
    DocumentType.TB.name: tb_filter,
    DocumentType.HB.name: hb_filter,
    DocumentType.CPM.name: cpm_filter,
    DocumentType.GUI.name: gui_filter,
    DocumentType.DC.name: dc_filter,
    DocumentType.UM.name: um_filter,
    DocumentType.TEVI.name: tevi_filter,
    DocumentType.IS.name: is_filter,
    DocumentType.II.name: ii_filter,
    DocumentType.EWD.name: ewd_filter
}


def build_filter(document_type: str) -> str:
    strategy = filter_strategies.get(document_type, lambda: default_filter(document_type))
    return strategy()

def reverse_mapping_document_type(document_type: str) -> str:

    for doc_type in DocumentType:
        if document_type in doc_type.value:
            return doc_type.name
    
    #Se non viene trovato nessun document type nella mappatura, ritorna stringa vuota
    return ''