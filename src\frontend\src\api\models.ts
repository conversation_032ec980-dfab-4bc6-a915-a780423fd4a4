export const enum ApproachType {
    Structured = "structured",
    Unstructured = "unstructured",
    ChitChat = "chit_chat"
}

export const enum FeedbackType {
    Good = "GOOD",
    Bad = "BAD",
}

export const enum Agents {
    rag = "rag",
    textToSql = "text_to_sql",
    ragDocument = "rag_document",
    eproexcella = "eproexcella"
}

export const enum Bots {
    compliBot = "Complibot",
    howToBot = "Application How-To",
    callCenterBot = "Call Center Bot",
    eproexcella = "EproExcelLa Bot"
}

export type ChatRequestOverrides = {
    showExplanation?: boolean;
    showSQL?: boolean;
    top?: number;
    temperature?: number;
    suggestFollowupQuestions?: boolean;
};

export type DocumentTypeMap = {
  [key: string]: string;
};

export const MANUALS_MAPPING:Record<string, string> = {
    SM: 'Service Manual',
    IN: 'Installation Manual',
    SPC: 'Spare Part Catalog',
    HB: 'Handbooks',
    TB: 'Technical Bulletin',
    II: 'Installation Instruction',
    PDS: 'Product Data Sheet',
    SPPDS: 'Spare Part Product Data Sheet',
    SPPH: 'Spare Part Photo',
    SPOCS: 'Spare Part Original Component Sticker',
    OM: 'Operating Manual',
    OI: 'Operating Instruction',
    CPM: 'Commissioning & Performance Maintenance',
    UM: 'User Maintenance',
    COC: 'Conformity Certificates',
    DC: 'Conformity Declaration',
    PM: 'Programming Manual',
    TEVI: 'Technical Videos',
    SPGP: 'Spare Part General Purpose',
    SPI: 'Spare Part Instruction',
    SPVID: 'Spare Part Technical Videos',
    SPIK: 'Spare Parts Instructions for Kit',
    IS: 'Instructions', // (Do not confuse with other types of instructions.)
    GUI: 'Guidelines',
    WI: 'Wall Instruction',
    PRF: 'Programming File',
    EWD: 'Electrical Wiring Diagram'
  };

interface DialogRequest {
    userID: string;
    conversationID: string;
    dialogID: string;
}

export interface ChatRequest extends DialogRequest {
    dialog: string;
    overrides?: ChatRequestOverrides;
    preview: boolean;
    agent_to_call?: Agents
    document_types?: Array<string>
}

export interface FeedbackRequest extends DialogRequest {
    feedback: FeedbackType;
}

export type Answer = {
    formatted_answer: string;
    explanation?: string;
    source_file?: string;
    query_generation_prompt?: string;
    query?: string;
    query_result?: string;
    feedback?: FeedbackType;
    agent: string;
    answer_type?: string;
    document_types?: Array<string>
};

export type File = {
    name: string;
    path: string;
};

export type ChatResponse = {
    answer: Answer;
    classification?: ApproachType;
    question: string;
    data_points: string[];
    show_retry?: boolean;
    suggested_classification?: ApproachType;
    error?: string;
    files: File[];
    dialog_id: string;
    images?: string[];
    selection?: SelectionObj;
};

export type ChatStopResponse = {
    status?: string,
};

export type SelectionObj = {
    is_multiple: boolean,
    choices: DocumentTypeMap[] | string[]
};

export interface BotChangeRequest extends DialogRequest {
    botType: string,
};

export type BotChangeResponse = {
    bot?: string,
    status?: string,
};

export type UserProfile = {
    user_id: string;
    user_name: string;
    description: string;
    sample_questions?: string[];
};

export type ChatError = {
    retryable: boolean;
    message?: string;
};

export type UserQuestion = {
    question: string;
    classificationOverride?: ApproachType;
    agent_to_call?: Agents;
    document_types?: Array<string>
};

export type SearchSettings = {
    show_explanation: boolean,
    show_sql: boolean,
    suggest_followup_questions: boolean,
    temperature: number,
    top: number,
    vectorization_enabled: boolean;
};

export const defaultSearchSettings: SearchSettings = {
    show_explanation: false,
    show_sql: false,
    suggest_followup_questions: false,
    temperature: 0.7,
    top: 10,
    vectorization_enabled: false
};

export type VersionDisplay  = {
    version: string;
    git: string;
    gui: string;
    bot: string;
    running_env: string;
};
