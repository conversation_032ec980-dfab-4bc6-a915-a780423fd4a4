"""question history columns index

Revision ID: d521331ff45e
Revises: c72b5e394aad
Create Date: 2024-05-21 22:55:02.299214

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'd521331ff45e'
down_revision = 'c72b5e394aad'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('question_history', schema=None) as batch_op:
        batch_op.drop_index('some_index')
        batch_op.create_index('QuestionHistory_index', ['user_id', 'conversation_id', 'dialog_id'], unique=False)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('question_history', schema=None) as batch_op:
        batch_op.drop_index('QuestionHistory_index')
        batch_op.create_index('some_index', ['user_id', 'conversation_id', 'dialog_id'], unique=False)

    # ### end Alembic commands ###
