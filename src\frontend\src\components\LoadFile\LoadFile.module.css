.clickToUpload {
    font-size: 1.25rem;
    font-weight: 600;
    margin-top: 20px;
    margin-bottom: 40px;
    text-align: center;
}

.uploadSVG {
    height: 100px;
    width: 100px;
    display: block;
    margin: auto;
}

.fileType {
    font-weight: 500;
    font-size: 1rem;
    margin-top: 0;
    margin-bottom: 20px;
    text-align: center;
    color:rgba(222, 8, 8, 0.684);
}

input[type="file"] {
    position: relative;
}

input[type="file"]::file-selector-button {
    width: 130px;
    color: transparent;
}

/* Faked label styles and icon */
input[type="file"]::before {
    position: absolute;
    pointer-events: none;
    top: 10px;
    left: 16px;
    height: 20px;
    width: 20px;
    content: "";
    background-image: url(../../assets/uploadIcon.svg);
}

input[type="file"]::after {
    position: absolute;
    pointer-events: none;
    top: 11px;
    left: 40px;
    color: white;
    content: "Upload File";
}

/* ------- From Step 1 ------- */

/* file upload button */
input[type="file"]::file-selector-button {
    border-radius: 4px;
    padding: 0 16px;
    height: 40px;
    cursor: pointer;
    background-color: rgba(115, 118, 225, 255);
    border: 1px solid rgba(0, 0, 0, 0.16);
    box-shadow: 0px 1px 0px rgba(0, 0, 0, 0.05);
    margin-right: 16px;
    transition: background-color 200ms;
}

/* file upload button hover state */
input[type="file"]::file-selector-button:hover {
    background-color: #f3f4f6;
}

/* file upload button active state */
input[type="file"]::file-selector-button:active {
    background-color: #e5e7eb;
}


input[type="submit"] {
    position: relative;
}

/* ------- From Step 1 ------- */

/* file upload button */
input[type="submit"] {
    display: block;
    margin: auto;
    border-radius: 4px;
    margin-top: 10px;
    padding: 0 16px;
    height: 40px;
    cursor: pointer;
    background-color: rgba(115, 118, 225, 255);
    border: 1px solid rgba(0, 0, 0, 0.16);
    box-shadow: 0px 1px 0px rgba(0, 0, 0, 0.05);

    color: white;
    transition: background-color 200ms;
}

/* file upload button hover state */
input[type="submit"]:hover {
    background-color: #f3f4f6;
}

/* file upload button active state */
input[type="submit"]:active {
    background-color: #e5e7eb;
}

.styled_table {
    width: 100%;
    border-collapse: collapse;
    margin: 25px 0;
    font-size: 14px;
    text-align: left;
}

.styled_table thead tr {
    background-color: rgba(115, 118, 225, 255);
    color: #ffffff;
    text-align: center;
}

.styled_table th,
.styled_table td {
    padding: 12px 15px;
}

.styled_table tbody tr {
    border-bottom: 1px solid #e5e7eb;
}

.styled_table tbody tr:nth-of-type(even) {
    background-color: rgba(115, 118, 225, 0.1);
}

.styled_table tbody tr:last-of-type {
    border-bottom: 2px solid rgba(115, 118, 225, 255);
}

.styled_table tbody tr.active-row {
    font-weight: bold;
    color: rgba(115, 118, 225, 255);
}

.popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
}

.popup_inner {
    background: #f0f0f0; /* Cambia questo colore con quello che preferisci */
    padding: 20px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    width: 300px; /* Riduci la larghezza del pop-up */
}

.popup_inner h2 {
    margin-top: 0;
}

.popup_inner button {
    margin-top: 20px;
    padding: 10px 20px;
    background-color: #007bff; /* Colore del pulsante */
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}

.popup_inner button:hover {
    background-color: #0056b3; /* Colore del pulsante al passaggio del mouse */
}
