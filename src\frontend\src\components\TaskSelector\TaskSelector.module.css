.container {
  display: flex;
  flex-direction: column;
  justify-content: center; /* Centra i bottoni inizialmente */
  align-items: center;
}

.buttonContainer {
  display: flex;
  flex-direction: row; /* Dispone i bottoni in riga */
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  width: 100%; /* Assicura che il contenitore occupi tutta la larghezza disponibile */
  text-align: center; /* Centra il testo all'interno del contenitore */
}

.button {
  background-color: white; /* Sfondo bianco */
  border: none;
  color: rgba(115, 118, 225, 255); /* Colore del testo */
  padding: 20px;
  text-align: center;
  text-decoration: none;
  font-size: 16px;
  margin: 10px;
  transition-duration: 0.4s;
  cursor: pointer;
  border-radius: 12px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); /* Ombreggiatura */
  width: 200px; /* Larghezza fissa per uniformità */
}

.button:hover {
  background-color: rgba(115, 118, 225, 255); /* Colore di sfondo al passaggio del mouse */
  color: white; /* Colore del testo al passaggio del mouse */
  border: 2px solid rgba(115, 118, 225, 255);
}

.button.selected {
  background-color: rgba(115, 118, 225, 255);
  color: white;
  border: 1px solid rgba(115, 118, 225, 255);
  width: 150px;
  padding: 5px; /* Riduce il padding dei bottoni */
  margin: 5px; /* Aggiunge margine tra i bottoni */
  border-radius: 20px;
}

.button.small {
  width: 150px; /* Riduce ulteriormente la larghezza dei bottoni */
  padding: 5px; /* Riduce il padding dei bottoni */
  margin: 5px; /* Aggiunge margine tra i bottoni */
  border-radius: 20px;
}

h2 {
  margin: 0;
  font-size: 18px;
}

p {
  margin: 5px 0 0;
  font-size: 14px;
}

.editableContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 20px;
}

.editableContainer p,
.editableContainer input {
  margin: 0;
  font-size: 16px;
  width: 80%; /* Limita la larghezza del paragrafo e dell'input */
  text-align: center; /* Centra il testo */
  word-wrap: break-word; /* Gestisce la rottura delle parole */
  box-sizing: border-box; /* Include padding e bordo nella larghezza totale */
}

.editableContainer button {
  margin-top: 10px;
  padding: 5px 10px;
  background-color: rgba(115, 118, 225, 255);
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}

.textfield--wrapper {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.textfield--header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.textfield--input {
  padding: 10px;
  border-radius: 5px;
  font-size: 1rem;
}

.textfieldInput {
  width: 300px; /* Imposta la larghezza dell'input */
  height: 40px; /* Imposta l'altezza dell'input */
  font-size: 16px;
  padding: 10px;
  box-sizing: border-box;
  border: 1px solid #ccc;
  border-radius: 5px;
}
