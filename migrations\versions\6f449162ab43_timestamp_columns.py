"""timestamp columns

Revision ID: 6f449162ab43
Revises: d521331ff45e
Create Date: 2024-05-24 23:32:47.769049

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '6f449162ab43'
down_revision = 'd521331ff45e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('question_history', schema=None) as batch_op:
        batch_op.add_column(sa.Column('created', sa.DateTime(), nullable=False))
        batch_op.add_column(sa.Column('updated', sa.DateTime(), nullable=False))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('question_history', schema=None) as batch_op:
        batch_op.drop_column('updated')
        batch_op.drop_column('created')

    # ### end Alembic commands ###
