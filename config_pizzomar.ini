[txt2sql.oracle_oci]
oci_client_path = C:\DBEAVER_OCI\instantclient-basic-windows.x64-21.18.0.0.0dbru\instantclient_21_18
oci_dsn = dwhtest_high

[db]
db_driver = ODBC Driver 18 for SQL Server

[rag.parameters]
embedding_resource = https://epr-openai-sandbox-plus.openai.azure.com/
embedding_model = embedding-test-rag

[common]
run_mode = TEST
logout_url = https://electroluxprofessional.unily.com

[log]
loglevel = DEBUG
log_folder = .

[ad]
ad_schema_callback=http

# EPROExcelLa Translation Bot configuration
[translator.azure_ai_api]
api_version = 2024-02-01
api_llm_endpoint = https://epr-openai-sandbox-plus.openai.azure.com/
llm_deployed = gpt-35-turbo
api_translator_endpoint = https://api.cognitive.microsofttranslator.com/