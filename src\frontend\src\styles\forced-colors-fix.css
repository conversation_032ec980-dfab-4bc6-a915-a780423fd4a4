/* Fix for -ms-high-contrast deprecation warnings */
/* Replace old -ms-high-contrast with modern forced-colors */

@media (forced-colors: active) {
  /* Styles for forced colors mode (replaces -ms-high-contrast) */
  .btn,
  .form-control,
  .card {
    forced-color-adjust: auto;
  }
  
  .text-muted {
    color: GrayText !important;
  }
  
  .bg-primary,
  .btn-primary {
    background-color: ButtonFace !important;
    border-color: ButtonText !important;
    color: ButtonText !important;
  }
}

/* Legacy support for older browsers */
@media (-ms-high-contrast: active) {
  .btn,
  .form-control,
  .card {
    border: 1px solid;
  }
}
