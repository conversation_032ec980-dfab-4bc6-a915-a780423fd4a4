from flask import current_app, jsonify

from config.config import PrimaryConfig


def stream_last_n_releases(changelog_path, n=2):
    releases = []
    current_release = []
    release_count = 0

    with open(changelog_path, 'r', encoding='utf-8') as file:
        for line in file:
            if line.startswith('## ['):  # Inizio di una release
                if current_release:
                    releases.append(''.join(current_release).strip())
                    release_count += 1
                    if release_count > n:
                        break
                    current_release = []
            current_release.append(line)

        # Aggiungi l’ultima release se non è già stata aggiunta
        if release_count < n and current_release:
            releases.append(''.join(current_release).strip())

    return '\n\n'.join(releases)


def get_env_version():
    primary_cfg = PrimaryConfig()

    cfg = current_app.config["AI_CONFIG"]
    return jsonify(
        git=cfg.default_version_release,
        running_env=primary_cfg.env
    )
