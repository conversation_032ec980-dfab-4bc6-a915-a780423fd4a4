from sqlalchemy.orm.attributes import flag_modified

from src import db
from src.agents.answer import Answer
from src.backend.contracts.chat_data import (
    AnswerFeedback,
    BotType,
    ChatExtraDetails,
    ChatQuestion,
)
from src.backend.models import QuestionHistory, User
from utils.core import get_logger

logger = get_logger(__file__)


def update_search_settings(db_session, user: User, new_settings: dict):

    # Check if user_settings exists, create if not
    updated_settings = user.user_settings
    if updated_settings is None:
        updated_settings = {}

    if "search_settings" not in updated_settings:
        updated_settings["search_settings"] = {}

    updated_settings["search_settings"].update(new_settings)  # Merge dictionaries
    # Mark JSON field ad "modified" to allow raw saving
    flag_modified(user, "user_settings")
    user.user_settings = updated_settings

    db_session.commit()


def user_get_allowed_bots(user: User):
    try:
        user_settings = user.user_settings
        bots_allowed = user_settings["allowed_bots"]
        return [BotType[bot] for bot in bots_allowed]
    except Exception as e:
        print(e)
        logger.error(f"No bot configured for user {user.email}")
        return []


def update_or_create_question_history(
    user_id,
    conversation_id,
    dialog_id,
    question: ChatQuestion = None,
    raw_response: Answer = None,
    extra_details: ChatExtraDetails = None,
):
    """
    Updates an existing QuestionHistory record or creates a new one based on user_id, conversation_id and dialog_id.

    Args:
        user_id (int): The user ID.
        conversation_id (str): The conversation ID.
        dialog_id (str): The dialog ID.
        question (JSON, optional): The question data. Defaults to None.
        raw_response (JSON, optional): The raw reponse data. Defaults to None.
        extra_details (JSON, optional): Extra details like thumbs up/down. Defaults to None.

    Returns:
        QuestionHistory: The created or updated QuestionHistory record.
    """

    # Try to find the existing record
    question_history = QuestionHistory.query.filter_by(
        user_id=user_id, conversation_id=conversation_id, dialog_id=dialog_id
    ).first()

    if question_history:
        # Update existing record
        question_history.question = question
        question_history.response = raw_response
        question_history.extra_details = extra_details
    else:
        # Create a new record
        question_history = QuestionHistory(
            user_id=user_id,
            conversation_id=conversation_id,
            dialog_id=dialog_id,
            question=question,
            response=raw_response,
            extra_details=extra_details,
        )

    # Save changes
    db.session.add(question_history)
    db.session.commit()

    return question_history


def update_feedback(user_id, conversation_id, dialog_id, feedback: AnswerFeedback):
    """
    Updates a Feedback in an existing QuestionHistory record based on user_id, conversation_id and dialog_id.

    Args:
        user_id (int): The user ID.
        conversation_id (str): The conversation ID.
        dialog_id (str): The dialog ID.
        feedback (AnswerFeedback): The feedback result

    Returns:
        QuestionHistory: The updated QuestionHistory record.
    """

    # Try to find the existing record
    question_history = QuestionHistory.query.filter_by(
        user_id=user_id, conversation_id=conversation_id, dialog_id=dialog_id
    ).first()

    if question_history:
        # Update existing record
        question_history.extra_details["feedback"] = feedback
        flag_modified(question_history, "extra_details")

        db.session.commit()
    else:
        logger.info(
            f"Unable to save feedback for cid->{conversation_id} did->{dialog_id}"
        )

    return question_history


def update_history(stream_answer:bool, reply: dict, submitted_text: str, question_status: str, extra_details: dict, user: dict , conversation_id: str, dialog_id:str, app ):

    with app.app_context():
        if stream_answer:
            reply.data = "".join(reply.data)

        update_or_create_question_history(
            user["id"],
            conversation_id,
            dialog_id,
            question=ChatQuestion(question=submitted_text, question_status=question_status).to_json(),
            raw_response=reply.to_json() if isinstance(reply, Answer) else reply,
            extra_details=extra_details.to_json()
        ) 