import argparse
from json import load
from os.path import dirname, join
from typing import Dict, List

import numpy as np
import sqlparse

from utils.core import <PERSON><PERSON>, get_logger

logger = get_logger(__file__)


class SQLStatementException(Exception):
    pass


class SqlEmbedder(metaclass=Singleton):

    def __init__(self, path: str) -> Dict[str, int]:
        """
        Initialise the embedder by loading the keyword dictionary from a json file located in `path` and returs the keyword dictionary, where each possible SQL query
        keyword is matched with an integer not-negative number.
        """
        # Read JSON file specified in `path`
        with open(path, "r") as file:
            logger.debug("Loading reserved keywords dictionary from JSON file...")
            self.dictionary = load(file)
        logger.info("Dictionary has been successfully loaded!")
        assert isinstance(
            self.dictionary, dict
        ), f"{self.dictionary} is not a dictionary."

    def embed(self, sql: str) -> List[int]:
        """
        Applies a binary word embedding to `query` where each keyword is mapped to a position of the returned array, based on the corresponding integer in `self.dictionary`
        and the corresponding binary values are: 0 if the keyword was not in `query` and 1 otherwise.
        `query` must contain only 1 SQL statement.
        """
        logger.debug(f"Embedding the following SQL query: {sql}...")

        # Initialises array with zeros in all positions
        binary_arr = np.zeros(len(self.dictionary))

        # Parsing the given query, ensuring keywords are in uppercase
        statements = sqlparse.parse(
            sqlparse.format(sql.replace("\n", " "), keyword_case="upper")
        )

        # Checking that `query` contains only 1 statement
        if len(statements) != 1:
            logger.critical(
                f"{len(statements)} SQL statements found. sql_embedder.embed embeds only 1 SQL statement. Exception raised"
            )
            raise SQLStatementException(
                f"{len(statements)} queries dectected. Please provide only one. Your input:\n{sql}"
            )

        logger.debug("1 statement has been found, beginning embedding...")

        # Iterating over each token in the parsed statement
        for token in statements[0].flatten():
            key = token.value

            if key in self.dictionary:
                # If `token` is a sql keyword, set the corresponding position in `binary_arr` as 1 to indicate its presence
                binary_arr[self.dictionary[key]] += 1

        binary_arr = binary_arr.astype(np.float32).tolist()

        logger.info(
            f"Created a {len(binary_arr)}D embedding containing {sum(binary_arr)} keywords."
        )
        return binary_arr


def get_args() -> argparse.Namespace:
    """
    Extract arguments from the CLI where this file is invoked.
    """
    parser = argparse.ArgumentParser(
        description="Embeds a string representing an SQL query into a binary (0/1) integer array."
    )
    parser.add_argument("query", type=str, help="A single SQL query")
    parser.add_argument(
        "-d",
        "--dictionary_path",
        dest="dict_path",
        default=join(dirname(__file__), "../../keywords_dictionary.json"),
        type=str,
        help="Path leading to a JSON file having SQL keywords as keys and numbers as values, such as each value i of the dictionary is between 0 and length(dictionary)-1",
    )
    return parser.parse_args()


if __name__ == "__main__":
    args = get_args()
    print(SqlEmbedder(args.dict_path).embed(args.query))
