from marshmallow import Schema, fields, validates, validates_schema, ValidationError
from src.agents.rag_agent.document_type_filter import DocumentType


class DocumentRequest(Schema):
    internal_code = fields.String(
        required=False,
        metadata={"description": "Product Code or 6-digit code", "example": "600679"}
    )
    product_number = fields.String(
        required=False,
        metadata={"description": "Product Number (PNC)", "example": "1502-001"}
    )
    document_type = fields.String(
        required=True,
        metadata={"description": "Document Type", "example": "IM"}
    )
    document_number = fields.String(
        required=False,
        metadata={"description": "PrIDE Document Number", "example": "595403K00"}
    )
    document_edition = fields.String(required=False)
    language_id = fields.Int(
        required=False,
        metadata={"description": "PrIDE language ID", "example": 1}
    )
    id_file = fields.Int(
        required=False,
        metadata={"description": "PrIDE File ID", "example": 3}
    )

    @validates_schema
    def validate_exactly_one_property(self, data, **kwargs):
        properties = ['internal_code', 'product_number']
        present_props = [prop for prop in properties if prop in data]

        if len(present_props) == 0:
            raise ValidationError("One of 'internal_code', 'product_number' must be provided.")

        if len(present_props) > 1:
            raise ValidationError(f"Properties are mutually exclusive. Found {len(present_props)}: {', '.join(present_props)}")

        return data

    @validates_schema
    def validate_document_type(self, data, **kwargs):
        if 'document_type' in data:
            valid_types = [doc_type.name for doc_type in DocumentType]
            if data['document_type'] not in valid_types:
                raise ValidationError(
                    {'document_type': f"Invalid document type. Must be one of: {', '.join(valid_types)}"}
                )
        return data

    @validates("language_id")
    def validate_language_id(self, value):
        if value is not None:
            if not (0 <= value <= 100):
                raise ValidationError("language_id is not valid")


class DocumentListRequest(Schema):
    internal_code = fields.String(required=False)
    product_number = fields.String(required=False)
    factory_model = fields.String(required=False)
    language_id = fields.List(fields.Int(), required=False)
    document_type = fields.List(fields.String(), required=False)
    document_edition = fields.String(required=False)
    document_status = fields.List(fields.Int(), required=False)
    document_number = fields.String(required=False)
    id_file = fields.Int(required=False)

    @validates_schema
    def check_identifiers(self, data, **kwargs):
        properties = ['internal_code', 'product_number', 'factory_model']
        present_props = [prop for prop in properties if prop in data]

        if len(present_props) == 0:
            raise ValidationError("One of 'internal_code', 'product_number', 'factory_model' must be provided.")

        if len(present_props) > 1:
            raise ValidationError(f"Properties are mutually exclusive. Found {len(present_props)}: {', '.join(present_props)}")

        return data

    @validates_schema
    def validate_document_type(self, data, **kwargs):
        if 'document_type' in data:
            valid_types = [doc_type.name for doc_type in DocumentType]
            for doc_type in data['document_type']:
                if doc_type not in valid_types:
                    raise ValidationError(
                        {'document_type': f"Invalid document type. Must be one of: {', '.join(valid_types)}"}
                    )
        return data

    @validates("language_id")
    def validate_language_id(self, value):
        if value is not None:
            for lang_id in value:
                if not (0 <= lang_id <= 200):
                    raise ValidationError("language_id is not valid")

    @validates("document_status")
    def validate_document_status(self,value):
        if value is not None:
            for status in value:
                if not (0<= status <= 4):
                    raise ValidationError(f"document status must be one of: {', '.join([str(i) for i in range(1,5)])}")


class ErrorResponse(Schema):
    status = fields.String(required=True, description="Status of the request", example="error")
    type = fields.String(required=True)
    message = fields.Dict(required=True, description="Error messages")

    @classmethod
    def create(cls, error_type, message):
        data = {"status": "error", "type": error_type, "message": message}
        return cls().dump(data)


class DocumentResponse(Schema):
    internal_code = fields.Str(description="Product Code or 6-digit code")
    product_number = fields.Str(description="Product Number (PNC)")
    document_type = fields.Str(description="Document type")
    document_number = fields.Str(description="Document number")
    document_edition = fields.Str(description="Document edition")
    file_name = fields.Str(description="Internal filename")
    document_descr = fields.Str(description="Document description")
    factory_model = fields.Str(description="Factory model")
    language_id = fields.Int(description="PrIDE Language ID")
    document_date = fields.DateTime(description="Document upload date")
    id_file = fields.Int(description="PrIDE File ID")
    in_production = fields.Bool(description="True if is in production status")
    in_distribution = fields.Bool(description="True if is in distribution status")

    @classmethod
    def create(cls, internal_code, product_number, factory_model, file_name, document_descr, document_type, document_number, document_edition, language_id, document_date, document_status, id_file):

        in_production, in_distribution = cls.get_document_status_flag(document_status)

        data = {
            'file_name': file_name,
            'document_descr': document_descr,
            'internal_code': internal_code,
            'product_number': product_number,
            'factory_model': factory_model,
            'document_type': document_type,
            'document_number': document_number,
            'document_edition': document_edition,
            'language_id': language_id,
            'id_file': id_file,
            'document_date': document_date,
            'in_production': in_production,
            'in_distribution': in_distribution
        }
        return cls().dump(data)

    @classmethod
    def get_document_status_flag(cls,document_status):

        if document_status == 1:
            in_production = True
            in_distribution = True
        elif document_status == 2:
            in_production = False,
            in_distribution = False
        elif document_status == 3:
            in_production = True
            in_distribution = False
        elif document_status == 4:
            in_production = False,
            in_distribution = True

        return in_production, in_distribution

class DocumentListResponse(Schema):
    document_list = fields.List(fields.Nested(DocumentResponse), description="Document list")

    @classmethod
    def create(cls):
        """Crea una nuova istanza vuota di DocumentListResponse"""
        data = {'document_list': []}
        return cls().dump(data)

    @classmethod
    def add_document(cls, response_data, document):
        """Aggiunge un documento alla lista esistente"""
        if not isinstance(response_data, dict):
            response_data = cls().load(response_data)

        if 'document_list' not in response_data:
            response_data['document_list'] = []

        response_data['document_list'].append(document)
        return cls().dump(response_data)