image: semgrep/semgrep:latest

definitions:
  caches:
      semgrep-cache: ~/.cache/semgrep
  services:
    docker:
      memory: 3072 # Memory in MB 
      
pipelines:
  branches:
    # Change to your default branch if different from main
    main:
    - step:
        name: Semgrep scan on push
        size: 2x
        script:
          - export SEMGREP_APP_TOKEN=$SEMGREP_APP_TOKEN
          - semgrep ci  --max-target-bytes 10000 --exclude='*/test/*' --exclude='Testutils/*' --exclude='*.webp' --exclude='*.png' --exclude='*.svg' --exclude='*.woff' --exclude='*.woff2' --exclude='*.jpg' --exclude='*.glb' --exclude='*.pdf' --exclude='*.md' --exclude='*.xml' --exclude='*.pro' --verbose --jobs=1


  pull-requests:
    '**': # This applies to pull requests for all branches
      - step:
          name: Semgrep scan on PR
          services: [docker]
          caches:
            - semgrep-cache
          script:
            - export SEMGREP_APP_TOKEN=$SEMGREP_APP_TOKEN
            - export BITBUCKET_TOKEN=$PAT # Necessary for PR comments
            # Change to your default branch if different from main
            - export SEMGREP_BASELINE_REF="origin/main"
            - free -m
            - git fetch origin "+refs/heads/*:refs/remotes/origin/*"
            - free -m
            - semgrep ci  --max-target-bytes 10000 --exclude='*/test/*' --exclude='Testutils/*' --exclude='*.webp' --exclude='*.png' --exclude='*.svg' --exclude='*.woff' --exclude='*.woff2' --exclude='*.jpg' --exclude='*.glb' --exclude='*.pdf' --exclude='*.md' --exclude='*.xml' --exclude='*.pro' --verbose --jobs=1
            - free -m

  custom:
  # Trigger job manually. For cron in Bitbucket, see: https://support.atlassian.com/bitbucket-cloud/docs/pipeline-triggers/#On-schedule
    semgrep-manual:
      - step:
          name: Semgrep manual scan
          size: 2x
          script:
            - export SEMGREP_APP_TOKEN=$SEMGREP_APP_TOKEN
            - echo $BITBUCKET_BRANCH
            - free -m
            - semgrep ci  --max-target-bytes 10000 --exclude='*/test/*' --exclude='Testutils/*' --exclude='*.webp' --exclude='*.png' --exclude='*.svg' --exclude='*.woff' --exclude='*.woff2' --exclude='*.jpg' --exclude='*.glb' --exclude='*.pdf' --exclude='*.md' --exclude='*.xml' --exclude='*.pro' --verbose --jobs=1