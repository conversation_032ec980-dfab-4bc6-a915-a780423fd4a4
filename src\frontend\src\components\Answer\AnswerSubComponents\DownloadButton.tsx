import React from "react";
import { <PERSON><PERSON>, IconButton } from "@fluentui/react";
import { FeedbackType, ChatResponse } from "../../../api";

interface Props {
    chatResponse: ChatResponse;
}

export const DownloadButton = ({
    chatResponse
}: Props) =>{
    return(
        <>
        {chatResponse.answer?.source_file && chatResponse.answer?.agent === 'rag_document' && (
            <div>
                <a href={`/download-file?path=${encodeURIComponent(chatResponse.answer?.source_file)}`}>
                    {chatResponse.answer?.source_file}
                </a>
            </div>
        )}
        </>
    )
}