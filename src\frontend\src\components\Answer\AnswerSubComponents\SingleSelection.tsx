import React from "react";
import { PrimaryButton } from "@fluentui/react";
import styles from  "../Answer.module.css"
import { Agents, DocumentTypeMap } from "../../../api";

interface Props {
    choices: any;
    sanitizedAnswerHtml: string;
    callAgent: (choice: Agents, document_types: Array<string> | undefined) => void;

    
}

export const SingleSelection = ({ choices, callAgent, sanitizedAnswerHtml }: Props) => {
    // Select only one choice
    return (
        <div className={styles.buttonGroup}>
            
            {choices.map((choice:DocumentTypeMap, index:number) => (
                <PrimaryButton
                    style={{ marginRight: "20px" }}
                    key={index}
                    className={styles.choiceButton}
                    onClick={() => {
                        if (Object.values(choice)[0] === Agents.rag) {
                            callAgent(Agents.rag, undefined);
                        } else if (Object.values(choice)[0] === Agents.ragDocument) {
                            callAgent(Agents.ragDocument, undefined);
                        } else {
                            callAgent(Agents.textToSql, undefined);
                        }
                    }}
                >
                    {Object.values(choice)[0] === Agents.rag 
                        ? "Search in document knowledge base" 
                        : Object.values(choice)[0] === Agents.ragDocument 
                            ? "Get the whole document" 
                            : "Search into the database"
                    }
                </PrimaryButton>
            ))}
        </div>
    );
};