import json
import traceback
from sqlalchemy import asc
from time import sleep
from src.backend.contracts.chat_data import (
    Answer,
    AnswerStatus,
    BotType,
    ChatExtraDetails,
    ChatResponse,
    UserRole,
    SQLAnswerType
)

from utils.exceptions import IncapableException, StoppedException, UndetectedIntendException, DocumentNotFoundException
from src.backend.models import QuestionHistory, UserRoles
from src import langchain_llm, azure_llm, text_to_sql_embedder, knowledge_bases
from src.common_tools.history.history import ConversationHistory
from src.common_tools.history.compilers import SQLAlchemyCompiler
from src.common_tools.history.parsers import SQLAlchemyParser
from src.common_tools.intent_detector.intent_detector import IntentDetector
from src.agents.text_to_sql_agent.text_to_sql import text_to_sql
from src.agents.rag_agent.rag import rag
from src.agents.rag_agent.rag_document import rag_document
from src.bots.bot import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>actory, BotType

class ChatService:

    def __init__(self,logger):
        self.logger = logger
        self.intent_detector = IntentDetector(langchain_llm, [rag, text_to_sql, rag_document])
        self.bot_handler_factory = BotHandlerFactory(langchain_llm, azure_llm, text_to_sql_embedder, knowledge_bases) 
        

    def chat(self, chatConfigsDTO: dict, submitted_text: str, selected_bot:str, conversation_session, max_rows=10):

        try:
            
            question_status = None

            sql_answer_type = chatConfigsDTO.get("overrides")["answer_type"]
            assert sql_answer_type in SQLAnswerType._value2member_map_, (f" {sql_answer_type} not supported as a type for sql answer.") #rimuovere perchè è da mettere nel controller
            
            user = chatConfigsDTO.get("user")
            conversation_id = chatConfigsDTO.get("conversation_id")
            user_id = chatConfigsDTO.get("user_id")
            dialog_id = chatConfigsDTO.get("dialog_id")


            history = self.setup_chat_history(user, conversation_id)
            self.logger.info(f"{len(history.messages)} previous messages identified for user {user_id}'s conversation {conversation_id}")


            web_response = ChatResponse(dialog_id=dialog_id)

            bot = self.bot_handler_factory.get_handler(selected_bot)
            extra_details = ChatExtraDetails(bot_type=bot.bot_name)
            chatConfigsDTO = self._build_bot_config_handler(chatConfigsDTO, max_rows, self.intent_detector)

            web_response, stream, question_status, reply = bot.handle(submitted_text, web_response, history, conversation_session, chatConfigsDTO)

            if chatConfigsDTO.get("overrides")["show_sql"] == False or self.get_user_role(user["id"]) != UserRole.ADMIN:
                web_response.answer.query = None

        except AssertionError as e:
            stream = False
            reply = f" {sql_answer_type} not supported as a type for sql answer."
            self.logger.error(reply)
            web_response.answer = Answer(
                formatted_answer=reply
            )

            extra_details.status = AnswerStatus.ERROR
        except IncapableException as e:
            stream = False
            self.logger.error(e)

            if e.failed_candidates is not None:
                reply: str = (
                    "Here are the failed examples with their associated database errors:\n"
                )

                for wrong_sql, error_message in e.failed_candidates:
                    reply += f"Failed SQL query:\n{wrong_sql}\nDatabase error message: {error_message}\n\n"

                web_response.answer = Answer(formatted_answer=reply)
            else:
                reply = e.message

            web_response.error = "ERROR: Cannot find an answer"
            web_response.answer = Answer(formatted_answer=reply)
            extra_details.status = AnswerStatus.ERROR
        except StoppedException:
            stream = False
            self.logger.debug("Stopped, clean exit.")
            reply = "Generation has been canceled"
            web_response.answer = Answer(formatted_answer=reply)
            extra_details.status = AnswerStatus.STOPPED

        except UndetectedIntendException:
            stream = False
            self.logger.debug(f"Can't detect intent")
            reply = "Can't detect intent, which feature do you want to use?"
            web_response.answer = Answer(
                formatted_answer= reply
            )
            extra_details.status = AnswerStatus.ERROR

        except DocumentNotFoundException:
            stream = False
            self.logger.debug("Document not found")
            reply = "Sorry, The document requested is not found"
            web_response.answer = Answer(
                formatted_answer=reply
            )
            extra_details.status = AnswerStatus.ERROR

        return web_response, stream, extra_details, question_status, reply        

    def setup_chat_history(self, user, conversation_id: str):

        messages = QuestionHistory.query.filter_by(
        user_id=user["id"],
        conversation_id=conversation_id,).filter(QuestionHistory.extra_details["status"].as_string() == AnswerStatus.SUCCESS.name).order_by(asc(QuestionHistory.created)).all()
        history = ConversationHistory(user["id"], messages, SQLAlchemyCompiler(SQLAlchemyParser()))
        return history
    
    def _build_bot_config_handler(self, chatConfigsDTO: dict, max_rows, intent_detector: IntentDetector):
        
        chatConfigsDTO["max_rows"] = max_rows
        chatConfigsDTO["intent_detector"] = intent_detector

        return chatConfigsDTO
    
    def stream_tokens(self, web_response: ChatResponse):
        """ Generator of stream tokens """
        formatted_answer = web_response.answer.formatted_answer
        web_response.answer.formatted_answer = ""
        # web_response.answer.source_files = ""

        json_web_answer = web_response.to_json()
        
        yield f"{json.dumps({'v': json_web_answer})}\n\n"
        sleep(0.005)
        for chunk in formatted_answer:
            yield f"{json.dumps({'v': chunk})}\n\n"
            sleep(0.005)
            # yield chunk
        # yield "data: [DONE]\n\n"

    def build_unhandled_error_response(self, dialog_id, selected_bot, user_role):

        web_response = ChatResponse(dialog_id=dialog_id)
        extra_details = ChatExtraDetails(bot_type=BotType[selected_bot].name)


        match user_role:
            case UserRole.USER:
                reply = "GENERIC ERROR: Cannot find an answer"
            case UserRole.ADMIN:
                reply = traceback.format_exc()

        web_response.answer = Answer(formatted_answer=reply)
        web_response.error = reply
        extra_details.status = AnswerStatus.ERROR

        return web_response
    
    def get_user_role(self, user_id) -> UserRole:
        user_role = UserRoles.query.filter_by(user_id=user_id).first()
        return UserRole(user_role.role_id)