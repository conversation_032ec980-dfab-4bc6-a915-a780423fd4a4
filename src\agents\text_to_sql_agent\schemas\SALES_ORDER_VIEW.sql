CREATE TABLE DWH_PUBLIC.SALES_ORDER_VIEW(
ORDER_COMPANY NVARCHAR2(10) NOT NULL ENABLE,
ORDER_YEAR NUMBER NOT NULL ENABLE,
ORDER_NUMBER NUMBER NOT NULL ENABLE,
ORDER_SOLDTO NVARCHAR2(20),
ORDER_BILLTO NVARCHAR2(20),
ORDER_SHIPTO NVARCHAR2(20),
ORDER_END_USER NVARCHAR2(20),
ORDER_KEY_ACCOUNT NVARCHAR2(20),
ORDER_CHANNEL NVARCHAR2(400),
ORDER_COMMERCIALBRAND NVARCHAR2(400),
ORDER_PRODUCTLINE NVARCHAR2(400),
ORDER_ORDERTYPE NVARCHAR2(10),
ORDER_DATE DATE,
ORDER_PRICELISTCOMPANY NVARCHAR2(20),
ORDER_PRICELISTCODE NVARCHAR2(20),
OR<PERSON>R_CURRENCY NVARCHAR2(10),
ORDER_EXCHANGERATE NUMBER(12,6),
ORDER_BRAND NVARCHAR2(10),
ORDER_PAYMENTCODE NVARCHAR2(10),
ORDER_PAYMENTCODE_DESCRIPTION NVARCHAR2(360),
ORDER_INTEREST NVARCHAR2(10),
ORDER_BANKFEE NVARCHAR2(10),
ORDER_INSTALLATION NVARCHAR2(10),
ORDER_WARRANTY NVARCHAR2(10),
ORDER_INVOICINGPERIOD NVARCHAR2(400),
ORDER_DISCOUNTAMOUNT NUMBER(13,2),
ORDER_DISCOUNTINVOICED NUMBER(13,2),
ORDER_DISCOUNT_PERCROW NUMBER(5,2),
ORDER_DISCOUNT_PERCENTAGE NUMBER(5,2),
ORDER_SOURCE NVARCHAR2(20),
ORDER_CREDIT_STATUS NVARCHAR2(400),
ORDER_ORDERSTATUS NVARCHAR2(400),
ORDER_DELVRYSTATUS NVARCHAR2(10),
ORDER_INVOICESTATUS NVARCHAR2(10),
ORDER_ROW NUMBER(38,0) NOT NULL ENABLE,
ORDER_ROW_SPLIT_ATP NUMBER(38,0) NOT NULL ENABLE,
ORDER_ROW_SPLIT_DELIVERY NUMBER(38,0) NOT NULL ENABLE,
ORDER_LOT_NUMBER NUMBER(38,0),
INTERNAL_CODE NVARCHAR2(20),
ORDER_ROW_QUANTITY NUMBER(38,0),
WH_REQUEST NVARCHAR2(20),
REQUESTED_DATE_ORIGIN DATE,
REQUESTED_DATE_ATP DATE,
DATE_ACCEPTED DATE,
UNIT_MESURE NVARCHAR2(20),
GROSS_PRICE NUMBER(17,3),
CAMPAIGN_PRICE NUMBER(17,3),
NET_PRICE NUMBER(17,3),
ORDER_CHECK_CREDIT_ENABLED NVARCHAR2(20),
ORDER_SHIP NVARCHAR2(10),
ORDER_DELVRYTERM NVARCHAR2(10),
ORDER_DELVRYTERM_DESCRIPTION NVARCHAR2(400),
ORDER_DELVRYTYPE NVARCHAR2(10),
ORDER_DELVRYTYPE_DESCRIPTION NVARCHAR2(400),
WARRANTY NVARCHAR2(5),
PROM_DATE_ATP DATE,
INVOICE_NUMBER NVARCHAR2(10),
DELIVERY_NOTE_NUMBER NVARCHAR2(10),
PRODUCT_NUMBER NVARCHAR2(20)
)