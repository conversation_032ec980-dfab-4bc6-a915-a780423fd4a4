import os
import unittest

from src.database.relevant_schemas import get_relevant_schemas
from src.models.preliminary import PreliminaryModel


class test_preliminary_model(unittest.TestCase):

    def test_prompt(self):
        inquiry = "Give me the number of apples"
        path = os.path.join(os.path.dirname(__file__), "./mock_schema.json")

        db = get_relevant_schemas([], path=path)

        ground_truth = f'{inquiry} | fruit_and_vegetables | fruit : name , ORIGIN | VEGETABLES : "Species" , farmer_name'

        self.assertEqual(PreliminaryModel._build_prompt(inquiry, db), ground_truth)


if __name__ == "__main__":
    unittest.main()
