from flask import Blueprint, render_template, request, jsonify, current_app, session
from src.backend.blueprints.auth.decorators import login_epr
from src.agents.eproexcella_agent.eproexcella import EPROExcelLa
from src.agents.agent_factory import AgentFactory
from src.bots.bot import EPROExcelLaBot
from src.backend.contracts.chat_data import BotType
from src.backend.models import User
from src import db
import os
import pandas as pd
from werkzeug.utils import secure_filename


translator_bot_routes = Blueprint('translator_bot_routes', __name__, template_folder='templates')


@translator_bot_routes.route('/')
def index():
    """Main translation tool page"""
    current_app.logger.info("Translation tool accessed")
    return render_template('translator_bot/translation_tool.html')


@translator_bot_routes.route('/api/upload', methods=['POST'])
def upload_file():
    """Handle file upload for translation"""
    try:
        if 'file' not in request.files:
            return jsonify({'error': 'No file provided'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400

        # Validate file type
        allowed_extensions = {'.xlsx', '.pptx', '.docx'}
        file_extension = '.' + file.filename.rsplit('.', 1)[1].lower()

        if file_extension not in allowed_extensions:
            return jsonify({'error': 'Invalid file type'}), 400

        # Get user ID from session or use default for testing
        user_id = session.get('user_id', 'test_user')
        
        # For Excel files, save and process with EPROExcelLa
        if file_extension == '.xlsx':
            # Create upload directory if it doesn't exist
            upload_dir = os.path.join(os.path.dirname(__file__), '..', '..', '..', 'agents', 'eproexcella_agent', 'upload')
            os.makedirs(upload_dir, exist_ok=True)
            
            # Save file with user ID
            filename = secure_filename(f"data_{user_id}.xlsx")
            file_path = os.path.join(upload_dir, filename)
            file.seek(0)  # Reset file pointer after reading
            file.save(file_path)
            
            # Read Excel file to get column information
            try:
                df = pd.read_excel(file_path, nrows=5)  # Preview first 5 rows
                columns = df.columns.tolist()

                # Convert preview data safely
                preview_data = []
                for _, row in df.head().iterrows():
                    row_dict = {}
                    for col in columns:
                        value = row[col]
                        # Handle NaN and other non-serializable values
                        if pd.isna(value):
                            row_dict[col] = None
                        elif isinstance(value, (int, float, str, bool)):
                            row_dict[col] = value
                        else:
                            row_dict[col] = str(value)
                    preview_data.append(row_dict)

                # Get total rows safely
                try:
                    total_df = pd.read_excel(file_path)
                    total_rows = len(total_df)
                except:
                    total_rows = len(df)

                return jsonify({
                    'success': True,
                    'filename': file.filename,
                    'type': file_extension,
                    'columns': columns,
                    'preview': preview_data,
                    'total_rows': total_rows
                })
            except Exception as e:
                current_app.logger.error(f"Excel processing error: {e}")
                return jsonify({'error': f'Error reading Excel file: {str(e)}'}), 400
        
        else:
            # For other file types, use existing logic
            return jsonify({
                'success': True,
                'filename': file.filename,
                'size': len(file.read()),
                'type': file_extension
            })
        
    except Exception as e:
        current_app.logger.error(f"File upload error: {e}")
        import traceback
        current_app.logger.error(f"Full traceback: {traceback.format_exc()}")
        return jsonify({'error': f'Upload failed: {str(e)}'}), 500


@translator_bot_routes.route('/api/translate', methods=['POST'])
def translate_document():
    """Handle document translation request"""
    try:
        data = request.get_json()

        # Validate required fields
        if not data or 'target_language' not in data:
            return jsonify({'error': 'Target language is required'}), 400

        target_language = data['target_language']
        source_language = data.get('source_language', 'auto')
        selected_columns = data.get('selected_columns', [])
        file_type = data.get('file_type', '')

        # Get user ID from session or use default for testing
        user_id = session.get('user_id', 'test_user')
        
        # Handle Excel files with EPROExcelLa
        if file_type == '.xlsx':
            try:
                # Create translation request for EPROExcelLa
                if selected_columns:
                    # Build natural language request for selected columns
                    column_names = ', '.join(selected_columns)
                    if len(selected_columns) == 1:
                        translation_request = f"Translate the column {column_names} in {target_language}"
                    else:
                        translation_request = f"Translate the columns {column_names} in {target_language}"
                else:
                    translation_request = f"Translate all text columns in {target_language}"
                
                # Initialize EPROExcelLa bot
                eproexcella_bot = EPROExcelLaBot(
                    agents=[AgentFactory.EPROEXCELLA.name], 
                    bot_name=BotType.EPROEXCELLA_BOT.name, 
                    user_id=user_id
                )
                
                # Process translation with preview=False for full file
                reply = eproexcella_bot.call_agent(
                    AgentFactory.EPROEXCELLA.name,
                    inquiry=translation_request,
                    history=None,
                    preview=False
                )
                
                current_app.logger.info(f"Translation completed for user {user_id}")
                
                return jsonify({
                    'success': True,
                    'message': 'Translation completed successfully',
                    'translation_id': f'trans_{user_id}_{hash(translation_request)}',
                    'status': 'completed',
                    'data': reply.data,
                    'columns_translated': selected_columns or 'all'
                })
                
            except Exception as e:
                current_app.logger.error(f"EPROExcelLa translation error: {e}")
                return jsonify({'error': f'Excel translation failed: {str(e)}'}), 500
        
        else:
            # For other file types, implement other translation logic
            current_app.logger.info(f"Translation request: {source_language} -> {target_language}")
            
            return jsonify({
                'success': True,
                'message': 'Translation started',
                'translation_id': 'trans_123456',
                'status': 'processing'
            })
        
    except Exception as e:
        current_app.logger.error(f"Translation error: {e}")
        return jsonify({'error': 'Translation failed'}), 500


@translator_bot_routes.route('/api/status/<translation_id>')
def get_translation_status(translation_id):
    """Get translation status"""
    try:
        # Here you would check actual translation status
        # For now, simulate completion
        return jsonify({
            'success': True,
            'translation_id': translation_id,
            'status': 'completed',
            'progress': 100,
            'download_url': f'/translator/api/download/{translation_id}'
        })
        
    except Exception as e:
        current_app.logger.error(f"Status check error: {e}")
        return jsonify({'error': 'Status check failed'}), 500


@translator_bot_routes.route('/api/download/<translation_id>')
@login_epr
def download_translated_file(translation_id):
    """Download translated file"""
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'error': 'User not authenticated'}), 401
        
        # For Excel files, get the processed file from EPROExcelLa
        upload_dir = os.path.join(os.path.dirname(__file__), '..', '..', '..', 'agents', 'eproexcella_agent', 'upload')
        file_path = os.path.join(upload_dir, f"data_{user_id}.xlsx")
        
        if os.path.exists(file_path):
            # Get the translated data from EPROExcelLa
            eproexcella_agent = EPROExcelLa(BotType.EPROEXCELLA_BOT.name, user_id=user_id)
            
            # Return the file for download
            from flask import send_file
            return send_file(
                file_path, 
                as_attachment=True, 
                download_name=f"translated_file_{user_id}.xlsx",
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
        else:
            return jsonify({'error': 'Translated file not found'}), 404
        
    except Exception as e:
        current_app.logger.error(f"Download error: {e}")
        return jsonify({'error': 'Download failed'}), 500


@translator_bot_routes.route('/api/preview', methods=['POST'])
@login_epr  
def preview_translation():
    """Preview translation for Excel files"""
    try:
        data = request.get_json()
        
        if not data or 'translation_request' not in data:
            return jsonify({'error': 'Translation request is required'}), 400
        
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'error': 'User not authenticated'}), 401
        
        translation_request = data['translation_request']
        
        # Initialize EPROExcelLa bot for preview
        eproexcella_bot = EPROExcelLaBot(
            agents=[AgentFactory.EPROEXCELLA.name], 
            bot_name=BotType.EPROEXCELLA_BOT.name, 
            user_id=user_id
        )
        
        # Process with preview=True for first 10 rows
        reply = eproexcella_bot.call_agent(
            AgentFactory.EPROEXCELLA.name,
            inquiry=translation_request,
            history=None,
            preview=True
        )
        
        # Convert JSON data to DataFrame for preview
        data_json = pd.read_json(reply.data)
        preview_html = data_json.head(10).to_html(index=False, classes='table table-striped')
        
        return jsonify({
            'success': True,
            'preview_html': preview_html,
            'total_rows': len(data_json),
            'columns': data_json.columns.tolist()
        })
        
    except Exception as e:
        current_app.logger.error(f"Preview error: {e}")
        return jsonify({'error': 'Preview failed'}), 500


@translator_bot_routes.route('/api/version', methods=['GET'])
@login_epr
def get_version():
    """Get version information for translator bot"""
    try:
        from src.backend.utils.sys_utils import get_env_version
        return get_env_version()
    except Exception as e:
        current_app.logger.error(f"Version error: {e}")
        return jsonify({'error': 'Failed to get version'}), 500


@translator_bot_routes.route('/api/changelog/preview', methods=['GET'])
@login_epr
def get_changelog_preview():
    """Get changelog preview for translator bot"""
    try:
        from src.backend.utils.sys_utils import stream_last_n_releases
        import os
        
        # Look for changelog file
        changelog_path = os.path.join(os.path.dirname(__file__), '..', '..', '..', '..', 'CHANGELOG.md')
        if os.path.exists(changelog_path):
            preview = stream_last_n_releases(changelog_path, 2)
            return preview, 200, {'Content-Type': 'text/plain; charset=utf-8'}
        else:
            return 'Changelog not found', 404
    except Exception as e:
        current_app.logger.error(f"Changelog error: {e}")
        return 'Error loading changelog', 500
