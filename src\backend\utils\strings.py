from json import dumps
from src.backend.contracts.chat_data import (
    Answer,
    ChatResponse
)

def sample_answer(dialog_id: str, text: str):
    answer = Answer(
        formatted_answer="PONG - " + text,
        query="SELECT * FROM myTable;"
    )
    web_response = ChatResponse(
        dialog_id=dialog_id,
        answer=answer,
        classification="ANSWER",
        show_retry=False,
        suggested_classification=None,
        data_points=["abcd"],
        )
    return web_response