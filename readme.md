# Compli-Bot

## Managing dependencies
Dependencies are managed using `pip-tools`, to add a dependency, add a row in the file `requirements.in` and execute this command `pip-compile requirements.in` to automatically fill-out the file `requirements.txt`.
The file `requirements.txt` is automatically managed by *pip-tools*, don't do manual changes on that file.
To install (and automatically remove) dependencies, execute `pip-sync` command.

## Compli-Bot Deployment Guide
This guide walks you through deploying Compli-Bot.
- Make sure you have downloaded the Compli-Bot repository from Bitbucket repository
- After merging the `develop` branc into the `main` branch, check the release using the command `semantic-release -c semantic_release.json --noop -v version`. Once everything is fine, launch the command `semantic-release -c semantic_release.json -v version` to apply the new version.

### Common commit messages
| Type      | Effect  | Description                                               |
|-----------|---------|-----------------------------------------------------------|
| feat      | Minor   | Adds a new feature                                        |
| fix       | Patch   | Fixes a bug                                               |
| chore     | None    | Changes not related to executable code (e.g., build, CI)  |
| docs      | None    | Documentation changes                                     |
| style     | None    | Code style changes (spacing, formatting)                  |
| refactor  | None    | Code refactoring without functional changes               |
| test      | None    | Adds or modifies tests                                    |
| perf      | Patch   | Performance improvements                                  |

WARNING1: only the first row of the commit message will be evaluated to increase the release version

WARNING2: the key word must be the first of the commit message (e.g. "fix: avoid generic error on answers")

WARNING3: use the ! char to highlight a breaking change and increment the major release (e.g. "feat!: …")

### Generating Credentials
You must create a file named `key.private` based on the provided template `key.private.example`.
This `key.private` file will securely store all the secret keys needed for Compli-Bot to interact with various services.

### Deployment Options
There are two main deployment options currently suppported:
- IIS Server:
    - To deploy Compli-Bot on an IIS server, you need to set an environment variable called `FLASK_ENV`. This variable should be set to one of the following values depending on your deployment environment:
        - `PRD`: Production environment
        - `STG`: Staging environment
        - `DEV`: Development environment
    - **Note**: This environment variable step is only necessary for IIS deployment, not for local development.
    - Simply run the script `setup.ps1`: this will create a ready-to-go `web.config` file for IIS
- Local Development:
    - For local development, you can simply run the script `setup.ps1`.
    - This script will automatically configure your local development environment:

## Database
Database versioning is managed with Alembic

### Migrations
WARNING: Take care about db migrations on branches. All the migrations should be created and applied only on the develop branch and reflected to the main branch!
Every time a modification it's made on _models.py_ use the **migrate** command to prepare the migration script:
`flask --app src db migrate -m "user settings data on user table"`
To execute the migration script and update the database run the **upgrade** command:
`flask --app src db upgrade`
In case of conflicts, you can use the merge command:
`flask --app src db merge`
Check the versions before the deployment, both the following command must return the same version before the deployment:
A. `flask --app src db current` (This command shows the version of the database)
B. `flask --app src db heads` (This command shows the last version of the migration script)
