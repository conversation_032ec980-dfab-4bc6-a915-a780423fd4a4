from typing import List, Tuple

class SQLErrors():
    def __init__(self):
        self.invalid_identifier_prompt = ''

    def check_invalid_identifier_error(self, candidates: list[Tuple[str, List[Tuple] | str]]):
        for candidate in candidates:
            if "invalid identifier" in candidate[1]:
                self.invalid_identifier_prompt = 'Pay attention to do the joins and be carefully to choose the correct tables, because the previously generated queries gave the invalid identifier error'
            else:
                self.invalid_identifier_prompt = ''

    def reset(self):
        """Reset invalid_identifier_prompt to the default value""" 
        self.invalid_identifier_prompt = ''