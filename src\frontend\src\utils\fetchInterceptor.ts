type FetchRequest = (input: RequestInfo | URL, init?: RequestInit) => Promise<Response>;

// Salva il fetch originale
const originalFetch = window.fetch;

// Crea l'interceptor
const fetchInterceptor = async (input: RequestInfo | URL, init?: RequestInit): Promise<Response> => {
    try {
        const response = await originalFetch(input, init);

        // Intercetta la risposta 503
        if (response.status === 503) {
            const data = await response.json();
            if (data.error === 'maintenance_mode' && data.redirectUrl) {
                window.location.href = data.redirectUrl;
                throw new Error('Maintenance mode redirect');
            }
        }
        return response;
    } catch (error) {
        throw error;
    }
};

// Sostituisci il fetch globale
window.fetch = fetchInterceptor as typeof fetch;

export default fetchInterceptor;