CREATE TABLE DWH_PUBLIC.AGREEMENT_VIEW (
	COMPANY_CODE VARCHAR2(20 BYTE),
	COMPANY_DESCRIPTION VARCHAR2(100 BYTE),
	PLANT_CODE VARCHAR2(8 BYTE),
	PLANT_DESCRIPTION VARCHAR2(240 BYTE),
	AGREEMENT_CODE VARCHAR2(8 BYTE),
	BUYER_CODE VARCHAR2(2 BYTE),
	BUYER_DESCRIPTION VARCHAR2(240 BYTE),
	SUPPLIER VARCHAR2(10 BYTE),
	SUPPLIER_DESCRIPTION VARCHAR2(240 BYTE),
	START_VALIDITY_DATE DATE,
	STOP_VALIDITY_DATE DATE,
	ROW_NUMBER NUMBER(5, 0),
	AMOUNT NUMBER(13, 2),
	CURRENCY VARCHAR2(4 BYTE),
	CURRENCY_DESCRIPTION VARCHAR2(240 BYTE),
	ITEM_CODE VARCHAR2(20 BYTE),
	ITEM_PRICE NUMBER(15, 5),
	ITEM_DISCOUNT_PERCENTAGE_1 NUMBER(5, 2),
	ITEM_DISCOUNT_PERCENTAGE_2 NUMBER(5, 2),
	ITEM_LOT_QTY_CODE NUMBER(10, 2),
	ITEM_LOT_QTY VARCHAR2(100 BYTE),
	ITEM_BUDGET_QTY NUMBER(10, 2),
	SUPPLIER_ITEM_CODE VARCHAR2(20 BYTE),
	PRIMARY KEY (COMPANY_CODE, PLANT_CODE, AGREEMENT_CODE, ITEM_CODE, ITEM_LOT_QTY_CODE)
)