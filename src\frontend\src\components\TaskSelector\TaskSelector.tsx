import React, { useState } from 'react';
import styles from './TaskSelector.module.css';

const TaskSelector = () => {
    const [selectedButton, setSelectedButton] = useState(null);
    const [isEditable, setIsEditable] = useState(false);
    const [text, setText] = useState("");

    const handleClick = (button: any) => {
        setSelectedButton(button);
        loadText(button);
    };

    const loadText = async (button: any) => {
        let fileName = '';
        switch (button) {
            // case 'Translation':
            //     fileName = '/system_prompt/translation.txt';
            //     break;
            // case 'Custom Class':
            //     fileName = '/system_prompt/custom_class.txt';
            //     break;
            // case 'Choose yourself':
            //     fileName = '/system_prompt/custom_prompt.txt';
            //     break;
            default:
                break;
        }

        // try {
        //     console.log(`Loading file: ${fileName}`); // Debug: verifica il percorso del file
        //     const response = await fetch(fileName);
        //     if (!response.ok) {
        //         throw new Error(`HTTP error! status: ${response.status}`);
        //     }
        //     const data = await response.text();
        //     setText(data);
        // } catch (error) {
        //     console.error('Error loading file:', error);
        // }
    };

    return (
        <div className={styles.container}>
            <div className={styles.buttonContainer}>
                <div
                    className={`${styles.button} ${selectedButton === 'Translation' ? styles.selected : styles.small}`}
                    onClick={() => handleClick('Translation')}
                >
                    <h2>Translation</h2>
                    {!selectedButton && <p>Translate the column of your file...</p>}
                </div>
                <div
                    className={`${styles.button} ${selectedButton === 'Custom Class' ? styles.selected : styles.small}`}
                    onClick={() => handleClick('Custom Class')}
                >
                    <h2>Custom Class</h2>
                    {!selectedButton && <p>Get the custom class by your description...</p>}
                </div>
                <div
                    className={`${styles.button} ${selectedButton === 'Choose yourself' ? styles.selected : styles.small}`}
                    onClick={() => handleClick('Choose yourself')}
                >
                    <h2>Choose yourself</h2>
                    {!selectedButton && <p>Simply program what you want to do...</p>}
                </div>
            </div>
            {selectedButton && (
                <div className={styles.editableContainer}>
                    <p>{isEditable ? (
                        <input
                            className={styles.textfieldInput}
                            type="text"
                            value={text}
                            onChange={(e) => setText(e.target.value)}
                        />
                    ) : (
                        text
                    )}</p>
                    <button onClick={() => setIsEditable(!isEditable)}>
                        {isEditable ? 'Save' : 'Edit'}
                    </button>
                </div>
            )}
        </div>
    );
};

export default TaskSelector;