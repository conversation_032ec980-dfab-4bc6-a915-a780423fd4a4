<!-- templates/translation_tool.html -->

{% extends "base.html" %}

{% block head_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
{% endblock %}

{% block content %}
<div class="translation-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10 col-xl-8">
                <div class="translation-card">
                    <div class="card-header-custom">
                        <h1 class="mb-0">
                            <i class="fas fa-language me-3"></i>
                            AI Document Translation Tool
                        </h1>
                        <p class="mb-0 mt-2 opacity-75">
                            Translate Excel, PowerPoint, and Word documents with AI precision
                        </p>
                    </div>
                    
                    <div class="card-body p-4">
                        <!-- File Upload Section -->
                        <div class="mb-4">
                            <h5 class="mb-3">
                                <i class="fas fa-upload me-2"></i>
                                Upload Document
                            </h5>
                            
                            <div class="upload-area" id="uploadArea">
                                <div class="upload-icon">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                </div>
                                <h6>Drag & drop your file here</h6>
                                <p class="text-muted mb-3">or click to browse</p>
                                <p class="small text-muted">
                                    Supported formats: Excel (.xlsx), PowerPoint (.pptx), Word (.docx)
                                    <br>Maximum file size: 50MB
                                </p>
                                <input type="file" id="fileInput" accept=".xlsx,.pptx,.docx" style="display: none;">
                            </div>
                            
                            <div id="fileInfo" class="file-info" style="display: none;">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-file me-3 text-success"></i>
                                    <div class="flex-grow-1">
                                        <div class="fw-bold" id="fileName"></div>
                                        <div class="small text-muted" id="fileDetails"></div>
                                    </div>
                                    <button class="btn btn-sm btn-outline-danger" id="removeFile">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Language Selection -->
                        <div class="mb-4">
                            <h5 class="mb-3">
                                <i class="fas fa-globe me-2"></i>
                                Language Settings
                            </h5>
                            
                            <div class="language-grid">
                                <div>
                                    <label for="sourceLanguage" class="form-label fw-semibold">Source Language</label>
                                    <select class="form-select form-select-custom" id="sourceLanguage">
                                        <option value="auto">Auto-detect</option>
                                        <option value="en">English</option>
                                        <option value="es">Spanish</option>
                                        <option value="fr">French</option>
                                        <option value="de">German</option>
                                        <option value="it">Italian</option>
                                        <option value="pt">Portuguese</option>
                                        <option value="ru">Russian</option>
                                        <option value="ja">Japanese</option>
                                        <option value="ko">Korean</option>
                                        <option value="zh">Chinese</option>
                                    </select>
                                </div>
                                
                                <div>
                                    <label for="targetLanguage" class="form-label fw-semibold">Target Language</label>
                                    <select class="form-select form-select-custom" id="targetLanguage">
                                        <option value="">Select target language</option>
                                        <option value="en">English</option>
                                        <option value="es">Spanish</option>
                                        <option value="fr">French</option>
                                        <option value="de">German</option>
                                        <option value="it">Italian</option>
                                        <option value="pt">Portuguese</option>
                                        <option value="ru">Russian</option>
                                        <option value="ja">Japanese</option>
                                        <option value="ko">Korean</option>
                                        <option value="zh">Chinese</option>
                                        <option value="ar">Arabic</option>
                                        <option value="hi">Hindi</option>
                                        <option value="th">Thai</option>
                                        <option value="vi">Vietnamese</option>
                                        <option value="nl">Dutch</option>
                                        <option value="sv">Swedish</option>
                                        <option value="no">Norwegian</option>
                                        <option value="da">Danish</option>
                                        <option value="fi">Finnish</option>
                                        <option value="pl">Polish</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Excel Column Selection (hidden by default) -->
                        <div id="excelOptions" class="excel-options" style="display: none;">
                            <h5 class="mb-3">
                                <i class="fas fa-table me-2"></i>
                                Excel Column Selection
                            </h5>
                            <p class="text-muted mb-3">Select which columns you want to translate:</p>
                            
                            <div class="row" id="columnCheckboxes">
                                <!-- Checkboxes will be populated dynamically -->
                            </div>
                        </div>

                        <!-- Translation Button -->
                        <div class="text-center mt-4">
                            <button class="btn btn-translate btn-lg" id="translateBtn" disabled>
                                <i class="fas fa-magic me-2"></i>
                                Start Translation
                            </button>
                        </div>

                        <!-- Progress Section (hidden by default) -->
                        <div id="progressSection" class="progress-container" style="display: none;">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="fw-semibold">Translation Progress</span>
                                <span id="progressPercent">0%</span>
                            </div>
                            <div class="progress progress-custom">
                                <div class="progress-bar progress-bar-custom" id="progressBar" role="progressbar" style="width: 0%"></div>
                            </div>
                            <div class="status-text" id="statusText">Initializing...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block tail_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const uploadArea = document.getElementById('uploadArea');
    const fileInput = document.getElementById('fileInput');
    const fileInfo = document.getElementById('fileInfo');
    const fileName = document.getElementById('fileName');
    const fileDetails = document.getElementById('fileDetails');
    const removeFile = document.getElementById('removeFile');
    const excelOptions = document.getElementById('excelOptions');
    const columnCheckboxes = document.getElementById('columnCheckboxes');
    const translateBtn = document.getElementById('translateBtn');
    const targetLanguage = document.getElementById('targetLanguage');
    const progressSection = document.getElementById('progressSection');
    const progressBar = document.getElementById('progressBar');
    const progressPercent = document.getElementById('progressPercent');
    const statusText = document.getElementById('statusText');
    
    let selectedFile = null;

    // Upload area click handler
    uploadArea.addEventListener('click', () => {
        fileInput.click();
    });

    // Drag and drop handlers
    uploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });

    uploadArea.addEventListener('dragleave', () => {
        uploadArea.classList.remove('dragover');
    });

    uploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleFileSelect(files[0]);
        }
    });

    // File input change handler
    fileInput.addEventListener('change', (e) => {
        if (e.target.files.length > 0) {
            handleFileSelect(e.target.files[0]);
        }
    });

    // Remove file handler
    removeFile.addEventListener('click', () => {
        selectedFile = null;
        fileInfo.style.display = 'none';
        uploadArea.style.display = 'block';
        excelOptions.style.display = 'none';
        fileInput.value = '';
        updateTranslateButton();
    });

    // Target language change handler
    targetLanguage.addEventListener('change', updateTranslateButton);

    // Translate button handler
    translateBtn.addEventListener('click', startTranslation);

    function handleFileSelect(file) {
        // Validate file type
        const allowedTypes = ['.xlsx', '.pptx', '.docx'];
        const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
        
        if (!allowedTypes.includes(fileExtension)) {
            alert('Please select a valid file type: Excel (.xlsx), PowerPoint (.pptx), or Word (.docx)');
            return;
        }

        // Validate file size (50MB limit)
        if (file.size > 50 * 1024 * 1024) {
            alert('File size must be less than 50MB');
            return;
        }

        selectedFile = file;
        
        // Update file info display
        fileName.textContent = file.name;
        fileDetails.textContent = `${(file.size / 1024 / 1024).toFixed(2)} MB • ${fileExtension.toUpperCase().substring(1)} file`;
        
        uploadArea.style.display = 'none';
        fileInfo.style.display = 'block';

        // Show Excel options if it's an Excel file
        if (fileExtension === '.xlsx') {
            showExcelOptions();
        } else {
            excelOptions.style.display = 'none';
        }

        updateTranslateButton();
    }

    function showExcelOptions() {
        // Sample columns - in a real implementation, these would be detected from the file
        const sampleColumns = [
            'Name', 'Description', 'Comments', 'Notes', 'Title', 
            'Content', 'Summary', 'Details', 'Instructions', 'Remarks'
        ];

        columnCheckboxes.innerHTML = '';
        
        sampleColumns.forEach((column, index) => {
            const colDiv = document.createElement('div');
            colDiv.className = 'col-md-6 column-checkbox';
            
            colDiv.innerHTML = `
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" value="${column}" id="col${index}" ${index < 3 ? 'checked' : ''}>
                    <label class="form-check-label" for="col${index}">
                        ${column}
                    </label>
                </div>
            `;
            
            columnCheckboxes.appendChild(colDiv);
        });

        excelOptions.style.display = 'block';
    }

    function updateTranslateButton() {
        const hasFile = selectedFile !== null;
        const hasTargetLanguage = targetLanguage.value !== '';
        
        translateBtn.disabled = !(hasFile && hasTargetLanguage);
    }

    function startTranslation() {
        progressSection.style.display = 'block';
        translateBtn.disabled = true;
        
        // Simulate translation progress
        let progress = 0;
        const steps = [
            'Analyzing document structure...',
            'Extracting text content...',
            'Processing with AI translation...',
            'Applying translations...',
            'Finalizing document...',
            'Translation complete!'
        ];
        
        const interval = setInterval(() => {
            progress += Math.random() * 20;
            if (progress > 100) progress = 100;
            
            const stepIndex = Math.floor((progress / 100) * (steps.length - 1));
            
            progressBar.style.width = progress + '%';
            progressPercent.textContent = Math.round(progress) + '%';
            statusText.textContent = steps[stepIndex];
            
            if (progress >= 100) {
                clearInterval(interval);
                setTimeout(() => {
                    // Simulate download
                    const downloadLink = document.createElement('a');
                    downloadLink.href = '#';
                    downloadLink.download = `translated_${selectedFile.name}`;
                    downloadLink.textContent = 'Download Translated File';
                    downloadLink.className = 'btn btn-success btn-lg mt-3';
                    downloadLink.innerHTML = '<i class="fas fa-download me-2"></i>Download Translated File';
                    
                    progressSection.appendChild(downloadLink);
                    translateBtn.disabled = false;
                }, 1000);
            }
        }, 200);
    }
});
</script>
{% endblock %}
