from flask import Blueprint
from src import db
from src.backend.models import User, Role, UserRoles
from sqlalchemy.dialects.sqlite import insert


bp = Blueprint('cli', __name__, cli_group=None)


@bp.cli.command()
def seed():

    #"Add seed data to the database."
    insert_user(session=db.session, email='al<PERSON>.<EMAIL>', name='<PERSON>', surname='<PERSON>')
    insert_user(session=db.session, email='<EMAIL>', name='<PERSON>', surname='<PERSON><PERSON><PERSON><PERSON>')
    insert_user(session=db.session, email='<EMAIL>', name='<PERSON>', surname='<PERSON><PERSON><PERSON>')
    insert_user(session=db.session, email='<EMAIL>', name='<PERSON>', surname='Tron<PERSON>')
    insert_user(session=db.session, email='gio<PERSON><PERSON>.<EMAIL>', name='<PERSON>', surname='<PERSON><PERSON><PERSON>')
    insert_user(session=db.session, email='lo<PERSON><PERSON>.ambro<PERSON>@electroluxprofessional.com', name='<PERSON>', surname='<PERSON><PERSON><PERSON>')
    insert_user(session=db.session, email='<EMAIL>', name='Silvana', surname='Falcomer')
    insert_user(session=db.session, email='<EMAIL>', name='Stefano', surname='Desiderio')

    insert_role(session=db.session, name='ADMIN')
    insert_role(session=db.session, name='USER')

    insert_userroles(session=db.session, user_id=1, role_id=1)
    insert_userroles(session=db.session, user_id=2, role_id=1)
    insert_userroles(session=db.session, user_id=3, role_id=2)
    insert_userroles(session=db.session, user_id=4, role_id=2)
    insert_userroles(session=db.session, user_id=5, role_id=2)
    insert_userroles(session=db.session, user_id=6, role_id=2)
    insert_userroles(session=db.session, user_id=7, role_id=2)
    insert_userroles(session=db.session, user_id=8, role_id=2)


def insert_user(session, email, name, surname):
    stmt = (
            insert(User)
            .values(
                email=email, name=name, surname=surname,
            ).on_conflict_do_nothing(
                 index_elements=['email']
            )
        )
    session.execute(stmt)
    session.commit()


def insert_role(session, name):
    stmt = (
            insert(Role)
            .values(
                name=name,
            ).on_conflict_do_nothing(
                 index_elements=['name']
            )
        )
    session.execute(stmt)
    session.commit()


def insert_userroles(session, user_id, role_id):
    stmt = (
            insert(UserRoles)
            .values(
                user_id=user_id, role_id=role_id
            ).on_conflict_do_nothing(
                 index_elements=['user_id']
            )
        )
    session.execute(stmt)
    session.commit()