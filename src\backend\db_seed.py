from sqlalchemy.sql import text

def seed(db):

    #"Add seed data to the database."
    insert_user(db, id= 1, email='al<PERSON>.<EMAIL>', name='<PERSON>', surname='<PERSON>')
    insert_user(db, id= 2, email='<EMAIL>', name='<PERSON>', surname='<PERSON><PERSON><PERSON><PERSON>')
    insert_user(db, id= 3, email='<EMAIL>', name='<PERSON>', surname='<PERSON><PERSON><PERSON>')
    insert_user(db, id= 4, email='<EMAIL>', name='<PERSON>', surname='<PERSON>ron<PERSON>')
    insert_user(db, id= 5, email='<EMAIL>', name='<PERSON>', surname='<PERSON><PERSON><PERSON>')
    insert_user(db, id= 6, email='<EMAIL>', name='<PERSON>', surname='<PERSON><PERSON><PERSON>')
    insert_user(db, id= 7, email='<EMAIL>', name='<PERSON><PERSON>', surname='Falcomer')
    insert_user(db, id= 8, email='<EMAIL>', name='<PERSON>', surname='Desiderio')

    insert_role(db, id= 1, name='ADMIN')
    insert_role(db, id= 2, name='USER')

    insert_userroles(db, id=1, user_id=1, role_id=1)
    insert_userroles(db, id=2, user_id=2, role_id=1)
    insert_userroles(db, id=3, user_id=3, role_id=2)
    insert_userroles(db, id=4, user_id=4, role_id=2)
    insert_userroles(db, id=5, user_id=5, role_id=2)
    insert_userroles(db, id=6, user_id=6, role_id=2)
    insert_userroles(db, id=7, user_id=7, role_id=2)
    insert_userroles(db, id=8, user_id=8, role_id=2)


def insert_user(db, id, email, name, surname):
    #sql = f"INSERT OR IGNORE INTO user (id, email, name, surname) VALUES ({id}, '{email}', '{name}', '{surname}')"
    user_data = {'id': id, 'email': email, 'name': name, 'surname': surname}
    sql = f"""
                MERGE INTO dbo.[user] AS target
                USING (SELECT :id AS id, :email AS email, :name AS name, :surname AS surname) AS source
                ON (target.id = source.id)
                WHEN MATCHED THEN
                    UPDATE SET email = source.email, name = source.name, surname = source.surname
                WHEN NOT MATCHED THEN
                    INSERT (id, email, name, surname) VALUES (:id, :email, :name, :surname);
            """
    db.session.execute(text(sql), user_data)
    db.session.commit()


def insert_role(db, id, name):
    #sql = f"INSERT OR IGNORE INTO role (id, name) VALUES ({id}, '{name}')"
    user_data = {'id': id, 'name': name}
    sql = f"""
                MERGE INTO dbo.role AS target
                USING (SELECT :id AS id, :name AS name) AS source
                ON (target.id = source.id)
                WHEN MATCHED THEN
                    UPDATE SET name = source.name
                WHEN NOT MATCHED THEN
                    INSERT (id, name) VALUES (:id, :name);
            """
    db.session.execute(text(sql), user_data)
    db.session.commit()


def insert_userroles(db, id, user_id, role_id):
    #sql = f"INSERT OR IGNORE INTO user_roles (id, user_id, role_id) VALUES ({id}, {user_id}, {role_id})"
    user_data = {'id': id, 'user_id': user_id, 'role_id': role_id}
    sql = f"""
                MERGE INTO dbo.user_roles AS target
                USING (SELECT :id AS id, :user_id AS user_id, :role_id AS role_id) AS source
                ON (target.id = source.id)
                WHEN MATCHED THEN
                    UPDATE SET id = source.id, user_id = source.user_id, role_id = source.role_id
                WHEN NOT MATCHED THEN
                    INSERT (id, name) VALUES (:id, :user_id, role_id);
            """
    db.session.execute(text(sql), user_data)
    db.session.commit()