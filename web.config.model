﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <system.webServer>
    <handlers accessPolicy="Read, Execute, Script">
      <add name="Python FastCGI" path="*" verb="*" modules="FastCgiModule" scriptProcessor="D:\WebSites\epro-bot\venv\Scripts\python.exe|D:\WebSites\epro-bot\venv\Lib\site-packages\wfastcgi.py" resourceType="Unspecified" requireAccess="Script" />
    </handlers>
  </system.webServer>
  <appSettings>
    <add key="WSGI_HANDLER" value="src.app" />            <!-- This will be get from key.private!!! -->
    <add key="PYTHONPATH" value="D:\WebSites\epro-bot" /> <!-- This will be get from key.private!!! -->
    <add key="PRELIMINARY_KEY" value="ABCD1234" />        <!-- This will be get from key.private!!! -->
  </appSettings>
</configuration>