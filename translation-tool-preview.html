<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Document Translation Tool - Preview</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        /* Color Palette */
        :root {
            --epr-blue: #14133B;
            --epr-gold: #cea941;
            --epr-spartan-blue: #7b8a9c;
            --epr-mushroom-forest: #8d8060;
            --epr-trailblazer: #bfb08f;
            --epr-cotton-seed: #bfbab0;
            --epr-fog: #d9d5d2;
            --epr-danger: #ee6a1f;
            --epr-secondary: #e1edf4;
            --epr-green: #00C89A;
            --epr-flower-blue: #433D6B;
            --epr-magnolia: #F4ECFF;

            /* New color palette from design specs */
            --corporate-primary: #1E1F41;
            --corporate-secondary: #7B869C;
            --corporate-light: #DFE7EA;
            --golden-primary: #7D653F;
            --golden-secondary: #9E8664;
            --golden-light: #BFAF8F;
            --warm-grey-primary: #85827A;
            --warm-grey-secondary: #BFBAB0;
            --warm-grey-light: #DFD5D2;
            --deep-teal-primary: #24639F;
            --deep-teal-secondary: #526D70;
            --deep-teal-light: #BCD4D2;
            --sustainability-primary: #6E826F;
            --sustainability-secondary: #A8B580;
            --sustainability-light: #C4D69A;
        }

        /* Base styles */
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            background-color: #f8f9fa;
        }

        /* Translation Tool Styles */
        .translation-container {
            background: transparent;
            min-height: 100vh;
            padding: 2rem 0;
        }

        .translation-card {
            background: white;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(30, 31, 65, 0.1);
            border: 1px solid rgba(30, 31, 65, 0.08);
            overflow: hidden;
        }

        .card-header-custom {
            background: linear-gradient(135deg, var(--corporate-primary) 0%, var(--deep-teal-primary) 100%);
            color: white;
            padding: 2rem;
            border: none;
        }

        .upload-area {
            border: 2px dashed var(--deep-teal-secondary);
            border-radius: 12px;
            padding: 3rem 2rem;
            text-align: center;
            background: linear-gradient(135deg, rgba(36, 99, 159, 0.05) 0%, rgba(82, 109, 112, 0.05) 100%);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-area:hover {
            border-color: var(--deep-teal-primary);
            background: linear-gradient(135deg, rgba(36, 99, 159, 0.1) 0%, rgba(82, 109, 112, 0.1) 100%);
            transform: translateY(-2px);
        }

        .upload-area.dragover {
            border-color: var(--sustainability-primary);
            background: linear-gradient(135deg, rgba(110, 130, 111, 0.1) 0%, rgba(168, 181, 128, 0.1) 100%);
        }

        .upload-icon {
            font-size: 3rem;
            color: var(--deep-teal-primary);
            margin-bottom: 1rem;
        }

        .file-info {
            background: linear-gradient(135deg, rgba(110, 130, 111, 0.1) 0%, rgba(196, 214, 154, 0.1) 100%);
            border: 1px solid var(--sustainability-light);
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1rem;
        }

        .language-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1.5rem;
            margin: 1.5rem 0;
        }

        .form-select-custom {
            border: 2px solid var(--warm-grey-secondary);
            border-radius: 8px;
            padding: 0.75rem 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-select-custom:focus {
            border-color: var(--deep-teal-primary);
            box-shadow: 0 0 0 0.2rem rgba(36, 99, 159, 0.25);
        }

        .excel-options {
            background: linear-gradient(135deg, rgba(125, 102, 63, 0.05) 0%, rgba(191, 175, 143, 0.05) 100%);
            border: 1px solid var(--golden-light);
            border-radius: 12px;
            padding: 1.5rem;
            margin-top: 1.5rem;
        }

        .column-checkbox {
            margin: 0.5rem 0;
        }

        .column-checkbox input[type="checkbox"] {
            margin-right: 0.5rem;
            transform: scale(1.2);
        }

        .btn-translate {
            background: linear-gradient(135deg, var(--sustainability-primary) 0%, var(--sustainability-secondary) 100%);
            border: none;
            color: white;
            padding: 0.75rem 2rem;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 16px rgba(110, 130, 111, 0.3);
        }

        .btn-translate:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(110, 130, 111, 0.4);
            background: linear-gradient(135deg, var(--sustainability-secondary) 0%, var(--sustainability-light) 100%);
            color: white;
        }

        .btn-translate:disabled {
            background: var(--warm-grey-secondary);
            box-shadow: none;
            cursor: not-allowed;
        }

        .progress-container {
            margin-top: 2rem;
            padding: 1.5rem;
            background: linear-gradient(135deg, rgba(36, 99, 159, 0.05) 0%, rgba(82, 109, 112, 0.05) 100%);
            border-radius: 12px;
            border: 1px solid var(--deep-teal-light);
        }

        .progress-custom {
            height: 8px;
            border-radius: 4px;
            background-color: var(--warm-grey-light);
        }

        .progress-bar-custom {
            background: linear-gradient(90deg, var(--deep-teal-primary) 0%, var(--sustainability-primary) 100%);
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .status-text {
            color: var(--corporate-primary);
            font-weight: 500;
            margin-top: 0.5rem;
        }

        @media (max-width: 768px) {
            .language-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            
            .translation-container {
                padding: 1rem 0;
            }
            
            .card-header-custom {
                padding: 1.5rem;
            }
            
            .upload-area {
                padding: 2rem 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="translation-container">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-10 col-xl-8">
                    <div class="translation-card">
                        <div class="card-header-custom">
                            <h1 class="mb-0">
                                <i class="fas fa-language me-3"></i>
                                AI Document Translation Tool
                            </h1>
                            <p class="mb-0 mt-2 opacity-75">
                                Translate Excel, PowerPoint, and Word documents with AI precision
                            </p>
                        </div>
                        
                        <div class="card-body p-4">
                            <!-- File Upload Section -->
                            <div class="mb-4">
                                <h5 class="mb-3">
                                    <i class="fas fa-upload me-2"></i>
                                    Upload Document
                                </h5>
                                
                                <div class="upload-area" id="uploadArea">
                                    <div class="upload-icon">
                                        <i class="fas fa-cloud-upload-alt"></i>
                                    </div>
                                    <h6>Drag & drop your file here</h6>
                                    <p class="text-muted mb-3">or click to browse</p>
                                    <p class="small text-muted">
                                        Supported formats: Excel (.xlsx), PowerPoint (.pptx), Word (.docx)
                                        <br>Maximum file size: 50MB
                                    </p>
                                    <input type="file" id="fileInput" accept=".xlsx,.pptx,.docx" style="display: none;">
                                </div>
                                
                                <div id="fileInfo" class="file-info" style="display: none;">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-file me-3 text-success"></i>
                                        <div class="flex-grow-1">
                                            <div class="fw-bold" id="fileName">sample-document.xlsx</div>
                                            <div class="small text-muted" id="fileDetails">2.5 MB • XLSX file</div>
                                        </div>
                                        <button class="btn btn-sm btn-outline-danger" id="removeFile">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Language Selection -->
                            <div class="mb-4">
                                <h5 class="mb-3">
                                    <i class="fas fa-globe me-2"></i>
                                    Language Settings
                                </h5>
                                
                                <div class="language-grid">
                                    <div>
                                        <label for="sourceLanguage" class="form-label fw-semibold">Source Language</label>
                                        <select class="form-select form-select-custom" id="sourceLanguage">
                                            <option value="auto" selected>Auto-detect</option>
                                            <option value="en">English</option>
                                            <option value="es">Spanish</option>
                                            <option value="fr">French</option>
                                            <option value="de">German</option>
                                            <option value="it">Italian</option>
                                        </select>
                                    </div>
                                    
                                    <div>
                                        <label for="targetLanguage" class="form-label fw-semibold">Target Language</label>
                                        <select class="form-select form-select-custom" id="targetLanguage">
                                            <option value="">Select target language</option>
                                            <option value="en">English</option>
                                            <option value="es" selected>Spanish</option>
                                            <option value="fr">French</option>
                                            <option value="de">German</option>
                                            <option value="it">Italian</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- Excel Column Selection -->
                            <div id="excelOptions" class="excel-options">
                                <h5 class="mb-3">
                                    <i class="fas fa-table me-2"></i>
                                    Excel Column Selection
                                </h5>
                                <p class="text-muted mb-3">Select which columns you want to translate:</p>
                                
                                <div class="row" id="columnCheckboxes">
                                    <div class="col-md-6 column-checkbox">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" value="Name" id="col0" checked>
                                            <label class="form-check-label" for="col0">Name</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6 column-checkbox">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" value="Description" id="col1" checked>
                                            <label class="form-check-label" for="col1">Description</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6 column-checkbox">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" value="Comments" id="col2" checked>
                                            <label class="form-check-label" for="col2">Comments</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6 column-checkbox">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" value="Notes" id="col3">
                                            <label class="form-check-label" for="col3">Notes</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Translation Button -->
                            <div class="text-center mt-4">
                                <button class="btn btn-translate btn-lg" id="translateBtn">
                                    <i class="fas fa-magic me-2"></i>
                                    Start Translation
                                </button>
                            </div>

                            <!-- Progress Section -->
                            <div id="progressSection" class="progress-container" style="display: none;">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span class="fw-semibold">Translation Progress</span>
                                    <span id="progressPercent">65%</span>
                                </div>
                                <div class="progress progress-custom">
                                    <div class="progress-bar progress-bar-custom" id="progressBar" role="progressbar" style="width: 65%"></div>
                                </div>
                                <div class="status-text" id="statusText">Processing with AI translation...</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Simple demo functionality
        document.getElementById('translateBtn').addEventListener('click', function() {
            document.getElementById('progressSection').style.display = 'block';
            this.disabled = true;
            
            // Simulate progress
            let progress = 0;
            const interval = setInterval(() => {
                progress += Math.random() * 15;
                if (progress > 100) progress = 100;
                
                document.getElementById('progressBar').style.width = progress + '%';
                document.getElementById('progressPercent').textContent = Math.round(progress) + '%';
                
                if (progress >= 100) {
                    clearInterval(interval);
                    document.getElementById('statusText').textContent = 'Translation complete!';
                    this.disabled = false;
                }
            }, 300);
        });

        // Show file info demo
        document.getElementById('uploadArea').addEventListener('click', function() {
            document.getElementById('uploadArea').style.display = 'none';
            document.getElementById('fileInfo').style.display = 'block';
        });

        document.getElementById('removeFile').addEventListener('click', function() {
            document.getElementById('fileInfo').style.display = 'none';
            document.getElementById('uploadArea').style.display = 'block';
        });
    </script>
</body>
</html>
