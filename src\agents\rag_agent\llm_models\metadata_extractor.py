import pandas as pd
from os.path import dirname, join
from json import load

from langchain.schema.runnable import RunnablePassthrough
from langchain_core.output_parsers import <PERSON>sonOutputParser, StrOutputParser
from langchain_core.prompts import ChatPromptTemplate
from langchain_openai import AzureOpenAI
from argparse import ArgumentParser, Namespace
from config.config import RAGConfig
from utils.core import  get_logger

FILE_DIR = dirname(__file__)
logger = get_logger(__file__)

    
class MetadataExtractor():
    def __init__(self, model) -> None:
        config = RAGConfig()

        self.llm = model._client
        
        self.metadata_file = join(FILE_DIR, "./metadata_to_extract.json")
        self.base_prompt = """Your task is to extract specific information from the user's query and structure it into a JSON format with the following fields:\n\n"""
        self.instruction_prompt = """INSTRUCTIONS\n- Respond only with the JSON format, with no additional explanations.\n- JSON fields must be translated to english.\n\n"""
        self.example_prompt = """EXAMPLES:\nUser query:give me the list of the spare part codes in the spare part catalogue for internal code 98231321\nJSON: "language_description": "English", "document_number": "missing", "product_code": "98231321"\n\nUser query: I need the edition 9 of the spare part catalogue number 560100a\nJSON: "language_description": "English", document_number": "560100a", "product_code": "missing", "edition": "9"\n\nUser query: Voglio il manuale di installazione per il codice prodotto 1L9023\nJSON: "language_description": "Italian", document_number": "missing", "product_code": "1L9023", "edition": "9"\n\nUser query: Voglio il manuale di installazione per il codice prodotto 1L9023 in Spagnolo\nJSON: "language_description": "Spanish", document_number": "missing", "product_code": "1L9023", "edition": "9"\n\nUser query: I want the sweden operating manual edition 2 number 213123\nJSON: "language_description": "Sweden", document_number": "213123", "product_code": "missing", "edition": "2"\n\n"""
        self.prompt = """User query: {query}\nJSON:\n"""
        
        self.is_food_or_laundry_prompt = """Categorize each sentence as "FOOD", "LAUNDRY", or "missing" based on the type of appliance referred to.

                                            - Use "FOOD" if the sentence refers to an appliance involved with food or the sentence contains the word "FOOD".
                                            - Use "LAUNDRY" if the sentence refers to an appliance involved with laundry sentence contains the word "LAUNDRY".
                                            - Use "missing" if you can't categorize the appliance as related to food or laundry.

                                            # Steps

                                            1. Read the sentence carefully.
                                            2. Identify any appliance mentioned.
                                            3. Determine if the appliance is involved with food or laundry.
                                            4. Categorize the sentence as "FOOD", "LAUNDRY", or "missing".

                                            # Output Format

                                            Provide a single category response: "FOOD", "LAUNDRY", or "missing".

                                            # Examples

                                            **Input:** "The refrigerator keeps my vegetables fresh."
                                            **Output:** FOOD

                                            **Input:** "The washing machine makes it easy to clean clothes."
                                            **Output:** LAUNDRY

                                            **Input:** "My favorite chair is very comfortable."
                                            **Output:** missing 

                                            **Input:** "food"
                                            **Output:** FOOD 

                                            **Input:** "laundry"
                                            **Output:** LAUNDRY 

                                            **Input:** "food section"
                                            **Output:** FOOD 

                                            **Input:** "laundry section"
                                            **Output:** LAUNDRY 
                                            
                                            **Input:** {query}
                                            **Output:**
                                            """
        
        self.document_type_prompt = """

            Your task is to identify the type of document the user is requesting based on their input. Use specific predefined terms and their corresponding abbreviations to classify the document type, and reason through the input to ensure accurate identification.

            # Instructions:

            1. **Identify Terms**: Examine the user's input to find references to specific document types listed below. Match the input text to the terms exactly as described. Use the corresponding abbreviations for each document type.

            2. **Reasoning**: Analyze the input text to determine all possible matches. 
               - If multiple matches are found, list all of them in the output.
               - If a single match is determined, list only that match.
             - While reasoning give a percentage probability of the estimated document type, if it's not 100%, provide all the possible options you found. 

            3. **Default Behavior**: If none of the listed document types are mentioned in the input text, give as document types `Service Manual`, `Installation Manual` and `Handbooks`.

            4. **Default Behavior for Instruction queries**: If the question asked for instrction ALWAYS give as document types `Service Manual`, `Installation Manual`, 'Installation Instruction' and 'Handbook'.

            # Document Types and Abbreviations:

            - `Service Manual`: fill with `'SM'`
            - `Installation Manual`: fill with `'IN'`
            - `Handbooks`: fill with `'HB'`
            - `Spare Part Catalog`: fill with `'SPC'`
            - `Technical Bulletin`: fill with `'TB'`
            - `Technical Information`: fill with `'TB'`
            - `Installation Instruction`: fill with `'II'`
            - `Product Data Sheet`: fill with `'PDS'`
            - `Spare Part Product Data Sheet`: fill with `'SPPDS'`
            - `Spare Part Photo`: fill with `'SPPH'`
            - `Spare Part Original Component Sticker`: fill with `'SPOCS'`
            - `Operating Manual`: fill with `'OM'`
            - `Operating Instruction`: fill with `'OI'`
            - `Commissioning & Performance Maintenance`: fill with `'CPM'`
            - `User Maintenance`: fill with `'UM'`
            - `Conformity Certificates`: fill with `'COC'`
            - `Conformity Declaration`: fill with `'DC'`
            - `Programming Manual`: fill with `'PM'`
            - `Technical Videos`: fill with `'TEVI'`
            - `Spare Part General Purpose`: fill with `'SPGP'`
            - `Spare Part Instruction`: fill with `'SPI'`
            - `Spare Part Technical Videos`: fill with `'SPVID'`
            - `Spare Parts Instructions for Kit`: fill with `'SPIK'`
            - `Instructions`: fill with `'IS'` (Do not confuse with other types of instructions.)
            - `Guidelines`: fill with `'GUI'`
            - `Wall Instruction`: fill with `'WI'`
            - `Programming File`: fill with `'PRF'`
            - `Electrical Wiring Diagram`: fill with `'EWD'`
            - 'Product Wiring Diagram (referred also as PWD)' : fill with `'EWD'`
            - 'Photo': fill with `'PH'`

            # Steps:

            1. Parse the user's input to locate keywords or phrases that match the document types listed above.
            2. Use reasoning to clarify ambiguous or partial matches where appropriate.
            3. Only if no matches are found and no document type of the ones listed above are identified, give as document types `Service Manual`, `Installation Manual` and `Handbooks`.

            # Output Format:

                - Provide the final output in a **JSON array** format, including all identified document types. If no document types are found, give as document types ONLY `Service Manual`, `Installation Manual` and `Handbooks`.

            # Example:

            **Input 1**: "I need the Service Manual and Installation Instruction for this product."
            - **Reasoning**:
              - "Service Manual" matches, corresponding to `'SM'`.
              - "Installation Instruction" matches, corresponding to `'II'`.
            - **Output**: `"SM", "II"`

            **Input 2**: "I would like a Conformity Certificate for my equipment."
            - **Reasoning**:
              - "Conformity Certificate" matches, corresponding to `'COC'`.
            - **Output**: `"COC"`

            **Input 3**: "Please provide any available guidelines or technical videos."
            - **Reasoning**:
              - "Guidelines" matches, corresponding to `'GUI'`.
              - "Technical Videos" matches, corresponding to `'TEVI'`.
            - **Output**: `"GUI", "TEVI"`

            **Input 4**: "How can i mount the dryer?"
            - **Reasoning**:
                - There is no a perfect match, based on the question, it seems the user is requesting information on an installation,so could be Installation Manual, Service Manual or Operating Manual
            - **Output**: `"IN", "OM", "SM"`

            # Notes:
            - Prioritize reasoning outputs transparently by referencing the exact phrases within the user query.
            - Avoid confusion between terms with overlapping partial phrases (e.g., "Instructions" vs. "Operating Instruction").
            - Provide only the array output without explanation

            
            Query: {query}  
            Intent: """
        
    def build_metadata_prompt(self, missing_metadata: list[str]): 
        
        # Load metadata from file
        with open(self.metadata_file) as file:
            metadata_to_extract = load(file)

        # Filter metadata based on the missing_metadata list if it's not empty
        if missing_metadata:
            metadata_to_extract = [
                (metadata, description) 
                for metadata, description in metadata_to_extract.items()
                if metadata in missing_metadata
            ]
        else:
            metadata_to_extract = [
                (metadata, description) 
                for metadata, description in metadata_to_extract.items()
            ]

        # Create the prompt string
        prompt = "JSON:\n"
        prompt += ''.join(
            f"""- "{metadata}": {description}\n"""
            for metadata, description in metadata_to_extract
        )
        prompt += f"\n{self.instruction_prompt+self.example_prompt+self.prompt}"

        return prompt
    
    def extract_document_type(self, query:str) -> str:
        try:
            input = {
                "query": RunnablePassthrough()
            }
            prompt_template = ChatPromptTemplate.from_template(self.document_type_prompt)
            prompt = input | prompt_template
            document_type_chain = prompt | self.llm | JsonOutputParser()
            document_type = document_type_chain.invoke(query)
            return ",".join(document_type)
        except Exception as e:
            logger.error("ERROR: Error in extracting document type")
        

                        
    def generate(self, query: str, missing_metadata: list) -> dict:
        
        prompt_variables = {
            "query": RunnablePassthrough()
        }
        
        builded_prompt =self.base_prompt + self.build_metadata_prompt(missing_metadata)
        logger.info(f"Builded prompt for extract metadata:\n {builded_prompt}")
        prompt_string = ChatPromptTemplate.from_template(builded_prompt)
        prompt = prompt_variables | prompt_string

        chain = prompt | self.llm | JsonOutputParser()
        answer = chain.invoke(query)
        return answer
    
    def is_food_or_laundry(self, query: str) -> str:

        prompt_variables = {
            "query": RunnablePassthrough()
        }

        logger.info(f"Prompt for checking if a question is related to food or laundry section: \n {self.is_food_or_laundry_prompt}")

        prompt_string_template = ChatPromptTemplate.from_template(self.is_food_or_laundry_prompt)
        prompt = prompt_variables | prompt_string_template

        chain = prompt | self.llm | StrOutputParser()

        return chain.invoke(query)
    
    def get_item_or_sparse_from_query(self, query:str):
        prompt_variables = {
                "query": RunnablePassthrough()
            }
        base_prompt = """Your task is to undestand if the query is related to spare or item. Categorize the query as spars as S or as item as I, returnin S or I as a string\n\n"""
        instruction_prompt = """INSTRUCTIONS\n- Respond only with the string , with no additional explanations.\n\n"""
        example_prompt = """EXAMPLES:\nUser query:give me the list of the spare part codes in the spare part catalogue for internal code 98231321\n"S"\n\nUser query: I need the edition 9 of the spare part catalogue number 560100a\n"S"\n\nUser query: Voglio il manuale di installazione per il codice prodotto 1L9023\n"1L9023"\n\nUser query: Voglio il manuale di installazione per il codice prodotto 1L9023 in Spagnolo\n"I"\n\nUser query: I want the sweden operating manual edition 2 number 213123\n"I"\n\n"""
        prompt = """User query: {query}\n\n"""
        
        builded_prompt = base_prompt + instruction_prompt + example_prompt + prompt
        logger.info(f"Builded prompt for extract metadata:\n {builded_prompt}")
        prompt_string = ChatPromptTemplate.from_template(builded_prompt)
        prompt = prompt_variables | prompt_string

        chain = prompt | self.llm | JsonOutputParser()
        answer = chain.invoke(query)
        return answer

    def get_code_from_query(self, query:str):
        prompt_variables = {
                "query": RunnablePassthrough()
            }
        base_prompt = """Your task is to extract the numeric code from the user's query and structure it into a strings if the code extracted is one otherwise in a list of strings\n\n"""
        instruction_prompt = """INSTRUCTIONS\n- Respond only with the string or list of strings, with no additional explanations.\n\n"""
        example_prompt = """EXAMPLES:\nUser query:give me the list of the spare part codes in the spare part catalogue for internal code 98231321\n"98231321"\n\nUser query: I need the edition 9 of the spare part catalogue number 560100a\n"560100a"\n\nUser query: Voglio il manuale di installazione per il codice prodotto 1L9023\n"1L9023"\n\nUser query: Voglio il manuale di installazione per il codice prodotto 1L9023 in Spagnolo\n"1L9023"\n\nUser query: I want the sweden operating manual edition 2 number 213123\n"213123"\n\n"""
        prompt = """User query: {query}\n\n"""
        
        builded_prompt = base_prompt + instruction_prompt + example_prompt + prompt
        logger.info(f"Builded prompt for extract metadata:\n {builded_prompt}")
        prompt_string = ChatPromptTemplate.from_template(builded_prompt)
        prompt = prompt_variables | prompt_string

        chain = prompt | self.llm | JsonOutputParser()
        answer = chain.invoke(query)
        return answer


def get_args() -> Namespace:
    parser = ArgumentParser(
        description="A retrieval augmented generator that answers questions about a set of Pride documents."
    )
    parser.add_argument(
        "file_path",
        type=str,
        help="The natural language inquiry about the documents in the set.",
    )
    return parser.parse_args()


if __name__ == "__main__":
    args = get_args()

    extractor = MetadataExtractor()

    file_path_test_set = args.file_path
    test_set_dataframe = pd.read_excel(file_path_test_set)

    correct_prediction = 0
    questions_count = len(test_set_dataframe)


    for index,question in test_set_dataframe.iterrows():
        metadata = extractor.generate(question["QUESTION"], [])

        document_type_predicted = metadata.get("document_type", "None")
        language_description_predicted = metadata.get("language_description", "None")
        product_code_predicted = metadata.get("product_code", "None")
        edition_predicted = metadata.get("edition", "None")
        document_number_predicted = metadata.get("document_number", "None")


        test_set_dataframe.at[index, "DOCUMENT_TYPE"] = document_type_predicted,
        test_set_dataframe.at[index, "LANGUAGE_DESCRIPTION"] = language_description_predicted,
        test_set_dataframe.at[index, "PRODUCT_CODE"] = product_code_predicted,
        test_set_dataframe.at[index, "EDITION"] = edition_predicted,
        test_set_dataframe.at[index, "DOCUMENT_NUMBER"] = document_number_predicted
        
    
    test_set_dataframe.to_excel(file_path_test_set, index=False)
