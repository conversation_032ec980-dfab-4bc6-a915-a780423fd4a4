CREATE TABLE DWH_PUBLIC.SERIAL_NUMBERS (
	COMPANY VARCHAR2(5 BYTE),
	COMPANY_DESCRIPTION VARCHAR2(50 BYTE),
	PLANT VARCHAR2(12 BYTE),
	PLANT_DESCRIPTION VARCHAR2(50 BYTE),
	ITEM_CODE VARCHAR2(25 BYTE),
	ITEM_INTERNAL_CODE VARCHAR2(10 BYTE),
	SHORT_ITEM_CODE VARCHAR2(10 BYTE),
	MAIN_ITEM_DESCRIPTION VARCHAR2(160 BYTE),
	ITEM_SERIAL_NUMBER VARCHAR2(30 BYTE),
	ITEM_PRODUCED_DATE DATE,
	ITEM_PRODUCED_WORKORDER_NUMBER VARCHAR2(8 BYTE),
	ITEM_PRODUCED_CUTOMER_ORDER_NUMBER VARCHAR2(13 BYTE),
	ITEM_PRODUCED_CUTOMER_ORDER_LINE VARCHAR2(10 BYTE),
	ITEM_PRODUCED_QUANTITY NUMBER(10, 0),
	SYSTEM_SOURCE VARCHAR2(10 BYTE),
	PRIMARY <PERSON> (COMPANY, PLANT, <PERSON>EM_CODE, ITEM_SERIAL_NUMBER, SYSTEM_SOURCE)
);