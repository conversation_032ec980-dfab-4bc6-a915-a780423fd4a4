import asyncio
from argparse import Argument<PERSON>arser, Namespace
from enum import StrEnum, auto
from tqdm.auto import tqdm
from rag_indexer.src.indexer.fetcher import fetch_uris
from rag_indexer.src.indexer.loader import DocumentLoader, SourceType
from rag_indexer.src.indexer.splitter import DocumentSplitter
from src.backend.contracts.chat_data import BotType

from rag_indexer.src.indexer.storer import DocumentStorer
from utils.core import get_logger

from src.backend.models import SyncDocumentation
from src import db, app

logger = get_logger(__file__)

class Source(StrEnum):
    LOCAL = auto()
    ONLINE = auto()


def get_args() -> Namespace:
    parser = ArgumentParser(
        prog="RAG Indexer",
        description="Loads, splits, and uploads a set of documents for RAG-accessibility.",
    )
    parser.add_argument(
        "source",
        type=Source,
        help='A string indicating where the documents are located. Must be either "local" or "online".',
        choices=list(Source),
    )

    parser.add_argument(
        "bot",
        type=str,
        help='A string indicating where the documents are located. Must be either "local" or "online".',
    )

    return parser.parse_args()


async def index_documents(
    loader: DocumentLoader, splitter: DocumentSplitter, storer: DocumentStorer
):
    for batch in tqdm(loader.load(5)):
        # await update_document_processing_status(batch, 'Elab') #only for pride
        logger.info("Status of batch updated to: 'ELAB'")
        batch_splits = await asyncio.gather(
            *(splitter.split(document) for document in batch)
        )

        await asyncio.gather(
            *(storer.upload_splits(document_splits) for document_splits in batch_splits)
        )
        # await update_document_processing_status(batch, 'OK') #Only for pride
        logger.info("Status of batch updated to: 'OK'")
        with open('performance_metrics.log', 'a') as file:
            print('5 files processed', file=file)


async def update_document_processing_status(batch: list, status: str):
    
    with app.app_context():
        for document in batch: 
                if "Attach\\"in document.metadata["url"]:
                    file_path = document.metadata["url"].split("Attach\\")[1].replace("\\", "/")
                else: 
                    file_path = document.metadata["url"].replace("\\", "/")
                file = db.session.query(SyncDocumentation).filter(SyncDocumentation.doc_name == file_path).first()
                if file:
                    setattr(file, 'status', status)
                    db.session.commit()
                else:
                    new_file = SyncDocumentation(doc_name=file_path,
                                                 status=status,
                                                 document_updated=document.metadata["document_update"],
                                                 document_type=document.metadata["document_type"])
                    db.session.add(new_file)
                    db.session.commit()


if __name__ == "__main__":
    args = get_args()
    source_type = (
        SourceType.LOCAL_FILE_SYSTEM
        if args.source == Source.LOCAL
        else SourceType.REMOTE_FILE_SYSTEM
    )

    bot_name = args.bot

    if bot_name in BotType.__members__:
        
        loader = DocumentLoader(source_type)
        files = fetch_uris(source_type,bot_name)
        loader.add_documents(files)

        splitter = DocumentSplitter(3, bot_name)

        storer = DocumentStorer(bot_name)

        asyncio.run(index_documents(loader, splitter, storer))

