{% extends 'base.html' %}
{% block content %}
<div class="container mt-4">
    <h2>Gestione Utenti</h2>
    <div class="d-flex justify-content-between align-items-center mb-3">
        <a href="{{ url_for('rbot_admin.rbot_admin_users.add_user') }}" class="btn btn-primary">Aggiungi Utente</a>
        <form class="d-flex" method="get" action="{{ url_for('rbot_admin.rbot_admin_users.users_list') }}">
            <input class="form-control me-2" type="search" name="search" placeholder="Cerca per email" value="{{ request.args.get('search', '') }}">
            <button class="btn btn-outline-secondary" type="submit">Cerca</button>
        </form>
    </div>
    <table class="table table-bordered table-hover">
        <thead>
            <tr>
                <th>ID</th>
                <th>Email</th>
                <th>Nome</th>
                <th>Cognome</th>
                <th>Ruoli</th>
                <th>Azioni</th>
            </tr>
        </thead>
        <tbody>
            {% for user in users.items %}
            <tr>
                <td>{{ user.id }}</td>
                <td>{{ user.email }}</td>
                <td>{{ user.name }}</td>
                <td>{{ user.surname }}</td>
                <td>{% for role in user.roles %}{{ role.name }}{% if not loop.last %}, {% endif %}{% endfor %}</td>
                <td>
                    <a href="{{ url_for('rbot_admin.rbot_admin_users.edit_user', user_id=user.id) }}" class="btn btn-sm btn-warning">Modifica</a>
                    <button class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal{{ user.id }}">Elimina</button>
                    <!-- Modal conferma eliminazione -->
                    <div class="modal fade" id="deleteModal{{ user.id }}" tabindex="-1" aria-labelledby="deleteModalLabel{{ user.id }}" aria-hidden="true">
                      <div class="modal-dialog">
                        <div class="modal-content">
                          <div class="modal-header">
                            <h5 class="modal-title" id="deleteModalLabel{{ user.id }}">Conferma eliminazione</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                          </div>
                          <div class="modal-body">
                            Sei sicuro di voler eliminare l'utente <b>{{ user.email }}</b>?
                          </div>
                          <div class="modal-footer">
                            <form method="post" action="{{ url_for('rbot_admin.rbot_admin_users.delete_user', user_id=user.id) }}">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annulla</button>
                                <button type="submit" class="btn btn-danger">Elimina</button>
                            </form>
                          </div>
                        </div>
                      </div>
                    </div>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    <!-- Paginazione -->
    <nav>
      <ul class="pagination">
        {% if users.has_prev %}
          <li class="page-item"><a class="page-link" href="{{ url_for('rbot_admin.rbot_admin_users.users_list', page=users.prev_num) }}">Precedente</a></li>
        {% else %}
          <li class="page-item disabled"><span class="page-link">Precedente</span></li>
        {% endif %}
        {% for page_num in users.iter_pages() %}
          {% if page_num %}
            {% if page_num == users.page %}
              <li class="page-item active"><span class="page-link">{{ page_num }}</span></li>
            {% else %}
              <li class="page-item"><a class="page-link" href="{{ url_for('rbot_admin.rbot_admin_users.users_list', page=page_num) }}">{{ page_num }}</a></li>
            {% endif %}
          {% else %}
            <li class="page-item disabled"><span class="page-link">…</span></li>
          {% endif %}
        {% endfor %}
        {% if users.has_next %}
          <li class="page-item"><a class="page-link" href="{{ url_for('rbot_admin.rbot_admin_users.users_list', page=users.next_num) }}">Successiva</a></li>
        {% else %}
          <li class="page-item disabled"><span class="page-link">Successiva</span></li>
        {% endif %}
      </ul>
    </nav>
</div>
{% endblock %}
