import React, { useState, useEffect } from "react";
import { Stack, IconButton } from "@fluentui/react";
import { ThumbLike20Regular, ThumbDislike20Regular, ThumbLike20Filled, ThumbDislike20Filled } from "@fluentui/react-icons";
import { FeedbackType, ChatResponse } from "../../../api";
import { AnswerButtons } from "./AnswerButtons";

interface Props {
    chatResponse: ChatResponse;
    onFeedbackClicked?: (dialogId: string, feedback: FeedbackType) => void;
    onExcelClicked?: (dialogId: string) => void;
    conversationId: string;
    dialogId: string;
    selectedBot: string | number | null;
}

export const AnswerActions = ({
    onExcelClicked,
    selectedBot,
    chatResponse,
    conversationId,
    dialogId,
    onFeedbackClicked
}: Props) => {
    // Usa il dialog_id dalla chatResponse invece che quello passato come prop
    const currentDialogId = chatResponse.dialog_id || dialogId;

    // Inizializza lo stato con il feedback iniziale se presente
    const [feedbackState, setFeedbackState] = useState<FeedbackType | undefined>(
        chatResponse.answer?.feedback
    );

    const [isLoading, setIsLoading] = useState(false);

    const showExcelButton = onExcelClicked &&
        selectedBot !== 'EPROEXCELLA_BOT' &&
        chatResponse.answer?.query != null;

    const handleFeedbackClick = async (feedback: FeedbackType) => {
        if (onFeedbackClicked) {
            try {
                setIsLoading(true);
                // Usa il dialog_id dalla chatResponse
                await onFeedbackClicked(currentDialogId, feedback);
                setFeedbackState(feedback);
            } catch (error) {
                setFeedbackState(chatResponse.answer?.feedback);
                console.error('Errore nell\'aggiornamento del feedback:', error);
            } finally {
                setIsLoading(false);
            }
        }
    };

    const renderFeedbackButtons = () => (
        <>
            <IconButton
                styles={{
                    root: { color: 'black' },
                    rootHovered: { color: 'blue' },
                }}
                title="Give a positive 👍🏼 feedback to this answer"
                ariaLabel="Give a positive 👍🏼 feedback to this answer"
                onClick={() => handleFeedbackClick(FeedbackType.Good)}
                disabled={isLoading}
            >
                {feedbackState === FeedbackType.Good ?
                    <ThumbLike20Filled /> :
                    <ThumbLike20Regular />
                }
            </IconButton>

            <IconButton
                styles={{
                    root: { color: 'black' },
                    rootHovered: { color: 'blue' },
                }}
                title="Give a negative 👎🏼 feedback to this answer"
                ariaLabel="Give a negative 👎🏼 feedback to this answer"
                onClick={() => handleFeedbackClick(FeedbackType.Bad)}
                disabled={isLoading}
            >
                {feedbackState === FeedbackType.Bad ?
                    <ThumbDislike20Filled /> :
                    <ThumbDislike20Regular />
                }
            </IconButton>
        </>
    );
    

    return (
        <Stack.Item>
            <Stack horizontal horizontalAlign="space-between">
                <AnswerButtons
                    onExcelClicked={onExcelClicked}
                    selectedBot={selectedBot}
                    chatResponse={chatResponse}
                    conversationId={conversationId}
                    dialogId={dialogId}
                ></AnswerButtons>
                <div>
                    {onFeedbackClicked && renderFeedbackButtons()}
                </div>
            </Stack>
        </Stack.Item>
    );
};