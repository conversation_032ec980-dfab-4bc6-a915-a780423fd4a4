from enum import CONTINUOUS, UNIQUE, Enum, verify
from logging import Lo<PERSON>, getLogger
from os.path import basename, dirname, isfile, join
from abc import ABCMeta

class Singleton(ABCMeta):
    _instances = {}

    def __call__(cls, *args, **kwargs):
        if cls not in cls._instances:
            cls._instances[cls] = super(Singleton, cls).__call__(*args, **kwargs)
        return cls._instances[cls]


@verify(UNIQUE, CONTINUOUS)
class SourceType(Enum):
    LOCAL_FILE_SYSTEM = 0
    REMOTE_FILE_SYSTEM = 1


def get_logger(file_path: str) -> Logger:
    """Returns a logger for the specified file.

    Args:
        file_path (str): The path of the file that must be logged.

    Returns:
        Logger: The logger for the give file.
    """
    assert isfile(file_path), "Must log an existing file."
    return getLogger(basename(dirname(file_path)))


def get_relative_path(base_file_path: str, relative_path: str) -> str:
    return join(dirname(base_file_path), relative_path)
