import React from "react";
import { Stack, IconButton } from "@fluentui/react";
import { getCitationFilePath } from "../../../api";
import { HtmlParsedAnswer } from "../AnswerParser";
import styles from "../Answer.module.css";

interface Props {
    parsedAnswer: HtmlParsedAnswer;
    onCitationClicked: (filePath: string) => void;
}

export const Citations = ({
    parsedAnswer,  
    onCitationClicked
}: Props) => {
    return(
        <>
            {!!parsedAnswer.citations.length && (
                <Stack.Item>
                    <Stack horizontal wrap tokens={{ childrenGap: 5 }}>
                        <span className={styles.citationLearnMore}>Citations:</span>
                        {parsedAnswer.citations.map((citation, i) => {
                            const path = getCitationFilePath(citation);
                            return (
                                <a
                                    key={i}
                                    className={styles.citation}
                                    title={citation}
                                    onClick={() => onCitationClicked(path)}
                                >
                                    {`${i + 1}. ${citation}`}
                                </a>
                            );
                        })}
                    </Stack>
                </Stack.Item>
            )}
        </>
    )
}