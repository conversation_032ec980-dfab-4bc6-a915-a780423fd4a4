"""added new column for sync document

Revision ID: 8b3943bdcc7e
Revises: d161c66234c9
Create Date: 2024-10-02 16:12:27.751217

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '8b3943bdcc7e'
down_revision = 'd161c66234c9'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('sync_documentation', schema=None) as batch_op:
        batch_op.add_column(sa.Column('document_type', sa.String(length=1000), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('sync_documentation', schema=None) as batch_op:
        batch_op.drop_column('document_type')

    # ### end Alembic commands ###
