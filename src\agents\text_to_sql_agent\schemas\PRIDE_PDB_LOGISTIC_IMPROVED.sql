-- Fixed version of the CREATE TABLE statement
CREATE TABLE DWH_PUBLIC.PRIDE_PDB_LOGISTIC_IMPROVED (
  "SINTCODE" VARCHAR2(25 CHAR) NOT NULL ENABLE,
  "DPLANPRODSTART" DATE,
  "DPRODSTART" DATE,
  "DPRODSTOP" DATE,
  "DDISTRIBSTART" DATE,
  "DDISTRIBSTOP" DATE,
  "SCUSTOMCLASS" VARCHAR2(12 CHAR),
  "STAXCODE" VARCHAR2(2 CHAR),
  "SACCOUNTING" VARCHAR2(4 CHAR),
  "SACCOUNTING_DESCRIPTION" NVARCHAR2(400),
  "CSALEWAY" CHAR(1 CHAR),
  "CSALEWAY_DESCRIPTION" NVARCHAR2(400),
  "BPICKLIST" NUMBER(3,0),
  "CMATERIAL" CHAR(2 CHAR),
  "CMATERIAL_DESCRIPTION" NVARCHAR2(400),
  "ILOCKEDDAY" NUMBER(10,0),
  "ILEADTIME" NUMBER(10,0),
  "CSTATUS" CHAR(1 CHAR),
  "SREPLACEDBY" VARCHAR2(25 CHAR),
  "IREPLACEDQTY" NUMBER(10,0),
  "SREPLACING" VARCHAR2(500 CHAR),
  "CFINDING" CHAR(1 CHAR),
  "CFINDING_DESCRIPTION" NVARCHAR2(400),
  "CDELIVERY" CHAR(1 CHAR),
  "CDELIVERY_DESCRIPTION" NVARCHAR2(400),
  "COWNER" CHAR(1 CHAR),
  "COWNER_DESCRIPTION" NVARCHAR2(400),
  "SDIVISION" VARCHAR2(2 CHAR),
  "SDIVISION_DESCRIPTION" NVARCHAR2(400),
  "SPLANTLINE" VARCHAR2(2 CHAR),
  "CMAKEBUY" CHAR(1 CHAR),
  "CMAKEBUY_DESCRIPTION" NVARCHAR2(400),
  "CSUBTYPE" CHAR(1 CHAR),
  "CSUBTYPE_DESCRIPTION" NVARCHAR2(400),
  "SCLASSIFICATION" VARCHAR2(4 CHAR),
  "SCLASSIFICATION_DESCRIPTION" NVARCHAR2(400),
  "SFATHER" VARCHAR2(25 CHAR),
  "SCOUNTRYOR" VARCHAR2(4 CHAR),
  "SCOUNTRYOR_DESCRIPTION" NVARCHAR2(400),
  "SNOTICE" VARCHAR2(10 CHAR),
  "SPMCLASSIFICATIONLEVEL1" VARCHAR2(2 CHAR),
  "SPMCLASSIFICATIONLEVEL1_DESCRIPTION" NVARCHAR2(400),
  "SPMCLASSIFICATIONLEVEL2" VARCHAR2(2 CHAR),
  "SPMCLASSIFICATIONLEVEL2_DESCRIPTION" NVARCHAR2(400),
  "SPMCLASSIFICATIONLEVEL3" VARCHAR2(50 CHAR),
  "SPMCLASSIFICATIONLEVEL3_DESCRIPTION" NVARCHAR2(400),
  "SPMCLASSIFICATIONLEVEL4" VARCHAR2(50 CHAR),
  "SPMCLASSIFICATIONLEVEL4_DESCRIPTION" NVARCHAR2(400),
  "SNOTELINE1" VARCHAR2(60 CHAR),
  "SNOTELINE2" VARCHAR2(60 CHAR),
  "SNOTELINE3" VARCHAR2(60 CHAR),
  "SNOTELINE4" VARCHAR2(60 CHAR),
  "SEXPCOUNTRYOFORIGIN" VARCHAR2(6 CHAR),
  "SRECMARKETS" VARCHAR2(500 CHAR),
  "SREPLACEDBY_ELS" VARCHAR2(25 CHAR),
  "SREPLACING_ELS" VARCHAR2(300 CHAR),
  "SCODIFICATION" VARCHAR2(50 CHAR),
  "BSUGGESTED" NUMBER(3,0),
  "BCOMPETITOR" CHAR(10 CHAR),
  "BSERIALNUMBER" NUMBER(3,0),
  "CP13REF" CHAR(1 CHAR),
  "CP13REF_DESCRIPTION" NVARCHAR2(400),
  "BSUGGACC" NUMBER(3,0),
  "SSUBLINE" CHAR(2 CHAR),
  "BFITTEDACC" NUMBER(3,0),
  "BGOODSLABEL" NUMBER(3,0),
  "BTECHNICIANFEE" CHAR(1 CHAR),
  "BGETBACK" NUMBER(3,0),
  "SGETBACKCLASS" VARCHAR2(6 CHAR),
  "SMORENOTICE" VARCHAR2(200 CHAR),
  "SMOREREPLACING" VARCHAR2(500 CHAR),
  "SPLANFATHER" VARCHAR2(25 CHAR),
  "DORIGINALDISTRIBSTART" DATE,
  "SCOO_TYPE" VARCHAR2(1 CHAR),
  "FCOO_PERCENTAGE" BINARY_DOUBLE,
  "BMANUALCOMPETITOR" NUMBER(3,0),
  "SBOI_UM" VARCHAR2(3 CHAR),
  "BSERVICEPART" NUMBER(3,0),
  "BBIPPART" NUMBER(3,0),
  "BLASTBUYORDER" NUMBER(3,0),
  "SGREENCODE" VARCHAR2(25 CHAR),
  "BAUTOMATICDISTRIBSTOPCALCULATION" NUMBER(3,0),
  "SBULKYITEM" VARCHAR2(1 CHAR),
  CONSTRAINT "PK_TB_PDB_LOGISTIC" PRIMARY KEY ("SINTCODE") USING INDEX ENABLE
);