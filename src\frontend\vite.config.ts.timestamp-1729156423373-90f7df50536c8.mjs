// vite.config.ts
import { defineConfig } from "file:///C:/Workspace/compli-bot/src/frontend/node_modules/vite/dist/node/index.js";
import react from "file:///C:/Workspace/compli-bot/src/frontend/node_modules/@vitejs/plugin-react/dist/index.mjs";
var vite_config_default = defineConfig({
  plugins: [react()],
  build: {
    outDir: "../backend/rbot/react_static",
    emptyOutDir: true,
    sourcemap: true,
    chunkSizeWarningLimit: 1024
  },
  server: {
    proxy: {
      "/ask": "http://127.0.0.1:5000",
      "/chat": "http://127.0.0.1:5000",
      "/user-profiles": "http://127.0.0.1:5000",
      "/search-settings": "http://127.0.0.1:5000",
      "/version": "http://127.0.0.1:5000",
      "/auth/login": "http://127.0.0.1:5000",
      "/auth/logout": "http://127.0.0.1:5000",
      "/export-data": "http://127.0.0.1:5000",
      "/bot-list": "http://127.0.0.1:5000",
      "/bot-change": "http://127.0.0.1:5000"
    }
  }
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
