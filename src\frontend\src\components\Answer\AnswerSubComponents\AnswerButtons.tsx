import React, {useState} from "react";
import J<PERSON><PERSON><PERSON> from "jszip";
import html2pdf from "html2pdf.js";
import { Spinner, SpinnerSize } from "@fluentui/react";
import { saveAs } from "file-saver";
import { Stack, IconButton } from "@fluentui/react";
import { FeedbackType, ChatResponse } from "../../../api";

interface Props {
    chatResponse: ChatResponse;
    onExcelClicked?: (dialogId: string) => void;
    selectedBot: string | number | null;
    conversationId: string;
    dialogId: string;
}

export const AnswerButtons = ({
    onExcelClicked,  
    chatResponse, 
    selectedBot, 
    conversationId, 
    dialogId, 
}: Props) => {
    const showExcelButton = onExcelClicked && 
                            selectedBot !== 'EPROEXCELLA_BOT' && 
                            chatResponse.answer?.agent == 'text_to_sql';
    
    const showCallCenterDownload = selectedBot === 'COMPLI_BOT';

    const [isDownloading, setIsDownloading] = useState(false)

    const handleDownloadZip = async () => {
        setIsDownloading(true);
        const formattedHTML = chatResponse.answer?.formatted_answer;
        if (!formattedHTML) {
            setIsDownloading(false);
            return;
        }

        const container = document.createElement("div");
        container.innerHTML = formattedHTML;

        const links = Array.from(container.querySelectorAll("a")) as HTMLAnchorElement[];
        if (links.length === 0) {
            setIsDownloading(false);
            return;
        }

        const zip = new JSZip();

        for (const link of links) {
            const url = link.href;
            const filename = link.textContent?.trim() || url.split('/').pop() || "file";

            try {
                const response = await fetch(url);
                const blob = await response.blob();
                zip.file(filename + ".zip", blob);
            } catch (err) {
                console.error(`Errore nel download del file ${filename}:`, err);
            }
        }

        // Genera e salva lo ZIP
        zip.generateAsync({ type: "blob" }).then((zipBlob) => {
            saveAs(zipBlob, "all_files.zip");
            setIsDownloading(false);
        });
    };
    
    const handleGeneratePdf = () => {
        const rawHTML = chatResponse.answer?.formatted_answer || "";

        // Crea un div virtuale per manipolare l'HTML
        const tempDiv = document.createElement("div");
        tempDiv.innerHTML = rawHTML;

        // Rimuove tutti i link, ma lascia il testo interno
        const links = tempDiv.querySelectorAll("a");
        links.forEach(link => {
            const span = document.createElement("span");
            span.textContent = link.textContent;
            link.replaceWith(span);
        });

        const cleanedHTML = tempDiv.innerHTML;

        // Costruzione PDF
        const wrapper = document.createElement("div");
        wrapper.innerHTML = `
            <div style="font-family: 'Arial', sans-serif; font-size: 12pt; color: #333; padding: 20px;">
                <h2 style="text-align: center; margin-bottom: 30px;">Trasmissione Documentale</h2>

                <p style="line-height: 1.6;">
                    Gentile Cliente,<br><br>
                    come da Sua richiesta, le inoltriamo in allegato i documenti relativi ai codici da Lei selezionati.
                    Nella tabella sottostante troverà l’elenco completo.
                </p>

                <div style="margin-top: 20px; margin-bottom: 30px;">
                    ${cleanedHTML}
                </div>

                <p style="line-height: 1.6;">
                    Restiamo a disposizione per qualsiasi ulteriore chiarimento o informazione.<br><br>
                    Cordiali saluti<br>
                </p>
            </div>
        `;

        html2pdf()
            .from(wrapper)
            .set({
                filename: 'documenti_richiesti.pdf',
                html2canvas: { scale: 2 },
                jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' }
            })
            .save();
    };
    
    return(
        <Stack.Item>
            <Stack horizontal horizontalAlign="space-between">
                {/* Management of the Excel Button */}
                {showExcelButton && (
                                <IconButton
                                    style={{ color: "black" }}
                                    iconProps={{ iconName: "ExcelDocument" }}
                                    title="Download all data in Excel format"
                                    ariaLabel="Download all data in Excel format"
                                    href={`/export-data/${conversationId}/${dialogId}`}
                                    onClick={() => onExcelClicked(dialogId)}
                                />
                )}

                {/* Procurement ZIP Button */}
                {showCallCenterDownload && (
                    <>
                    {isDownloading
                     ? (<Spinner label="Download in corso..." size={SpinnerSize.small} />)

                     : (<IconButton
                        style={{ color: "black" }}
                        iconProps={{ iconName: "Download" }}
                        title="Scarica tutti i file come ZIP"
                        ariaLabel="Scarica tutti i file come ZIP"
                        onClick={handleDownloadZip}
                    />
                )}
                    <IconButton
                            style={{ color: "black" }}
                            iconProps={{ iconName: "Mail" }}
                            title="Invia email (PDF)"
                            ariaLabel="Invia email (PDF)"
                            onClick={handleGeneratePdf}
                        />
                    </>                    
                )}
            </Stack>
        </Stack.Item>
        
    )
}