import logging
from datetime import date
from json import load
from re import sub
from typing import Dict, List, Tuple

from openai import AzureOpenAI

from config.config import Text2SQLConfig
from src.agents.text_to_sql_agent.database.schema import DatabaseSchema, Column, Table
from src.agents.text_to_sql_agent.sql_errors import SQLErrors
from src.backend.contracts.chat_data import AgentName, BotType
from src.common_tools.history.history import ConversationHistory
from utils.core import get_logger

type Inquiry = str
type Sql = str

logger = get_logger(__file__)


# TODO - Move history logic to session, allowing this to become a Singleton
class LLM:
    def __init__(self, skeleton_path: str, schema: DatabaseSchema, model: AzureOpenAI) -> None:
        """Init a LLM for SQL query generation with a skeleton for its prompt and a database schema to use for query generation.

        Args:
            skeleton_path (str): The path of the file containing the skeleton for the prompt.
            schema (DatabaseSchema): The schema of the database on which the query should be based.
        """
        # Load env variables
        self.cfg = Text2SQLConfig()
        logger.info("Environment variables loaded")

        # Init client
        self.client = model
        self.schema = schema
        logger.info(f"OpenAI client has been initialised")

        # Load skeleton
        with open(skeleton_path) as file:
            self.skeleton = load(file)
        logger.info(f"Prompt skeleton has been loaded")

    def __get_system_message(self) -> Dict[str, str]:
        """
        Returns the system message.
        """
        message = f"""/* Current date: {date.today().strftime('%Y-%m-%d')}. This is today's date. */\n"""
        message += self.__build_schema(self.schema)
        return {"role": "system", "content": message}

    def __build_example(self, inquiry: str, sql: str) -> str:
        """
        Builds an example based on the skeleton.
        """
        logger.debug("Formatting example for prompt...")
        example = (
            self.skeleton["examples"]["prefix"]
            + inquiry
            + self.skeleton["examples"]["suffix"]
            + sql
            + "\n\n"
        )
        logger.debug(f"Example formatted! ({len(example)} chars generated)")
        return example

    def __build_schema(self, schema: DatabaseSchema) -> str:
        """
        Builds a schema representation based on the skeleton.
        """
        logger.debug("Formatting database schema into prompt...")
        prompt_schema = self.skeleton["target_question"]["header"]

        for table in schema.tables():
            if table.description != "":
                prompt_schema += "/* Table description: " + table.description + "*/\n"
            if self.__column_descriptions_in_table(table):
                prompt_schema += self.__add_column_descriptions(table)
            prompt_schema += table.schema + "\n\n"

        logger.info(
            f"Database schema formatted! ({len(prompt_schema)} chars generated)"
        )
        return prompt_schema
    
    def __column_descriptions_in_table(self, table: Table) -> bool:
        for column in table.columns():
            if column.description != "":
                return True
        return False
    
    def __add_column_descriptions(self, table: Table) -> str:
        column_prompt = f"/*Column descriptions for table {table.name}: \n "

        for column in table.columns():
            if column.description != "":
                column_prompt += f"{column.name}: {column.description} \n"
        
        return column_prompt

    def clarify(self, inquiry: str) -> str:
        skel = self.skeleton["clarification"]

        prompt = (
            skel["header"]
            + "\n"
            + skel["prefix"]
            + "\n"
            + skel["suffix"]
            + "\n"
            + skel["footer"]
        )
        prompt = {"role": "system", "content": prompt}
        messages = [prompt, {"role": "user", "content": inquiry}]

        response = self.client.chat.completions.create(
            model=self.cfg.openai_llm_deployed,
            messages=messages,
            temperature=0.3,
            top_p=0.2,
        )

        response = response.choices[0].message.content

        return response

    def get_prompt(self, examples: List[Tuple[Inquiry, Sql]], inquiry: str, sql_errors: SQLErrors, followup=False, bot_name = None) -> str:
        """Builds a prompt based on the skeleton, the examples, and the inquiry, to instruct the LLM to generate SQL queries.

        Args:
            examples (List[Tuple[Inquiry, Sql]]): The examples (inquiry, sql) to use as a reference in the prompt.
            inquiry (str): The question the LLM has to answer.

        Returns:
            str: A prompt for SQL query generation.
        """
        logger.debug(f"Building prompt...")
        # Init prompt
        prompt = self.skeleton["examples"]["header"]

        for nl, sql in examples:
            prompt += self.__build_example(nl, sql)
            
        prompt += self.skeleton["examples"]["footer"]
            
        if followup:
            formatted_examples = prompt
            prompt = ""
            skeleton = self.skeleton["followup_question"]
        else:
            skeleton = self.skeleton["target_question"]

        # prompt += (
        #     skeleton["prefix"]
        #     + inquiry
        #     + skeleton["suffix"]
        #     + skeleton["footer"]
        # )
        #Check if a previous prompt was generate and the resultant query gave the error 'invalid identifier'
        prompt = self.prompt_error_management(prompt, skeleton, inquiry, sql_errors.invalid_identifier_prompt)
        
        if followup:
            prompt += f"\n{formatted_examples}"

        if bot_name in self.skeleton["bot_additional_prompt"]:
            prompt += self.skeleton["bot_additional_prompt"][bot_name]

        logger.info(f"Prompt is ready:\n{prompt}")
        return prompt

    def generate_query(self, prompt: str, history: List[Dict[str, str]] = []) -> str:
        """Starts a conversation with the LLM by submitting a prompt instructing the LLM to generate an SQL statement.

        Args:
            prompt (str): The prompt to submit.

        Returns:
            str: The SQL answer to the inquiry contained in `prompt`.
        """
        system_msg = self.__get_system_message()
        user_msg = {"role": "user", "content": prompt}

        logger.debug("Invoking LLM to answer first question...")
        response = self.client.chat.completions.create(
            model=self.cfg.openai_llm_deployed,
            messages=[system_msg, user_msg],
            temperature=0.2,
            top_p=0.1,
        )
        logger.info(f"Client has reponded!")

        response = response.choices[0].message.content

        return sub(r"[\s]+", " ", response)

    def explain_query(self, query: str) -> str:
        """Generate an explanation for the provided query.

        Args:
            query (str): The query to explain.

        Returns:
            str: The explanation in natural language of the query.
        """
        guide = self.skeleton["explanation"]
        message = guide["header"] + guide["prefix"] + guide["suffix"] + guide["footer"]
        message = [
            {"role": "assistant", "content": query},
            {"role": "user", "content": message},
        ]

        logger.debug(f"Invoking LLM to explain the following SQL query: {query}")
        response = self.client.chat.completions.create(
            model=self.cfg.openai_llm_deployed,
            messages=message,
            temperature=0.3,
            top_p=0.2,
        )
        logger.info("Client has responded!")

        response = response.choices[0].message.content

        return response

    def keep_chatting(
        self, message: str, history: ConversationHistory
    ) -> str:
        """Continues an existing conversation with the LLM.

        Args:
            message (str): The message submitted by the user
            history (ConversationHistory): The history of messages exchanged by the LLM and a user, if None provided defaults to local history. Defaults to None.

        Returns:
            str: The text generated by the LLM

        Side effects:
            If `history` is provided, it will be updated by adding `message` and the related response returned by this function. Providing a history does not update the local history.
        """
        conversation = [self.__get_system_message()]
        conversation.extend(history.history_messages(AgentName.TEXT_TO_SQL.name).copy())     
        logger.info(f"Conversation history is {len(conversation)} messages long.")

        conversation.append({"role": "user", "content": message})

        logger.debug(f"Invoking LLM with following message: {message.replace("\n", " ")}")
        response = self.client.chat.completions.create(
            model=self.cfg.openai_llm_deployed, messages=conversation, temperature=0.2, top_p=0.1
        )
        logger.info("Client has reponded!")

        response = response.choices[0].message.content or ""

        return sub(r"[\s]+", " ", response)

    def prompt_error_management(self, prompt, skeleton, inquiry, error_management_promt) -> str:
        if not error_management_promt:
            prompt += (
            skeleton["prefix"]
            + inquiry
            + skeleton["suffix"]
            + skeleton["footer"]
            )
        else:
            prompt += (
            skeleton["prefix"]
            + inquiry
            + error_management_promt
            + skeleton["suffix"]
            + skeleton["footer"]
            )
        
        return prompt
            