CREATE TABLE DWH_PUBLIC.BOM_PRMS (
	COMPANY_CODE NVARCHAR2(5),
	COMPANY_DESCRIPTION NVARCHAR2(50),
	PLANT_CODE NVARCHAR2(12),
	PLANT_DESCRIPTION NVARCHAR2(50),
	CHILD_ITEM_CODE NVARCHAR2(25),
	PARENT_ITEM_CODE NVARCHAR2(25),
	QUANTITY NUMBER(25, 0),
	ITEM_UNIT_OF_MEASURE NVARCHAR2(2),
	ITEM_UNIT_OF_MEASURE_DESCRIPTION NVARCHAR2(500),
	START_PRODUCTION_DATE DATE,
	STOP_PRODUCTION_DATE DATE,
	<PERSON><PERSON><PERSON><PERSON> NVARCHAR2(2),
	POSITION NVARCHAR2(8),
	SYSTEM_SOURCE NVARCHAR2(10),
	PARENT_ITEM_DESCR NVARCHAR2(500),
	CHILD_ITEM_DESCR NVARCHAR2(500),
	<PERSON><PERSON><PERSON><PERSON> (COMPANY_CODE, P<PERSON>NT_CODE, CH<PERSON>D_ITEM_CODE, PARENT_ITEM_CODE, MODULE, POSITION, START_PRODUCTION_D<PERSON>E, STOP_PRODUCTION_DATE)
);