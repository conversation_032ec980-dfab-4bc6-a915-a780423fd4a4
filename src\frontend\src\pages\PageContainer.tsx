import { useState, useEffect } from "react";

import { SearchSettings, UserProfile, getAllUsers, getSearchSettings, getBotList, defaultSearchSettings } from "../api";
import Chat from "./chat/Chat";
import { LoadingPanel } from "../components/LoadingPanel";
import { ErrorPanel } from "../components/ErrorPanel";

const PageContainer = () => {
    const [isDataLoading, setIsDataLoading] = useState<boolean>(true);
    const [dataError, setDataError] = useState<unknown>();
    const [users, setUsers] = useState<UserProfile[]>([]);
    const [searchSettings, setSearchSettings] = useState<SearchSettings>(defaultSearchSettings);
    const [botList, setBotList] = useState<{key: string, text: string}[]>([]);


    const fetchData = async () => {
        try {
            const users = await getAllUsers();
            const searchSettings = await getSearchSettings();
            const botList = await getBotList();
            setUsers(users);
            setSearchSettings(searchSettings);
            setBotList(botList || [])
            setDataError(undefined);
        } catch (e) {
            setDataError(e);
        } finally {
            setIsDataLoading(false);
        }
    };

    useEffect(() => {
        fetchData();
    }, []);

    return (
        <div>
            {isDataLoading ? (
                <LoadingPanel />
            ) : dataError ? (
                <ErrorPanel error={dataError.toString()} onRetry={() => fetchData()} />
            ) : (
                <Chat users={users} searchSettings={searchSettings} botList={botList} />
            )}
        </div>
    );
};

export default PageContainer;
