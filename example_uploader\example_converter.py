import argparse
from io import String<PERSON>
from json import dump, load
from os.path import dirname, join
from re import MULTILINE, sub
from typing import Dict, List, Union

import pandas as pd


def excel_to_dict(excel_path: str, excel_sheet: str) -> Dict[str, Dict[str, str]]:
    """
    Converts an Excel sheet containing (inquiry; sql) examples to a dictionary
    """
    # Load whole Excel file
    with pd.ExcelFile(excel_path) as file:
        # Load sheet of interest
        excel = pd.read_excel(file, excel_sheet)
        excel.rename(
            columns={"QUESTION FOR AI": "inquiry", "QUERY": "sql"}, inplace=True
        )

    # Select only rows with non-na inquiry and sql fields
    excel = excel[excel["sql"].notna() & excel["inquiry"].notna()]
    excel = excel[["inquiry", "sql", "Bots"]]
    # Add custom index
    excel.insert(0, "id", [excel_sheet.lower() + f"_{i}_0" for i in range(len(excel))])
    excel.set_index("id", inplace=True)
    # Convert df to dict
    excel = load(StringIO(excel.to_json(orient="index")))

    return excel


def ready_for_synonyms(
    indexed: Dict[str, Dict[str, str]]
) -> Dict[str, List[Dict[str, Union[int, str]]]]:
    """
    Changes the structure of the dictionary to facilitate example population
    """
    ready = {}

    for key, example in indexed.items():
        components = key.split("_")

        new_key = components[0] + "_" + str(components[1])
        new_example = [
            {
                "id": int(components[2]),
                "inquiry": example["inquiry"],
                "sql": sub("\s+", " ", example["sql"], flags=MULTILINE),
                "bot": example["Bots"],
            }
        ]

        ready[new_key] = new_example

    return ready


def update_examples(
    old_examples_path: str, new_examples: Dict[str, List[Dict[str, Union[int, str]]]]
) -> None:
    """
    Update old example JSON with new examples from dict. If a key is present in both the JSON and the dict, this function will overwrite the corresponding value in the JSON
    with the value from the dict.
    """
    with open(old_examples_path, "r", encoding="utf-8") as file:
        old_examples = dict(load(file))

        # Only insert, do not update
        for key, example in new_examples.items():
            if key not in old_examples:
                old_examples[key] = example

    with open(old_examples_path, "w", encoding="utf-8") as file:
        dump(old_examples, file, indent=4, ensure_ascii=False)


def get_args() -> argparse.Namespace:
    parser = argparse.ArgumentParser(
        description="Updates a JSON containing (inquiry; sql) examples with new examples from an Excel sheet"
    )
    parser.add_argument(
        "--excel_file",
        "-E",
        dest="excel_path",
        type=str,
        help="The path of the Excel file containing the new examples",
        default=join(dirname(__file__), "./TXT2SQL_questions.xlsm"),
    )
    parser.add_argument(
        "excel_sheet",
        type=str,
        help="The name of the sheet in the Excel file where the new examples are located",
    )
    parser.add_argument(
        "--JSON_examples",
        "-J",
        dest="json_path",
        type=str,
        help="The JSON file path containing all the examples already prepared",
        default=join(dirname(__file__), "./synonyms.json"),
    )

    return parser.parse_args()


if __name__ == "__main__":
    args = get_args()

    examples = excel_to_dict(args.excel_path, args.excel_sheet)
    examples = ready_for_synonyms(examples)
    update_examples(args.json_path, examples)
