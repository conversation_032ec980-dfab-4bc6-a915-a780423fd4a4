"""fix after lost migration

Revision ID: 62b5c1523213
Revises: 8b3943bdcc7e
Create Date: 2025-04-17 00:34:39.619787

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '62b5c1523213'
down_revision = '8b3943bdcc7e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('app_config',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('key_name', sa.String(length=255), nullable=True),
    sa.Column('value', sa.String(length=255), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('app_config')
    # ### end Alembic commands ###
