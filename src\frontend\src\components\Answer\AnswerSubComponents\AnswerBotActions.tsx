import React from "react";
import { Stack, IconButton, PrimaryButton } from "@fluentui/react";
import { FeedbackType, ChatResponse, Agents } from "../../../api";

interface Props {
    chatResponse: ChatResponse;
    onExcelClicked?: (dialogId: string) => void;
    selectedBot: string | number | null;
    preview: boolean;
    onChangeAgent?: () => void;
    handleDownloadClick?: () => void;
}

export const AnswerBotActions = ({ 
    chatResponse, 
    selectedBot, 
    preview, 
    onChangeAgent, 
    handleDownloadClick
}: Props) => {
    return(
        <Stack.Item>
                <div>
                    <span>
                        {selectedBot === 'CALL_CENTER_BOT' && !chatResponse.error ? "Isn't that what you're looking for?" : ""}
                        {preview && selectedBot === 'EPROEXCELLA_BOT' && !chatResponse.error ? "Do you want to translate and download the whole file?" : ""}
                    </span>

                    {onChangeAgent && selectedBot === 'CALL_CENTER_BOT'&& !chatResponse.error && (
                        <PrimaryButton
                            onClick={onChangeAgent}
                            style={{ marginLeft: "10px" }}
                            text={chatResponse.answer?.agent === Agents.ragDocument ?
                                "Go to documentation" :
                                chatResponse.answer?.agent === Agents.rag ?
                                    "Go to Database" :
                                    "Go to documentation"
                            }
                        />
                    )}

                    {preview && selectedBot === 'EPROEXCELLA_BOT' && (
                        <PrimaryButton
                            style={{ marginLeft: "10px" }}
                            text="Download"
                            title="Prepare all data in Excel format"
                            ariaLabel="Prepare all data in Excel format"
                            onClick={handleDownloadClick}
                        />
                    )}
                </div>
            </Stack.Item>
    )
}