import React from "react";
import { <PERSON><PERSON>, IconButton, PrimaryButton, Label } from "@fluentui/react";
import { FeedbackType, ChatResponse } from "../../../api";
import { ErrorCircle20Regular } from "@fluentui/react-icons";
import styles from "../Answer.module.css";


interface Props {
    retryable: boolean;
    onRetryClicked?: () => void;
}

export const RetryButton = ({
    retryable,
    onRetryClicked 
}: Props) => {
    return (
        <>
            {retryable && onRetryClicked && (
                <div className={styles.retryContainer}>
                    <ErrorCircle20Regular />
                    <Label className={styles.retryText}>
                        Looks like this search ran into an issue. Would you like me to try again
                        with an expanded scope?
                    </Label>
                    <PrimaryButton
                        className={styles.retryButton}
                        onClick={onRetryClicked}
                        text="Retry"
                    />
                </div>
            )}
        </>
    )
}