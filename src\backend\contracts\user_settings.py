import inspect
from typing import Optional


class SearchSettings:
    def __init__(self,
                 show_explanation: Optional[bool] = False,
                 show_sql: Optional[bool] = False,
                 top: Optional[int] = 10,
                 temperature: Optional[float] = 0.7,
                 suggest_followup_questions: Optional[bool] = False,
                 **kwargs):
        # Retrieve only the expected parameters
        expected = inspect.signature(self.__class__).parameters
        for key in expected:
            if key not in ('self', 'kwargs'):
                setattr(self, key, locals()[key])


    def to_json(self):
        return {
            "show_explanation": self.show_explanation,
            "show_sql": self.show_sql,
            "top": self.top,
            "temperature": self.temperature,
            "suggest_followup_questions": self.suggest_followup_questions
        }

    def __json__(self):
        return self.to_json()

class AllUserSettings:
    def __init__(self, search_settings: SearchSettings):
        self.search_settings = search_settings

    def to_json(self):
        return {
            "search_settings": self.search_settings.to_json(),
        }

    def __json__(self):
        return self.to_json()