import { useState, useEffect } from "react";
import { Outlet, Link } from "react-router-dom";
import epr_logo from "../../assets/logo_white.svg";
import styles from "./Layout.module.css";
import { VersionDisplay, getVersionDisplay, getChangelogPreview } from "../../api";
import { Button, Spinner, Dialog, DialogSurface, DialogBody, DialogContent, DialogTitle, DialogActions } from "@fluentui/react-components";


const Layout = () => {
    const [versionDisplay, setVersionDisplay] = useState<VersionDisplay>();
    const [showChangelog, setShowChangelog] = useState(false);
    const [changelog, setChangelog] = useState<string>("");
    const [loadingChangelog, setLoadingChangelog] = useState(false); // nuovo stato

    const fetchData = async () => {
        try {
            const fetchedVersionDisplay = await getVersionDisplay();
            setVersionDisplay(fetchedVersionDisplay);
        } catch (e) {
            console.log(`Error getting version from /version API: ${e}`);
        }
    };

    const handleVersionClick = async () => {
        setShowChangelog(true); // mostra subito il popup
        setLoadingChangelog(true); // attiva il loader
        try {
            const preview = await getChangelogPreview();
            setChangelog(preview);
        } catch (e) {
            setChangelog("Errore nel caricamento del changelog.");
        }
        setLoadingChangelog(false); // disattiva il loader
    };

    useEffect(() => {
        fetchData();
    }, []);
    return (
        <div className={styles.layout}>
            <header className={styles.header} role={"banner"}>
                <div className={styles.headerContainer}>
                    <div className={styles.headerTitleContainer}>
                        <Link reloadDocument to="/" className={styles.headerTitleContainer}>
                            <img src={epr_logo} alt="Electrolux Professional logo" aria-label="CompliBot" height="40px" className={styles.eprLogo} />
                        </Link>
                        {versionDisplay?.running_env && (
                            <span className={styles.runningEnv} title="Running Environment">
                                {versionDisplay.running_env}
                            </span>
                        )}
                    </div>
                    <nav>
                        <ul className={styles.headerNavList}>
                            <li className={styles.headerNavLeftMargin}>
                                <span
                                    style={{ cursor: "pointer", textDecoration: "underline" }}
                                    onClick={handleVersionClick}
                                    title="Changelog"
                                >
                                    {versionDisplay?.git}
                                </span>
                                <Dialog open={showChangelog} onOpenChange={(_, data) => setShowChangelog(data.open)}>
                                    <DialogSurface>
                                        <DialogBody>
                                        <DialogTitle>Changelog</DialogTitle>
                                        <DialogContent>
                                            {loadingChangelog ? (
                                                <Spinner labelPosition="below" label="Loading Changelog..." />
                                            ) : (
                                                <pre style={{ whiteSpace: "pre-wrap", margin: 0, padding: 16 }}>{changelog}</pre>
                                            )}
                                        </DialogContent>
                                        <DialogActions>
                                            <Button appearance="primary" onClick={() => setShowChangelog(false)}>
                                                    Close
                                            </Button>
                                        </DialogActions>
                                        </DialogBody>
                                    </DialogSurface>
                                </Dialog>
                            </li>
                        </ul>
                    </nav>
                </div>
            </header>
            <Outlet />
        </div>
    );
};

export default Layout;
