{% extends 'base.html' %}
{% block content %}
<div class="container mt-4">
    <h2>{% if edit %}Modifica Utente{% else %}Aggiungi Utente{% endif %}</h2>
    <form method="post">
        <div class="mb-3">
            <label for="email" class="form-label">Email</label>
            <input type="email" class="form-control" id="email" name="email" required value="{{ user.email if edit else '' }}">
        </div>
        <div class="mb-3">
            <label for="name" class="form-label">Nome</label>
            <input type="text" class="form-control" id="name" name="name" required value="{{ user.name if edit else '' }}">
        </div>
        <div class="mb-3">
            <label for="surname" class="form-label">Cognome</label>
            <input type="text" class="form-control" id="surname" name="surname" required value="{{ user.surname if edit else '' }}">
        </div>
        <div class="mb-3">
            <label for="role" class="form-label">Ruolo</label>
            <select class="form-select" id="role" name="role" required>
                {% for role in roles %}
                    <option value="{{ role.id }}"
                        {% if edit and user.roles %}
                            {% for r in user.roles %}
                                {% if role.id == r.id %}selected{% endif %}
                            {% endfor %}
                        {% endif %}
                    >{{ role.name }}
                    </option>
                {% endfor %}
            </select>
        </div>
        <fieldset class="mb-3 border rounded p-3">
            <legend class="float-none w-auto px-2">User Settings</legend>
            {% set settings = user.user_settings if edit else user_settings_template %}
            <div class="mb-3">
                <label for="allowed_bots" class="form-label">Bot abilitati</label>
                <select class="form-select" id="allowed_bots" name="allowed_bots" multiple required>
                    {% for bot in bot_types %}
                        <option value="{{ bot }}" {% if bot in settings.allowed_bots %}selected{% endif %}>{{ bot }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="mb-3">
                <label class="form-label">Impostazioni ricerca</label>
                <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" id="suggest_followup_questions" name="suggest_followup_questions" value="true" {% if settings.search_settings.suggest_followup_questions %}checked{% endif %}>
                    <label class="form-check-label" for="suggest_followup_questions">Suggerisci domande di follow-up</label>
                </div>
                <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" id="show_explanation" name="show_explanation" value="true" {% if settings.search_settings.show_explanation %}checked{% endif %}>
                    <label class="form-check-label" for="show_explanation">Mostra spiegazione</label>
                </div>
                <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" id="show_sql" name="show_sql" value="true" {% if settings.search_settings.show_sql %}checked{% endif %}>
                    <label class="form-check-label" for="show_sql">Mostra SQL</label>
                </div>
                <div class="mt-2">
                    <label for="top" class="form-label">Top</label>
                    <input type="number" class="form-control" id="top" name="top" min="1" value="{{ settings.search_settings.top }}">
                </div>
                <div class="mt-2">
                    <label for="sql_answer_type" class="form-label">SQL Answer Type</label>
                    <select class="form-select" id="sql_answer_type" name="sql_answer_type">
                        {% for t in sql_answer_types %}
                            <option value="{{ t }}" {% if settings.search_settings.sql_answer_type == t %}selected{% endif %}>{{ t }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="mt-2">
                    <label for="answer_type" class="form-label">Answer Type</label>
                    <select class="form-select" id="answer_type" name="answer_type">
                        {% for t in sql_answer_types %}
                            <option value="{{ t }}" {% if settings.search_settings.answer_type == t %}selected{% endif %}>{{ t }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
        </fieldset>
        <button type="submit" class="btn btn-success">Salva</button>
        <a href="{{ url_for('rbot_admin.rbot_admin_users.users_list') }}" class="btn btn-secondary">Annulla</a>
    </form>
</div>
{% endblock %}
