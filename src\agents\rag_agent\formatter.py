import re
import markdown

def format_rag_document_to_markdown(text: str) -> str:
    """Format rag_document answers to markdown."""
    lines = text.strip().split('\n')
    intro = lines[0]
    items = lines[1:]
    formatted = f"{intro}\n\n" + "\n".join([f"- {line.strip('- ').strip()}" for line in items])
    return formatted

def format_rag_document_to_html(text: str) -> str:
    """Format rag_document strings to HTML."""
    md = format_rag_document_to_markdown(text)
    return markdown.markdown(md)

def format_rag_to_markdown(text: str) -> str:
    """Format rag answers to markdown."""
    # Already Markdown, just return as-is
    return text.strip()

def format_rag_to_html(text: str) -> str:
    """Format rag answers  to HTML."""
    md = format_rag_to_markdown(text)
    return markdown.markdown(md)