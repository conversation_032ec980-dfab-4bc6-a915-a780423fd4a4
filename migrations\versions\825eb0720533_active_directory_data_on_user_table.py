"""active directory data on user table

Revision ID: 825eb0720533
Revises: 0b251b1991be
Create Date: 2024-04-11 17:21:25.553831

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '825eb0720533'
down_revision = '0b251b1991be'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('user', schema=None) as batch_op:
        batch_op.add_column(sa.Column('ad_data', sa.JSON(), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('user', schema=None) as batch_op:
        batch_op.drop_column('ad_data')

    # ### end Alembic commands ###
