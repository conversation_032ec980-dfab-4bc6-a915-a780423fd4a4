"""question history columns

Revision ID: 1a1fff0955bc
Revises: de61c78c79db
Create Date: 2024-05-21 22:24:27.272419

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '1a1fff0955bc'
down_revision = 'de61c78c79db'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('question_history',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('question', sa.JSON(), nullable=True),
    sa.Column('answer', sa.JSO<PERSON>(), nullable=True),
    sa.Column('extra_details', sa.JSON(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('question_history')
    # ### end Alembic commands ###
