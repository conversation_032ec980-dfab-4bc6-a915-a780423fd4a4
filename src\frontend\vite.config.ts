import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";

// https://vitejs.dev/config/
export default defineConfig({
    plugins: [react()],
    build: {
        outDir: "../backend/rbot/react_static",
        emptyOutDir: true,
        sourcemap: true,
        chunkSizeWarningLimit: 1024
    },
    server: {
        proxy: {
            "/ask": "http://127.0.0.1:5000",
            "/chat": "http://127.0.0.1:5000",
            "/user-profiles": "http://127.0.0.1:5000",
            "/search-settings": "http://127.0.0.1:5000",
            "/version": "http://127.0.0.1:5000",
            "/changelog-preview": "http://127.0.0.1:5000",
            "/auth/login": "http://127.0.0.1:5000",
            "/auth/logout": "http://127.0.0.1:5000",
            "/export-data": "http://127.0.0.1:5000",
            "/bot-list": "http://127.0.0.1:5000",
            "/bot-change": "http://127.0.0.1:5000",
            "/upload": "http://127.0.0.1:5000",
        }
    }
});
