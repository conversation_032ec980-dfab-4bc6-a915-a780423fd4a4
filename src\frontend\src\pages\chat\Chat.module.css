.container {
    flex: 1;
    display: flex;
    flex-direction: column;
    margin-top: 7px;
}

.topRow {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    margin-bottom: 1px;
}

.dropdownContainer {
    flex: 0 0 auto;
    display: flex;
    align-items: center;
}

.dropdownLabel {
    margin-right: 10px;
    white-space: nowrap;
}

.chatRoot {
    flex: 1;
    display: flex;
}

.chatContainer {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
}

.chatEmptyState {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    max-height: 1024px;
    padding-top: 40px;
}

.chatEmptyStateTitle {
    font-size: 4rem;
    font-weight: 600;
    margin-top: 0;
    margin-bottom: 20px;
}

.chatEmptyStateSubtitle {
    font-weight: 600;
    margin-bottom: 10px;
}

@media only screen and (max-height: 780px) {
    .chatEmptyState {
        padding-top: 0;
    }

    .chatEmptyStateTitle {
        font-size: 3rem;
    }
}

.chatMessageStream {
    flex-grow: 1;
    max-height: 70vh;
    max-width: 1028px;
    width: 100%;
    overflow-y: auto;
    padding-left: 24px;
    padding-right: 24px;
    display: flex;
    flex-direction: column;
}

.chatMessageGpt {
    margin-bottom: 20px;
    max-width: 80%;
    display: flex;
    min-width: 500px;
}

.chatMessageGptMinWidth {
    max-width: 500px;
    margin-bottom: 20px;
}

.chatInput {
    position: sticky;
    bottom: 0;
    flex: 0 0 100px;
    padding-top: 12px;
    padding-bottom: 24px;
    padding-left: 24px;
    padding-right: 24px;
    width: 100%;
    max-width: 1028px;
    background: #f2f2f2;
}

.chatAnalysisPanel {
    flex: 1;
    overflow-y: auto;
    max-height: 89vh;
    margin-left: 20px;
    margin-right: 20px;
}

.chatSettingsSeparator {
    margin-top: 15px;
}

.loadingLogo {
    font-size: 28px;
}

.commandsContainer {
    display: flex;
    align-self: flex-end;
}

.botContainer {
    display: flex;
    align-self: flex-start;
}

.commandButton {
    margin-right: 20px;
    margin-bottom: 20px;
}
