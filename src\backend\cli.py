import logging
from os import get_terminal_size
from time import time

import pandas as pd
from art import tprint

from config.config import CommonConfig
from src.complibot import CompliBot, IncapableException


def build_interface(bot: CompliBot):
    inquiry = input("Enter your inquiry: ")

    try:
        start = time()

        cfg = CommonConfig()
        answer = bot.ask(inquiry)

        end = time()

        data = pd.DataFrame(data=answer.data[1:], columns=answer.data[0]).to_string(
            index=False
        )
        print(
            f"\nResults fetched with {answer.confidence*100:.0f}% confidence:\n{data}\n"
        )
        print(f"Explanation:\n{answer.explanation}")
        if answer.sql is not None:
            print(f"\nQuery executed:\n{answer.sql}")

        print(f"\nElapsed time: {end - start:.2f}s\n")
    except IncapableException as e:
        print(
            "I'm unable to answer your question. Please try to be as specific as possible.\n"
        )
        if cfg.default_run_mode == "TEST":
            print("Here are the failed examples with their associated database errors:")

            for wrong_sql, error_message in e.failed_candidates:
                print(
                    f"Failed SQL query: {wrong_sql}\nDatabase error message: {error_message}"
                )

    print("-" * get_terminal_size().columns, end="\n\n")


def run_interface():
    # Setting up connections for inference and validation
    complibot = CompliBot()

    tprint("CompliBot")

    try:
        while True:
            build_interface(complibot)
    except KeyboardInterrupt:
        print(
            "\nKill command received, shutting down connections...", end=" ", flush=True
        )
        complibot.shutdown()
        print("DONE")


if __name__ == "__main__":
    logging.basicConfig(
        filename="./final_call.log",
        level=logging.INFO,
        filemode="w",
        format="[%(levelname)s] %(module)s.%(funcName)s: %(message)s",
    )

    run_interface()
