import React from "react";
import styles from  "../Answer.module.css"
import Zoom from 'react-medium-image-zoom';

interface Props {
 images: string[] | undefined;
    
}

export const ImageGallery = ({ images }: Props) => {
    
    if (!images || images.length === 0) {
        return null;
    }
   
    return (
        <div className={styles.imageClass}>
            {images.map((url:string) => (
                <Zoom>
                <img
                    src={url}
                    className={styles.imageFitContain}
                    loading="lazy" // Enable lazy loading
                />
                </Zoom>
            ))}
        </div>
    );
};