import logging
from json import load
from math import ceil
from os.path import dirname, join
from statistics import mean
from typing import Dict, List, Tuple, Union

import pandas as pd
from tqdm import tqdm

from src.complibot import CompliBot, IncapableException, equals
from src.database.connection import DatabaseConnection

type Path = str
RUNS_FOLDER = join(dirname(__file__), "./bot_test_runs/")

def save_as_df(name:str, list_of_tuples: List[Tuple]) -> Path:
    dest = join(RUNS_FOLDER, name+".parquet")
    pd.DataFrame(data=list_of_tuples[1:], columns=list_of_tuples[0]).to_parquet(dest)
    return dest

def load_parquet(path: Path) -> pd.DataFrame:
    return pd.read_parquet(path)

def compute_accuracy(test_results: Dict[str, Dict[str, Union[str, List[Tuple], float]]]) -> float:
    count = 0
    
    for result in test_results.values():
        test = result["prediction"]
        test = load_parquet(test) if type(test) == str else test
        
        gold = result["gold"]
        gold = load_parquet(result["gold"]) if type(gold) == str else gold
        
        if equals(test, gold):
            count += 1
            result["passed"] = True
            
    return count / len(test_results)

def run_tests(cases: Dict[str, Dict[str, str]]) -> None:
    """Run the CompliBot test. Will only work if environment variable `MODE` is set to "TEST".

    Args:
        cases (Dict[str, Dict[str, str]]): The test set
    """
    bot = CompliBot()
    database = DatabaseConnection()
    results = {}
    for id, example in tqdm(cases.items(), desc="Preparing test set..."):
        gold = database.validate(example["sql"])
        
        results[id] = {
                "case": example["inquiry"],
                "gold_sql": example["sql"],
                "predicted_sql": "",
                "gold": save_as_df(id+"_gold", gold[1]) if gold is not None else pd.DataFrame(),
                "prediction": [],
                "passed": False
        }
    confidences = []
    
    for case_name, case_data in (pbar := tqdm(cases.items())):
        pbar.set_description(f"Testing {case_name}...")
        
        try:
            sql, data, confidence = bot.ask(case_data["inquiry"])
            
            results[case_name]["predicted_sql"] = sql
            results[case_name]["prediction"] = save_as_df(case_name+"_prediction", data)
            confidences.append(confidence)
        except IncapableException:
            results[case_name]["predicted_sql"] = "INCAPABLE"
            results[case_name]["prediction"] = pd.DataFrame()
            confidences.append(1)
            
        
    acc = compute_accuracy(results)
        
    message = "RESULTS:\n\n"
    message += f"Execution accuracy is {acc*100}%, avarage confidence is {mean(confidences)} - {ceil(len(cases) - acc*len(cases))} cases have failed\n\n"
    
    for name, result in results.items():
        message += "-"*16 + "\n\n"
        if not result["passed"]:
            message += f"Case {name} has failed:\n"
            message += f"Inquiry: {result["case"]}\n"
            message += f"Expected query:\n{result["gold_sql"]}\n"
            message += f"Given query:\n{result["predicted_sql"]}\n\n"
            expected_data = load_parquet(result["gold"]).describe().to_string() if type(result["gold"]) == str else result["gold"].to_string()
            message += f"Expected data:\n{expected_data}\n"
            given_data = load_parquet(result["prediction"]).describe().to_string() if type(result["prediction"]) == str else result["prediction"].to_string()
            message += f"Given data:\n{given_data}\n\n"
        else:
            message += f"Case {name} passed!\n\n"
            
    with open(join(dirname(__file__), "./test_results.report"), "w", encoding="utf-8") as file:
        file.write(message)
        
    bot.shutdown()
        
if __name__ == "__main__":
    logging.basicConfig(filename=join(dirname(__file__), "./bot_test.log"), filemode="w", level=logging.DEBUG, format="[%(levelname)s] %(module)s.%(funcName)s: %(message)s")
    with open(join(dirname(__file__), "./test_set.json")) as file:
        cases = load(file)
    
    run_tests(cases)
