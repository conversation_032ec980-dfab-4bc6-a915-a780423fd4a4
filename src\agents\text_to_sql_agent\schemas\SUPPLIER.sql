CREATE TABLE DWH_PUBLIC.SUPPLIER (
	SUPPLIER VARCHAR2(10 BYTE),
	SUPPLIER_DESCRIPTION VARCHAR2(240 BYTE),
	COMPANY_CODE VARCHAR2(3 BYTE),
	COMPANY_DESCRIPTION VARCHAR2(100 BYTE),
	OLD_SUPPLIER VARCHAR2(6 BYTE) DEFAULT ' ',
	CURRENCY VARCHAR2(3 BYTE) DEFAULT ' ',
	CURRENCY_DESCRIPTION VARCHAR2(100 BYTE),
	PURCHASING_STATUS CHAR(1 BYTE) DEFAULT ' ',
	ADMIN_STATUS CHAR(1 BYTE) DEFAULT ' ',
	PAYMENT_CODE VARCHAR2(10 BYTE) DEFAULT ' ',
	PAYMENT_DESCRIPTION VARCHAR2(360 BYTE) DEFAULT ' ',
	SUPPLIER_CATEGORY VARCHAR2(3 BYTE) DEFAULT ' ',
	SUPPLIER_CATEGORY_DESCRIPTION VARCHAR2(400 BYTE) DEFAULT ' ',
	LANGUAGE VARCHAR2(2 BYTE) DEFAULT ' ',
	LANGUAGE_DESCRIPTION VARCHAR2(100 BYTE) DEFAULT ' ',
	EDI_STATUS CHAR(1 BYTE) DEFAULT ' ',
	TOP_IDCO VARCHAR2(2 BYTE) DEFAULT ' ',
	TOP_IDCO_DESCRIPTION VARCHAR2(400 BYTE) DEFAULT ' ',
	SUPPLY_TYPE CHAR(1 BYTE) DEFAULT ' ',
	SUPPLIER_STATUS CHAR(1 BYTE) DEFAULT '',
	PRIMARY KEY (SUPPLIER, COMPANY_CODE)
);