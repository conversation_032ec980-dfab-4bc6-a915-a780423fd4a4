# CHANGELOG

## v1.0.3 (2025-07-10)
### Bug fixes
* fix: show sql button fixed
### Refactoring
* refactor: removed unused old auth code
feat: new users admin gui
fix: loglevel from config file

## v1.0.2 (2025-07-04)
### Bug fixes
* fix: Now the user knows the environment
- now the bot uses the final index for it-documents-pride
- little change on the setup script to set only the environment vars
without altering the venv
### Documentation
* docs: edit changelog

## v1.0.1 (2025-07-03)
### Bug fixes
* fix: returned correct document type in the response of the rag_document tool
### Bug fixes
* fix: Now user can save personal settings
### Documentation
* docs: improved deploy documentation
### Refactoring
* refactor: changed document type selection structure from string array to dict array

## v1.0.0 (2025-06-24)

## v0.8.1 (2025-06-24)
