from flask import Blueprint
from .users import rbot_admin_users
# from .roles import rbot_admin_roles  # se hai altri blueprint

# Crea il blueprint principale
rbot_admin = Blueprint('rbot_admin', __name__, template_folder='templates')

# Registra i sotto-blueprint
rbot_admin.register_blueprint(rbot_admin_users, url_prefix='/users')
# rbot_admin.register_blueprint(rbot_admin_roles, url_prefix='/roles')  # esempio

# Ora puoi importare rbot_admin nel tuo create_app