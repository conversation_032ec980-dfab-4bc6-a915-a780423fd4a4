CREATE TABLE DWH_PUBLIC.INSTALLED_BASE (
	ID_PNC NUMBER(38, 0) NOT NULL ENABLE,
	INTERNAL_CODE  NVARCHAR2(80), -- Always use in OR with DWH_PUBLIC.INSTALLED_BASE.PRODUCT_NUMBER
	PRODUCT_NUMBER NVARCHAR2(80), -- Always use in OR with DWH_PUBLIC.INSTALLED_BASE.INTERNAL_CODE
	SERVICE_PRODUCT_DESCRIPTION NVARCHAR2(960),
	SERIAL_NUMBER NVARCHAR2(80),
	QC_NUMBER NVARCHAR2(80),
	SERVICE_MODEL NVARCHAR2(480),
	DATA_SOURCE_INSTALLED_BASE NVARCHAR2(12),
	DATA_SOURCE_INSTALLED_BASE_DESCRIPTION NVARCHAR2(400),
	SALES_ORDER_NUMBER NVARCHAR2(52),
	SALES_ORDER_NUMBER_ROW NUMBER(38, 0),
	WARRANTY_CONDITION NVARCHAR2(12),
	ORDER_BRAND NVARCHAR2(8),
	<PERSON>HY<PERSON>CAL_POSITION NVARCHAR2(200),
	PHYSICAL_AREA NVARCHAR2(40),
	DELIVERY_NOTE_COMPANY NVARCHAR2(12),
	DELIVERY_NOTE_DATE DATE,
	DELIVERY_NOTE_NUMBER NVARCHAR2(40),
	COMPANY_CODE NVARCHAR2(40),
	INVOICE_COMPANY NVARCHAR2(12),
	INVOICE_DATE DATE,
	INVOICE_NUMBER NVARCHAR2(40),
	DISTRIBUTION_STATUS NVARCHAR2(4),
	INSTALLATION_DATE DATE,
	END_WARRANTY_COSTS_DATE DATE,
	END_WARRANTY_SPARES_DATE DATE,
	INTERNAL_COMMENT NVARCHAR2(1440),
	SKILL NVARCHAR2(8),
	PRODUCT_LINE NVARCHAR2(12),
	CURRENT_CONTRACT_NUMBER NVARCHAR2(80),
	SITE_DATA_SOURCE NVARCHAR2(12),
	SITE_ID NVARCHAR2(400),
	SITE_CODE NVARCHAR2(40),
	SITE_NAME NVARCHAR2(1440),
	SITE_ADDRESS NVARCHAR2(1440),
	SITE_CITY NVARCHAR2(1440), -- Always use UPPER() 
	SITE_ZIP NVARCHAR2(160),
	SITE_PLATE NVARCHAR2(16),
	CUSTOMER_DATA_SOURCE NVARCHAR2(12),
	CUSTOMER_CODE NVARCHAR2(40),
	CUSTOMER_NAME NVARCHAR2(1440),
	CUSTOMER_ADDRESS NVARCHAR2(1440),
	CUSTOMER_CITY NVARCHAR2(1440),
	CUSTOMER_ZIP NVARCHAR2(160),
	CUSTOMER_PLATE NVARCHAR2(16),
	IDPNC_SITEDI NVARCHAR2(800) NOT NULL ENABLE,
	ID_CUSTOMER NUMBER,
	ROW_NUM NUMBER,
	SITE_PLAT_DESCRIPTION NVARCHAR2(800),
	CUSTOMER_PLATE_DESCRIPTION NVARCHAR2(800),
	WARRANTY_CONDITION_DESCRIPTION NVARCHAR2(1000),
	BRAND_DESCRIPTION NVARCHAR2(1000),
	ACTIVE_MACHINE NVARCHAR2(1000),
	SKILL_DESCRIPTION NVARCHAR2(1000),
	ELX_CST NVARCHAR2(1000),
	ELX_CST_DESCRIPTION NVARCHAR2(1000),
	DIRECT_SERVICE NVARCHAR2(1000),
	SITE_COUNTRY_ISO2 NVARCHAR2(1000), -- Always use UPPER() 
	SITE_COUNTRY_ISO2_DESCRIPTION NVARCHAR2(1000)
	PRODUCT_LINE_DESCRIPTION NVARCHAR2(1000),
	CONSTRAINT PK_IDPNC_SITEDI PRIMARY KEY (IDPNC_SITEDI) USING INDEX ENABLE
);