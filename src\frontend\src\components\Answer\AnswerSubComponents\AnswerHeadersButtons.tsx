import React from "react";
import { Stack, IconButton } from "@fluentui/react";
import { FeedbackType, ChatResponse } from "../../../api";

interface Props {
    chatResponse: ChatResponse;
    onThoughtProcessClicked: () => void;
    onSupportingContentClicked: () => void;
}

export const AnswerHeaderButtons = ({
    onThoughtProcessClicked, 
    onSupportingContentClicked,
    chatResponse, 
}: Props) => {
    return (
        <div>
            <IconButton
                style={{ color: "black" }}
                iconProps={{ iconName: "Lightbulb" }}
                title="Show thought process"
                ariaLabel="Show thought process"
                onClick={onThoughtProcessClicked}
                disabled={
                    !chatResponse.classification &&
                    !chatResponse.answer?.query &&
                    !chatResponse.answer?.query_generation_prompt &&
                    !chatResponse.answer?.query_result
                }
            />
            <IconButton
                style={{ color: "black" }}
                iconProps={{ iconName: "ClipboardList" }}
                title="Show supporting content"
                ariaLabel="Show supporting content"
                onClick={onSupportingContentClicked}
                disabled={!chatResponse.data_points?.length}
            />
        </div>
    )
}