ABS
ABSOLUTE
ADD
ALIAS
ALL
AND
ANY
AS
ASC
ASCII
AT
AVG
BETWEEN
BY
CASE
CAST
CEIL
CEILING
COALESCE
COLLATE
COLLATION
COMPUTE
CONCAT
CONNECT
CONTAINS
CONTAINSTABLE
CONVERT
COUNT
CROSS
CUBE
CURRENT
CURRENT_DATE
CURRENT_PATH
CURRENT_ROLE
CURRENT_TIME
CURRENT_TIMESTAMP
CURRENT_USER
CURSOR
CYCLE
DAY
DAYO<PERSON>ONTH
DAYOFWEEK
DAYOFYEAR
DAYS
DECODE
DESC
DESCRIBE
DISTINCT
EQUALS
ENDSELECT
EVERY
EXCEPT
EXCLUDE
EXCLUDING
EXCLUSIVE
EXISTS
EXP
EXTRACT
FALSE
FETCH
FIRST
FLOOR
FLOPPY
FROM
FULL
GREATEST
GROUP
HAVING
HOUR
HOURS
IFNULL
IN
INITCAP
INNER
INSENSITIVE
INSTR
INTERSECT
IS
JOIN
LAST
LATERAL
LEAST
LEFT
LESS
LENGTH
LEVEL
LIKE
LIMIT
LINENO
LOWER
LPAD
LTRIM
MAX
MAXVALUE
MICROSECOND
MICROSECONDS
MIN
MINUS
MINUTE
MINUTES
MINVALUE
MOD
MODULE
MONTH
MONTHS
NOCYCLE
NONE
NOT
NULL
NULLIF
OF
ON
ONCE
ONLY
OR
ORDER
OUT
OUTER
PAD
PERCENT
PI
POSITION
POWER
QUALIFICATION
QUARTER
RAND
RANGE
RELATIVE
REPLACE
REPLICATE
RIGHT
ROWID
ROWIDTOCHAR
ROWLABEL
ROWNUM
RPAD
RRN
RTRIM
SECOND
SECONDS
SELECT
SIGN
SIZE
SOME
SORT
SQL
SQRT
SUBSTR
SUBSTRING
SUFFIX
SUM
SYSDATE
SYSTIME
SYSTIMESTAMP
THAN
TIME
TIMESTAMP
TIMEZONE_HOUR
TIMEZONE_MINUTE
TO
TOP
TRIM
TRUE
TRUNCATE
UNION
UPPER
VALUE
VALUES
WEEK
WHERE
WITH
YEAR
YEARS
ZONE
+
-
*
/
||
=
<>
!=
>
>=
<
<=