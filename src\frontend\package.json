{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "watch": "tsc && vite build --watch"}, "dependencies": {"@fluentui/react": "^8.105.3", "@fluentui/react-components": "^9.66.3", "@fluentui/react-dialog": "^9.13.3", "@fluentui/react-icons": "^2.0.195", "@react-spring/web": "^9.7.1", "classnames": "^2.5.1", "dompurify": "^3.0.1", "express": "^4.21.1", "file-saver": "^2.0.5", "html2pdf.js": "^0.10.3", "immer": "^10.1.1", "jszip": "^3.10.1", "nanoid": "^5.0.6", "papaparse": "^5.4.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-medium-image-zoom": "^5.2.10", "react-router-dom": "^6.8.1", "sql-formatter": "^15.3.0", "xlsx": "^0.18.5", "xslx": "^1.0.0"}, "devDependencies": {"@types/dompurify": "^2.4.0", "@types/file-saver": "^2.0.7", "@types/react": "^18.0.27", "@types/react-dom": "^18.0.10", "@vitejs/plugin-react": "^3.1.0", "prettier": "^2.8.3", "typescript": "^4.9.5", "vite": "^4.5.5"}}