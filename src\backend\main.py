# main.py

from flask import (
    Blueprint, render_template, current_app
)
from flask_login import current_user


main = Blueprint('main', __name__, url_prefix='/main')

@main.route('/')
def index():
    return render_template('index.html')

@main.route("/assets/<path:rest_of_path>")
def assets(rest_of_path):
    return current_app.send_static_file(f"assets/{rest_of_path}")

@main.route('/profile')
def profile():
    return render_template('profile.html', name=current_user.name)