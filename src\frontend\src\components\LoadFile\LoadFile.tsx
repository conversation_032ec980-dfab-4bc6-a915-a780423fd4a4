import React, { useState } from "react"
import styles from "./LoadFile.module.css"
import check_logo from "../../assets/IconFeCheck.svg"
import { uploadFileApi } from "../../api"
import * as XLSX from "xlsx"
import TaskSelector from "../TaskSelector/TaskSelector"

interface Props {
    onUploadSuccess: (flag: boolean) => void;
}

interface ErrorPopupProps {
    message: string;
    onClose: () => void;
}


export const UploadFile = ({ onUploadSuccess }: Props) => {

    const [isFileUploaded, setIsFileUploaded] = (useState(false))
    const [isFileSubmitted, setIsFileSubmitted] = (useState(false))
    const [file, setFile] = useState<File | null>(null)
    const [headerContent, setHeaderContent] = useState([])
    const [fileContent, setFileContent] = useState([])
    const [errorMessage, setErrorMessage] = useState('');


    const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file: any = e.currentTarget.files
        const maxSize = 5 * 1024 * 1024; // Imposta la soglia a 5MB

        if (file && file[0].size > maxSize) {
            setErrorMessage('The file is too large. The maximum allowed size is 5MB.');
            return;
        }

        if (file)
            setFile(file[0])
        // show success message on file upload
        setIsFileUploaded(true)


        const reader = new FileReader();
        reader.onload = function (e) {
            const bstr = e.target?.result;
            const wb = XLSX.read(bstr, { type: 'binary' });
            const wsname: string = wb.SheetNames[0]; // Usa il primo nome del foglio come stringa
            const ws = wb.Sheets[wsname];
            const jsonData: any[] = XLSX.utils.sheet_to_json(ws, { header: 1 });

            let header = jsonData[0];
            let data: any = jsonData.slice(1, 4); // Prendi le prime 4 righe di dati

            setHeaderContent(header);
            setFileContent(data);
            console.log("Header:", header);
            console.log("Data:", data);
        };

        reader.readAsArrayBuffer(file[0]);
    }

    const ErrorPopup: React.FC<ErrorPopupProps> = ({ message, onClose }) => (
        <div className={styles.popup}>
            <div className={styles.popup_inner}>
                <h2>Error</h2>
                <p>{message}</p>
                <button onClick={onClose}>Close</button>
            </div>
        </div>
    );

    const handleFileSubmit = async () => {
        try {
            if (file) {
                await uploadFileApi(file);
                console.log('File caricato con successo'); // Log per debug
            }
        } catch (error) {
            console.error('Errore durante il caricamenot del file', error)
        }
        setIsFileSubmitted(true);
        onUploadSuccess(true);
    };

    return !isFileUploaded || !isFileSubmitted ? (
        <div>
            {!isFileUploaded && !errorMessage ? (
                <div>
                    <label htmlFor="dropzone-file">
                        <div >
                            <svg className={styles.uploadSVG} aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 16">
                                <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2" />
                            </svg>
                            <p className={styles.clickToUpload}><span className="font-semibold">Click to upload</span> or drag and drop</p>
                            <p className={styles.fileType}>Supported file: XLSX, CSV & MaxDim 5MB</p>
                        </div>
                        <input id="dropzone-file" className={styles.fileLoadInput} type="file" accept='.xlsx, .csv' required onChange={handleFileInput} />
                    </label>
                </div>
            ) : (
                <div>
                    {errorMessage && <ErrorPopup message={errorMessage} onClose={() => setErrorMessage('')} />}
                </div>
            )}
            {isFileUploaded && (
                <div>
                    <p className={styles.clickToUpload}>File ready to be uploaded</p>
                    <p className={styles.fileType}>Be clear with the request, specify clearly "Translate the column" ... or "Give me the custom class"</p>
                    {<div><TaskSelector /></div>}
                    <h4>Preview file</h4>
                    {fileContent.length > 0 && (
                        <div className={styles.table_container}>
                            <table className={styles.styled_table}>
                                <thead>
                                    <tr>
                                        {headerContent.map((key, index) => (
                                            <th key={index}>{key}</th>
                                        ))}
                                    </tr>
                                </thead>
                                <tbody>
                                    {fileContent.map((row: any, rowIndex) => (
                                        <tr key={rowIndex}>
                                            {row.map((cell: any, cellIndex: any) => (
                                                <td key={cellIndex}>{cell || 'N/A'}</td>
                                            ))}
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    )}
                    <input type="submit" onClick={handleFileSubmit} value="Upload" />
                </div>
            )}
        </div>
    ) : (
        <div>
            <img src={check_logo} alt="check logo" aria-label="CompliBot" height="100px" />
        </div>
    )
}

export default UploadFile