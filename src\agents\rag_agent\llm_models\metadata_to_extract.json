{"language_description": "The language of the query or a specific language requested by the user.", "edition": "The edition of the document, typically a number. If not provided in the query, Don't put this field in the JSON.", "product_code": "The product code as referred to in the query, such as 'product code', 'internal code', 'product number','PNC', 'Serial Number' or 'Factory Model', factory model could also be referred as 'model name'. This code could have special characters like the underscore '_', so consider it in this case as a unique string. If none is present, fill with the word 'missing' with no suffix. Add to the information extracted the suffix '*serial_number' or '*factory_model', '*internal_code', '*product_number', '*PNC', '*product_code' based on how it is referred. Some example on how to fill this field: 'product_code': '12312*internal_code', 'product_code': '1L0FC*factory_model', 'product_code': '2010001*product_number', 'product_code':98700121*PNC, 'product_code': 'missing'", "document_number": "The number of the document, some requests are formatted like 'I want the document number 23JAK' or 'Give me the spare part catalogue 1231231' or 'I want the installation manual with document number i want the certification conformity document number equal to 598404P00' If not provided in the query, fill this field in the JSON with 'missing'.", "production_status": "The  production status of the document, could be: Draft (fill with 0), In production (fill with 1), out of production (fill with 2). Ignore this field if is missing in the query.", "distribution_status": "The distribution status of the document, could be in distribution (fill with 0) or out of distribution (fill with 1). Ignore this field if is missing in the query."}