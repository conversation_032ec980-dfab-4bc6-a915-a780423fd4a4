{"DWH_PUBLIC": {"description": "", "tables": {"DWH_PUBLIC.ITEM_COMPLIANCES": {"schema": "CREATE TABLE DWH_PUBLIC.ITEM_COMPLIANCES ( -- Use only if asked for details on a certification\n\tCOMPANY_CODE VARCHAR2(3 BYTE),\n\tCOMPANY_DESCRIPTION VARCHAR2(100 BYTE),\n\tPLANT_CODE VARCHAR2(20 BYTE),\n\tPLANT_DESCRIPTION VARCHAR2(100 BYTE),\n\tSUPPLIER VARCHAR2(10 BYTE),\n\tSUPPLIER_DESCRIPTION VARCHAR2(240 BYTE),\n\tITEM_CODE VARCHAR2(20 BYTE),\n\tCERTIFICATION_TYPE VARCHAR2(3 BYTE),\n\tCERTIFICATION_DESCRIPTION VARCHAR2(100 BYTE),\n\tCERTIFICATION_LINE NUMBER,\n\tCERTIFICATION_STATUS VARCHAR2(2 BYTE),\n\tCERTIFICATION_STATUS_DESCRIPTION VARCHAR2(100 BYTE),\n\tCERTIFICATION_START_VALIDITY_DATE DATE,\n\tCERTIFICATION_END_VALIDITY_DATE DATE,\n\tCERTIFICATION_COUNTRY VARCHAR2(3 BYTE),\n\tCERTIFICATION_COUNTRY_DESCRIPTION VARCHAR2(100 BYTE),\n\tPREFERENTIAL CHAR(1 BYTE),\n\tPREFERENTIAL_DESCRIPTION VARCHAR2(100 BYTE),\n\tHS_CODE VARCHAR2(10 BYTE),\n\tDUAL_USE CHAR(1 BYTE),\n\tCERTIFICATION_CATEGORY VARCHAR2(3 BYTE),\n\tCERTIFICATION_CATEGORY_DESCRIPTION VARCHAR2(100 BYTE),\n\tLINE_CATEGORY CHAR(1 BYTE),\n\tPERCENTAGE_SUBSTANCE VARCHAR2(10 BYTE),\n\tSUBSTANCE_CAS_NUMBER VARCHAR2(20 BYTE),\n\tSUBSTANCE_EC_NUMBER VARCHAR2(20 BYTE),\n\tSUBSTANCE_OTHER_NUMBER VARCHAR2(20 BYTE),\n\tTOTAL_VOLUME VARCHAR2(20 BYTE),\n\tEXEMPTED VARCHAR2(20 BYTE),\n\tPRE_REGISTER VARCHAR2(20 BYTE),\n\tPRE_REGISTER_DATE DATE,\n\tREGISTER_DEADLINE_DATE DATE,\n\tREGISTER_NUMBER VARCHAR2(20 BYTE),\n\tWEIGHT VARCHAR2(20 BYTE),\n\tRML_SUBST_OVER_PERC VARCHAR2(20 BYTE),\n\tUPD_DATE DATE,\n\tTABLE_SOURCE VARCHAR2(25 BYTE),\n\tPRIMARY KEY (COMPANY_CODE, PLANT_CODE, SUPPLIER, ITEM_CODE, CERTIFICATION_TYPE, CERTIFICATION_LINE)\n);", "aliases": [], "description": "", "columns": {"COMPANY_CODE": {"aliases": [], "description": ""}, "COMPANY_DESCRIPTION": {"aliases": [], "description": ""}, "PLANT_CODE": {"aliases": [], "description": ""}, "PLANT_DESCRIPTION": {"aliases": [], "description": ""}, "SUPPLIER": {"aliases": [], "description": ""}, "SUPPLIER_DESCRIPTION": {"aliases": [], "description": ""}, "ITEM_CODE": {"aliases": [], "description": ""}, "CERTIFICATION_TYPE": {"aliases": [], "description": ""}, "CERTIFICATION_DESCRIPTION": {"aliases": [], "description": ""}, "CERTIFICATION_LINE": {"aliases": [], "description": ""}, "CERTIFICATION_STATUS": {"aliases": [], "description": ""}, "CERTIFICATION_STATUS_DESCRIPTION": {"aliases": [], "description": ""}, "CERTIFICATION_START_VALIDITY_DATE": {"aliases": [], "description": ""}, "CERTIFICATION_END_VALIDITY_DATE": {"aliases": [], "description": ""}, "CERTIFICATION_COUNTRY": {"aliases": [], "description": ""}, "CERTIFICATION_COUNTRY_DESCRIPTION": {"aliases": [], "description": ""}, "PREFERENTIAL": {"aliases": [], "description": ""}, "PREFERENTIAL_DESCRIPTION": {"aliases": [], "description": ""}, "HS_CODE": {"aliases": [], "description": ""}, "DUAL_USE": {"aliases": [], "description": ""}, "CERTIFICATION_CATEGORY": {"aliases": [], "description": ""}, "CERTIFICATION_CATEGORY_DESCRIPTION": {"aliases": [], "description": ""}, "LINE_CATEGORY": {"aliases": [], "description": ""}, "PERCENTAGE_SUBSTANCE": {"aliases": [], "description": ""}, "SUBSTANCE_CAS_NUMBER": {"aliases": [], "description": ""}, "SUBSTANCE_EC_NUMBER": {"aliases": [], "description": ""}, "SUBSTANCE_OTHER_NUMBER": {"aliases": [], "description": ""}, "TOTAL_VOLUME": {"aliases": [], "description": ""}, "EXEMPTED": {"aliases": [], "description": ""}, "PRE_REGISTER": {"aliases": [], "description": ""}, "PRE_REGISTER_DATE": {"aliases": [], "description": ""}, "REGISTER_DEADLINE_DATE": {"aliases": [], "description": ""}, "REGISTER_NUMBER": {"aliases": [], "description": ""}, "WEIGHT": {"aliases": [], "description": ""}, "RML_SUBST_OVER_PERC": {"aliases": [], "description": ""}, "UPD_DATE": {"aliases": [], "description": ""}, "TABLE_SOURCE": {"aliases": [], "description": ""}}}, "DWH_PUBLIC.ITEM_MASTER": {"schema": "CREATE TABLE DWH_PUBLIC.ITEM_MASTER (\n\tCOMPANY_CODE VARCHAR2(12 BYTE),\n\tCOMPANY_DESCRIPTION VARCHAR2(100 BYTE),\n\tPLANT_CODE VARCHAR2(12 BYTE),\n\tPLANT_DESCRIPTION VARCHAR2(50 BYTE),\n\tITEM_CODE VARCHAR2(25 BYTE),\n\tITEM_INTERNAL_CODE VARCHAR2(25 BYTE),\n\tSHORT_ITEM_CODE VARCHAR2(12 BYTE),\n\tMAIN_ITEM_DESCRIPTION VARCHAR2(160 BYTE),\n\tITEM_STOCKING_TYPE VARCHAR2(2 BYTE), -- Either \"M\" for \"manufacture\" or \"B\" for \"buy\"\n\tECO_NUMBER VARCHAR2(16 BYTE),\n\tITEM_PREFERRED_SUPPLIER VARCHAR2(20 BYTE),\n\tITEM_UNIT_OF_MEASURE_CODE VARCHAR2(4 BYTE),\n\tITEM_UNIT_OF_MEASURE_DESCRIPTION VARCHAR2(100 BYTE),\n\tSTART_PRODUCTION_DATE DATE,\n\tSTOP_PRODUCTION_DATE DATE,\n\tCOUNTRY_OF_ORIGIN VARCHAR2(6 BYTE), -- Accepts international vehicle registration codes only\n\tEXPIRING_DATE DATE, -- Country of origin expiration date\n\tPERCENTAGE_NOT_UE NUMBER(5, 0),\n\tFOOD_CONTACT VARCHAR2(2 BYTE),\n\tCURRENCY VARCHAR2(6 BYTE),\n\tSTANDARD_STK3_COST NUMBER(15, 5), -- Cost of production/manufacturing\n\tPURCHASING_COST NUMBER(15, 5),\n\tEXTERNAL_WORK_COST NUMBER(15, 5),\n\tITEM_CODE_VALID VARCHAR2(1 BYTE),\n\tSYSTEM_SOURCE VARCHAR2(10 BYTE),\n\tPRIMARY KEY (COMPANY_CODE, PLANT_CODE, ITEM_CODE, SYSTEM_SOURCE)\n);", "aliases": [], "description": "", "columns": {"COMPANY_CODE": {"aliases": [], "description": ""}, "COMPANY_DESCRIPTION": {"aliases": [], "description": ""}, "PLANT_CODE": {"aliases": [], "description": ""}, "PLANT_DESCRIPTION": {"aliases": [], "description": ""}, "ITEM_CODE": {"aliases": [], "description": ""}, "ITEM_INTERNAL_CODE": {"aliases": [], "description": ""}, "SHORT_ITEM_CODE": {"aliases": [], "description": ""}, "MAIN_ITEM_DESCRIPTION": {"aliases": [], "description": ""}, "ITEM_STOCKING_TYPE": {"aliases": [], "description": ""}, "ECO_NUMBER": {"aliases": [], "description": ""}, "ITEM_PREFERRED_SUPPLIER": {"aliases": [], "description": ""}, "ITEM_UNIT_OF_MEASURE_CODE": {"aliases": [], "description": ""}, "ITEM_UNIT_OF_MEASURE_DESCRIPTION": {"aliases": [], "description": ""}, "START_PRODUCTION_DATE": {"aliases": [], "description": ""}, "STOP_PRODUCTION_DATE": {"aliases": [], "description": ""}, "COUNTRY_OF_ORIGIN": {"aliases": [], "description": ""}, "EXPIRING_DATE": {"aliases": [], "description": ""}, "PERCENTAGE_NOT_UE": {"aliases": [], "description": ""}, "FOOD_CONTACT": {"aliases": [], "description": ""}, "CURRENCY": {"aliases": [], "description": ""}, "STANDARD_STK3_COST": {"aliases": [], "description": ""}, "PURCHASING_COST": {"aliases": [], "description": ""}, "EXTERNAL_WORK_COST": {"aliases": [], "description": ""}, "ITEM_CODE_VALID": {"aliases": [], "description": ""}, "SYSTEM_SOURCE": {"aliases": [], "description": ""}}}, "DWH_PUBLIC.COMMERCIAL_PRODUCTS": {"schema": "CREATE TABLE DWH_PUBLIC.COMMERCIAL_PRODUCTS (\n\tINTERNAL_CODE NVARCHAR2(25), -- Always use in OR with DWH_PUBLIC.COMMERCIAL_PRODUCTS.PRODUCT_NUMBER\n\tSHORT_DESCRIPTION NVARCHAR2(500),\n\tLONG_DESCRIPTION CLOB,\n\tSTART_PRODUCTION_DATE TIMESTAMP (6),\n\tSTOP_PRODUCTION_DATE TIMESTAMP (6),\n\tSTART_DISTRIBUTION_DATE TIMESTAMP (6),\n\tSTOP_DISTRIBUTION_DATE TIMESTAMP (6),\n\tPRODUCTION_STATUS NUMBER(38, 0),\n\tPRODUCTION_STATUS_DESCRIPTION NVARCHAR2(50),\n\tDISTRIBUTION_STATUS NCHAR(1),\n\tDIS<PERSON>IBUTION_STATUS_DESCRIPTION NVARCHAR2(40),\n\tPLANT_CODE NVARCHAR2(10),\n\tPLANT_DESCRIPTION NVARCHAR2(50),\n\tFACTORY_CODE NVARCHAR2(5),\n\tFACTORY_DESCRIPTION NVARCHAR2(30),\n\t<PERSON><PERSON><PERSON>_OF_PRODUCT NVARCHAR2(30),\n\tCOUNTRY_OF_ORIGIN_ISO_CODE NVARCHAR2(4),\n\tCOUNTRY_OF_ORIGIN_DECLARATION_EXPIRY_DATE TIMESTAMP (6),\n\tPREFERENTIAL_ORIGIN NVARCHAR2(1), -- Either 'P' for \"preferential\" or 'C' for \"common\"\n\tPREFERENTIAL_ORIGIN_PERCENTAGE_EU FLOAT(53),\n\tCOO_ISO2 CHAR(2 BYTE),\n\tCOUNTRY_OF_ORIGIN NVARCHAR2(50),\n\tPREFERENTIAL_ORIGIN_PERCENTAGE_EXTRA_EU FLOAT(53),\n\tPRODUCT_NUMBER NVARCHAR2(30), -- Always use in OR with DWH_PUBLIC.COMMERCIAL_PRODUCTS.INTERNAL_CODE\n\tFACTORY_MODEL NVARCHAR2(30),\n\tEXTERNAL_MODEL_NAME NVARCHAR2(30),\n\tLANGUAGE_GROUP NVARCHAR2(5),\n\tIRONER_FRONT_COLOR NVARCHAR2(100),\n\tWASHER_FRONT_COLOR NVARCHAR2(100),\n\tDRYER_FRONT_COLOR NVARCHAR2(50),\n\tIRONER_SIDE_COLOR NVARCHAR2(100),\n\tWASHER_SIDE_COLOR NVARCHAR2(100),\n\tDRYER_SIDE_COLOR NVARCHAR2(50),\n\tELECTRICAL_CONNECTION_VOLTAGE NVARCHAR2(100),\n\tELECTRICAL_CONNECTION_FREQUENCY NVARCHAR2(100),\n\tELECTRICAL_CONNECTION_PHASE NVARCHAR2(100),\n\tHEATING_TYPE NVARCHAR2(32),\n\tWASHER_COINMETER NVARCHAR2(20),\n\tDRYER_COINMETER NVARCHAR2(20),\n\tDRYER_CONTROL_SYSTEM NVARCHAR2(50),\n\tIRONER_CONTROL_SYSTEM NVARCHAR2(100),\n\tWASHER_CONTROL_SYSTEM NVARCHAR2(100),\n\tDRYER_SOFTWARE_CODE NVARCHAR2(50),\n\tWASHER_SOFTWARE_CODE NVARCHAR2(100),\n\tWATER_CONNECTION_TEMPERATURE NVARCHAR2(10),\n\tIRONER_NUMBER_OF_IO_BOARDS FLOAT(126),\n\tWASHER_NUMBER_OF_IO_BOARDS FLOAT(126),\n\tPRIMARY KEY ( INTERNAL_CODE )\n)", "aliases": [], "description": "", "columns": {"INTERNAL_CODE": {"aliases": [], "description": ""}, "SHORT_DESCRIPTION": {"aliases": [], "description": ""}, "LONG_DESCRIPTION": {"aliases": [], "description": ""}, "START_PRODUCTION_DATE": {"aliases": [], "description": ""}, "STOP_PRODUCTION_DATE": {"aliases": [], "description": ""}, "START_DISTRIBUTION_DATE": {"aliases": [], "description": ""}, "STOP_DISTRIBUTION_DATE": {"aliases": [], "description": ""}, "PRODUCTION_STATUS": {"aliases": [], "description": ""}, "PRODUCTION_STATUS_DESCRIPTION": {"aliases": [], "description": ""}, "DISTRIBUTION_STATUS": {"aliases": [], "description": ""}, "DISTRIBUTION_STATUS_DESCRIPTION": {"aliases": [], "description": ""}, "PLANT_CODE": {"aliases": [], "description": ""}, "PLANT_DESCRIPTION": {"aliases": [], "description": ""}, "FACTORY_CODE": {"aliases": [], "description": ""}, "FACTORY_DESCRIPTION": {"aliases": [], "description": ""}, "KIND_OF_PRODUCT": {"aliases": [], "description": ""}, "COUNTRY_OF_ORIGIN_ISO_CODE": {"aliases": [], "description": ""}, "COUNTRY_OF_ORIGIN_DECLARATION_EXPIRY_DATE": {"aliases": [], "description": ""}, "PREFERENTIAL_ORIGIN": {"aliases": [], "description": ""}, "PREFERENTIAL_ORIGIN_PERCENTAGE_EU": {"aliases": [], "description": ""}, "COO_ISO2": {"aliases": [], "description": ""}, "COUNTRY_OF_ORIGIN": {"aliases": [], "description": ""}, "PREFERENTIAL_ORIGIN_PERCENTAGE_EXTRA_EU": {"aliases": [], "description": ""}, "PRODUCT_NUMBER": {"aliases": [], "description": ""}, "FACTORY_MODEL": {"aliases": [], "description": ""}, "EXTERNAL_MODEL_NAME": {"aliases": [], "description": ""}, "LANGUAGE_GROUP": {"aliases": [], "description": ""}, "IRONER_FRONT_COLOR": {"aliases": [], "description": ""}, "WASHER_FRONT_COLOR": {"aliases": [], "description": ""}, "DRYER_FRONT_COLOR": {"aliases": [], "description": ""}, "IRONER_SIDE_COLOR": {"aliases": [], "description": ""}, "WASHER_SIDE_COLOR": {"aliases": [], "description": ""}, "DRYER_SIDE_COLOR": {"aliases": [], "description": ""}, "ELECTRICAL_CONNECTION_VOLTAGE": {"aliases": [], "description": ""}, "ELECTRICAL_CONNECTION_FREQUENCY": {"aliases": [], "description": ""}, "ELECTRICAL_CONNECTION_PHASE": {"aliases": [], "description": ""}, "HEATING_TYPE": {"aliases": [], "description": ""}, "WASHER_COINMETER": {"aliases": [], "description": ""}, "DRYER_COINMETER": {"aliases": [], "description": ""}, "DRYER_CONTROL_SYSTEM": {"aliases": [], "description": ""}, "IRONER_CONTROL_SYSTEM": {"aliases": [], "description": ""}, "WASHER_CONTROL_SYSTEM": {"aliases": [], "description": ""}, "DRYER_SOFTWARE_CODE": {"aliases": [], "description": ""}, "WASHER_SOFTWARE_CODE": {"aliases": [], "description": ""}, "WATER_CONNECTION_TEMPERATURE": {"aliases": [], "description": ""}, "IRONER_NUMBER_OF_IO_BOARDS": {"aliases": [], "description": ""}, "WASHER_NUMBER_OF_IO_BOARDS": {"aliases": [], "description": ""}}}, "DWH_PUBLIC.BOM": {"schema": "CREATE TABLE DWH_PUBLIC.BOM (\n\tCOMPANY_CODE NVARCHAR2(5),\n\tCOMPANY_DESCRIPTION NVARCHAR2(50),\n\tPLANT_CODE NVARCHAR2(12),\n\tPLANT_DESCRIPTION NVARCHAR2(50),\n\tCHILD_ITEM_CODE NVARCHAR2(25),\n\tPARENT_ITEM_CODE NVARCHAR2(25),\n\tQUANTITY NUMBER(25, 0),\n\tITEM_UNIT_OF_MEASURE NVARCHAR2(2),\n\tITEM_UNIT_OF_MEASURE_DESCRIPTION NVARCHAR2(500),\n\tSTART_PRODUCTION_DATE DATE,\n\tSTOP_PRODUCTION_DATE DATE,\n\t<PERSON><PERSON><PERSON><PERSON> NVARCHAR2(2),\n\tPOSITION NVARCHAR2(8),\n\tSYSTEM_SOURCE NVARCHAR2(10),\n\tPRIMARY KEY (COMPANY_CODE, PLANT_CODE, CHILD_ITEM_CODE, PARENT_ITEM_CODE, <PERSON><PERSON><PERSON><PERSON>, POSITION, START_PRODUCTION_DATE, STOP_PRODUCTION_DATE)\n);", "aliases": [], "description": "", "columns": {"COMPANY_CODE": {"aliases": [], "description": ""}, "COMPANY_DESCRIPTION": {"aliases": [], "description": ""}, "PLANT_CODE": {"aliases": [], "description": ""}, "PLANT_DESCRIPTION": {"aliases": [], "description": ""}, "CHILD_ITEM_CODE": {"aliases": [], "description": ""}, "PARENT_ITEM_CODE": {"aliases": [], "description": ""}, "QUANTITY": {"aliases": [], "description": ""}, "ITEM_UNIT_OF_MEASURE": {"aliases": [], "description": ""}, "ITEM_UNIT_OF_MEASURE_DESCRIPTION": {"aliases": [], "description": ""}, "START_PRODUCTION_DATE": {"aliases": [], "description": ""}, "STOP_PRODUCTION_DATE": {"aliases": [], "description": ""}, "MODULE": {"aliases": [], "description": ""}, "POSITION": {"aliases": [], "description": ""}, "SYSTEM_SOURCE": {"aliases": [], "description": ""}}}, "DWH_PUBLIC.AGREEMENT_VIEW": {"schema": "CREATE TABLE DWH_PUBLIC.AGREEMENT_VIEW (\n\tCOMPANY_CODE VARCHAR2(20 BYTE),\n\tCOMPANY_DESCRIPTION VARCHAR2(100 BYTE),\n\tPLANT_CODE VARCHAR2(8 BYTE),\n\tPLANT_DESCRIPTION VARCHAR2(240 BYTE),\n\tAGREEMENT_CODE VARCHAR2(8 BYTE),\n\tBUYER_CODE VARCHAR2(2 BYTE),\n\tBUYER_DESCRIPTION VARCHAR2(240 BYTE),\n\tSUPPLIER VARCHAR2(10 BYTE),\n\tSUPPLIER_DESCRIPTION VARCHAR2(240 BYTE),\n\tSTART_VALIDITY_DATE DATE,\n\tSTOP_VALIDITY_DATE DATE,\n\tROW_NUMBER NUMBER(5, 0),\n\tAMOUNT NUMBER(13, 2),\n\tCURRENCY VARCHAR2(4 BYTE),\n\tCURRENCY_DESCRIPTION VARCHAR2(240 BYTE),\n\tITEM_CODE VARCHAR2(20 BYTE),\n\tITEM_PRICE NUMBER(15, 5),\n\tITEM_DISCOUNT_PERCENTAGE_1 NUMBER(5, 2),\n\tITEM_DISCOUNT_PERCENTAGE_2 NUMBER(5, 2),\n\tITEM_LOT_QTY_CODE NUMBER(10, 2),\n\tITEM_LOT_QTY VARCHAR2(100 BYTE),\n\tITEM_BUDGET_QTY NUMBER(10, 2),\n\tSUPPLIER_ITEM_CODE VARCHAR2(20 BYTE),\n\tPRIMARY KEY (COMPANY_CODE, PLANT_CODE, AGREEMENT_CODE, ITEM_CODE, ITEM_LOT_QTY_CODE)\n)", "aliases": [], "description": "", "columns": {"COMPANY_CODE": {"aliases": [], "description": ""}, "COMPANY_DESCRIPTION": {"aliases": [], "description": ""}, "PLANT_CODE": {"aliases": [], "description": ""}, "PLANT_DESCRIPTION": {"aliases": [], "description": ""}, "AGREEMENT_CODE": {"aliases": [], "description": ""}, "BUYER_CODE": {"aliases": [], "description": ""}, "BUYER_DESCRIPTION": {"aliases": [], "description": ""}, "SUPPLIER": {"aliases": [], "description": ""}, "SUPPLIER_DESCRIPTION": {"aliases": [], "description": ""}, "START_VALIDITY_DATE": {"aliases": [], "description": ""}, "STOP_VALIDITY_DATE": {"aliases": [], "description": ""}, "ROW_NUMBER": {"aliases": [], "description": ""}, "AMOUNT": {"aliases": [], "description": ""}, "CURRENCY": {"aliases": [], "description": ""}, "CURRENCY_DESCRIPTION": {"aliases": [], "description": ""}, "ITEM_CODE": {"aliases": [], "description": ""}, "ITEM_PRICE": {"aliases": [], "description": ""}, "ITEM_DISCOUNT_PERCENTAGE_1": {"aliases": [], "description": ""}, "ITEM_DISCOUNT_PERCENTAGE_2": {"aliases": [], "description": ""}, "ITEM_LOT_QTY_CODE": {"aliases": [], "description": ""}, "ITEM_LOT_QTY": {"aliases": [], "description": ""}, "ITEM_BUDGET_QTY": {"aliases": [], "description": ""}, "SUPPLIER_ITEM_CODE": {"aliases": [], "description": ""}}}}}}