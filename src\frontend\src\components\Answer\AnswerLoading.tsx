import { Stack } from "@fluentui/react";
import { animated, useSpring } from "@react-spring/web";
import { DefaultButton } from "@fluentui/react";

import styles from "./Answer.module.css";
import { AnswerIcon } from "./AnswerIcon";

interface AnswerLoadingProps {
    onStopChat: () => void;
    isChatStopped: boolean;
}

export const AnswerLoading: React.FC<AnswerLoadingProps> = ({ onStopChat, isChatStopped }) => {
    const animatedStyles = useSpring({
        from: { opacity: 0 },
        to: { opacity: 1 }
    });

    return (
        <animated.div style={{ ...animatedStyles }}>
            <Stack className={styles.answerContainer} verticalAlign="space-between">
                <AnswerIcon />
                <Stack.Item grow>
                    <p className={styles.answerText}>
                        Generating answer
                        <span className={styles.loadingdots} />
                    </p>
                </Stack.Item>
                {!isChatStopped && (
                    <Stack horizontal>
                        <Stack.Item align="end">
                            <DefaultButton
                                text="Stop Answering"
                                onClick={onStopChat}
                                allowDisabledFocus
                            />
                        </Stack.Item>
                    </Stack>
                )}
            </Stack>
        </animated.div>
    );
};
