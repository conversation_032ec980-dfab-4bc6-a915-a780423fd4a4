# Node artifact files
node_modules/
jspm_packages/
dist/

# React static
# react_static/

# Optional npm cache directory
.npm

# Compiled Java class files
*.class

# Compiled Python bytecode
__pycache__/
*.py[cod]

# Log files
*.log
*.log.*

# Package files
*.jar

# Maven
target/
dist/

# JetBrains IDE
.idea/

# Unit test reports
TEST*.xml

# Generated by MacOS
.DS_Store

# Generated by Windows
Thumbs.db

# Applications
*.app
*.exe
*.war

# Large media files
*.mp4
*.tiff
*.avi
*.flv
*.mov
*.wmv

# Private files
*.private

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
pride_indexer/test

# Jupyter sketches
*sketch.ipynb
*sketches.ipynb
*sketch.py
*sketches.py
*query_tester.ipynb
*blob_operation.ipynb
*.ipynb
# Oracle traces
*.trc

# UI instance variables
instance/
pride_indexer/src/indexer/temp_pdf
# IIS config file
web.config

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# Test runs
tests/bot_test_runs/*

# .lock files
*.lock

# vscode files
.vscode/*


pride_indexer/src/indexer/temp_pdf