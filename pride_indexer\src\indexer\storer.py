from os.path import basename, dirname
from typing import Iterable
import backoff
from azure.core.exceptions import HttpResponseError
from langchain_core.documents.base import Document
from datetime import datetime
from src.agents.rag_agent.storage import get_rag_storage
from utils.core import get_logger, Singleton

logger = get_logger(__file__)


def _get_document_id(split: Document) -> str:
    url = split.metadata["url"]
    return dirname(url) + basename(url)


class DocumentStorer:
    """Allows to store chunks of documents in a vector storage."""

    def __init__(self, bot_name: str) -> None:
        """Creates a new document storer that will keep uploading, from the last element provided by `upload_tracker`, the documents to the vector storage.

        Args:
            upload_tracker (str): The URI identifying the resource that keeps track of the uploads.
        """
        logger.debug("Init storage...")
        self.storage = get_rag_storage(bot_name)
        logger.info("Storage available!")


    @backoff.on_exception(
            backoff.expo,
            (HttpResponseError),
            max_tries=8,
            jitter=backoff.full_jitter,
            factor=2,
        )
    async def upload_splits(self, document_splits: Iterable[Document]) -> None:
        """Uploads the provided splits to the vector storage. All the splits must belong to the same document.

        Args:
            documents (Iterable[Document]): A set of documents to upload.
        """
        performance_metric_file = open('performance_metrics.log', 'a')
        ids = list(map(_get_document_id, document_splits))
        assert len(set(ids)) == 1, "Provide splits from just one document."

        splits = list(document_splits)
        ids = [f"{id}_{i}" for i, id in enumerate(ids)]
        
        
        print(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - Start Uploading {len(ids)} chunks", file=performance_metric_file)
        for i in range(0, len(splits),50):
            batch_ids = ids[i:i+50]
            logger.debug(f"Uploading chunks from {batch_ids[0]} to {batch_ids[-1]}...")
            batch_splits = splits[i:i+50] 
            loaded_ids = await self.storage.aadd_documents(batch_splits, keys=batch_ids)
        print(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - End Uploading {len(ids)} chunks", file=performance_metric_file)

        logger.info(
            f"Loaded {len(loaded_ids)} documents and assigned following ids:\n{loaded_ids}"
        )
        #per ogni file path unique, aggiornare il dato flag sulla tabella. 
