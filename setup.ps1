
param (
    [switch]$h,
    [switch]$skip
)

# Se è stato passato il flag -h, mostra l'help e termina
if ($h) {
    Write-Host "Use: setup.ps1 [-h] [-skip]"
    Write-Host "`t-h`tHelp"
    Write-Host "`t-skip`tDoes not execute the virtual environment setup, only updates the environment variables"
    exit
}


Function fillWebConfig($execution_env) {
    # Load the XML file
    $xmlDoc = New-Object System.Xml.XmlDocument

    $xmlDoc.Load("$PSScriptRoot\web.config.model")

    # Select the appSettings node
    $appSettingsNode = $xmlDoc.SelectSingleNode("//appSettings")
    # Remove all child nodes of appSettings
    $appSettingsNode.RemoveAll()

    foreach ($line in Get-Content ($execution_env + "/key.private")) {
        if ($line -notmatch "^#") { # Exclude comments
            if ($line -match "^FASTCGI_PATH") {
                $scriptProcessor = $line.split('=', 2).trim()[1] # Store scriptProcessor for later use
            } else {
                $splitted = $line.split('=', 2).trim()
                if ($splitted.Count -gt 1 ) {
                    $newElement1 = $xmlDoc.CreateElement("add")
                    $newElement1.SetAttribute("key", $splitted[0])
                    $newElement1.SetAttribute("value", $splitted[1])
                    $appSettingsNode.AppendChild($newElement1)
                }
            }
        }
    }

    # Select the add node
    $nodes = $xmlDoc.SelectNodes("/configuration/system.webServer/handlers/add");
    foreach($node in $nodes) {
        $node.SetAttribute("scriptProcessor", $scriptProcessor);
    }

    # Save the modified XML document
    $xmlDoc.Save("$PSScriptRoot\web.config")

    Write-Host "Successfully updated web.config"

    # Ensure wfastcgi is installed on server
    Invoke-Expression 'pip install wfastcgi'
}

Function fillAppVersion() {
    $filePath = "config.ini"

    try {

        $regex_pattern_rev = "(version_gitrev)\s*=\s*(.*)"
        $regex_pattern_tag = "(version_gittag)\s*=\s*(.*)"

        $version_gitrev = git rev-parse --short=6 HEAD
        $version_gittag = git tag --points-at HEAD

        $content = Get-Content -Path $filePath

        if ($version_gitrev) {
            $content = $content -replace $regex_pattern_rev, "`$1 = $version_gitrev"
        }
        if ($version_gittag) {
            $content = $content -replace $regex_pattern_tag, "`$1 = $version_gittag"
        }

        Set-Content -Path $filePath -Value $content | Out-Null

        Write-Output "Version updated in the config file"
    } catch {
        Write-Output "Unable to update version tag (git is missing?) $($_.Exception.Message)"
    }
}


Write-Output "The script file path is: $PSCommandPath"

if (-not $skip) {

    # check if virtual environment already exists
    $venv_exists = $false
    if (Test-Path -Path 'venv') {
        Write-Output "Using existing virtual environment..."
        $venv_exists = $true
    } else {
        Write-Output "Setup new virtual environment..."
        Invoke-Expression 'py -3.12 -m venv venv'
    }
}

# Activate virtual environment
Write-Output "Activate virtual environment..."
Invoke-Expression '.\venv\Scripts\Activate.ps1'

if (-not $skip) {

    # install requirements if needed
    if (!$venv_exists) {
        Write-Output "Installing requirements..."
        Invoke-Expression 'pip install -r requirements.txt'
    } else {
        Write-Output "Updating existing virtual environment..."
        Invoke-Expression 'pip install -r requirements.txt'
    }
    Invoke-Expression 'pip-sync'
}

$execution_env = $env:FLASK_ENV
switch ($execution_env) {
    "PRD" { fillWebConfig 'PRD' }
    "STG" { fillWebConfig 'STG' }
    "DEV"  { fillWebConfig 'DEV' }
    default { # local development server$
        $Env = @{}
        foreach ($line in Get-Content 'key.private') {
            if ($line -notmatch "^#" -and $line.Contains('=')) { # Exclude comments and lines without equals
                $splitted = $line.split('=', 2).trim()
                if ($splitted.Count -gt 1) {
                    $Env[$splitted[0]] = $splitted[1]
                }
            }
        }

        $Env.GetEnumerator() | Foreach-Object {
            $Name, $Value = $_.Name, $_.Value
            Set-Content -Path "env:\$Name" -Value $Value
        }

        Invoke-Expression 'pip install python-certifi-win32 --use-feature=truststore'
    }
}
