# CHANGELOG
{%- for version, release in (context.history.released.items() | list)[:5] %}
{# RELEASED #}
## {{ version.as_semver_tag() }} ({{ release.tagged_date.strftime("%Y-%m-%d") }})
    {%- for type_, commits in release["elements"] | dictsort -%}
        {%- for commit in commits -%}
            {%- if type_ != "unknown" %}
### {{ type_ | capitalize }}
* {{ commit.message.rstrip() }}
            {%- endif -%}
        {%- endfor -%}
    {%- endfor %}
{%- endfor -%}