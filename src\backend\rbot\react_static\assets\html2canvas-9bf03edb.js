import{g as c,r as f}from"./index-9996bb07.js";function l(r,n){for(var o=0;o<n.length;o++){const e=n[o];if(typeof e!="string"&&!Array.isArray(e)){for(const t in e)if(t!=="default"&&!(t in r)){const a=Object.getOwnPropertyDescriptor(e,t);a&&Object.defineProperty(r,t,a.get?a:{enumerable:!0,get:()=>e[t]})}}}return Object.freeze(Object.defineProperty(r,Symbol.toStringTag,{value:"Module"}))}var s=f();const i=c(s),u=l({__proto__:null,default:i},[s]);export{u as h};
//# sourceMappingURL=html2canvas-9bf03edb.js.map
