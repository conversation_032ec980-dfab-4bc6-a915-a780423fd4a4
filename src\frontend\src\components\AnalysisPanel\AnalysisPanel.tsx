import { Label, Pivot, PivotItem } from "@fluentui/react";
import { format } from 'sql-formatter';
import { useState } from 'react';

import styles from "./AnalysisPanel.module.css";
import classNames from 'classnames';


import { SupportingContent } from "../SupportingContent";
import { Agents, ChatResponse, File } from "../../api";
import { AnalysisPanelTabs } from "./AnalysisPanelTabs";

interface Props {
    className: string;
    activeTab: AnalysisPanelTabs;
    onActiveTabChanged: (tab: AnalysisPanelTabs) => void;
    activeCitation: string | undefined;
    citationHeight: string;
    chatResponse: ChatResponse;
}

const pivotItemDisabledStyle = { disabled: true, style: { color: "grey" } };

export const AnalysisPanel = ({ chatResponse: answer, activeTab, activeCitation, citationHeight, className, onActiveTabChanged }: Props) => {
    const isDisabledThoughtProcessTab: boolean =
        !answer?.classification && !answer.answer?.query && !answer.answer?.query_generation_prompt && !answer.answer?.query_result;
    const isDisabledSupportingContentTab: boolean = !answer.data_points?.length;
    const isDisabledCitationTab: boolean = !activeCitation;

    const [clickedItems, setClickedItems] = useState({});
    return (
        <Pivot
            className={className}
            selectedKey={activeTab}
            onLinkClick={pivotItem => pivotItem && onActiveTabChanged(pivotItem.props.itemKey! as AnalysisPanelTabs)}
        >
            <PivotItem
                itemKey={AnalysisPanelTabs.ThoughtProcessTab}
                headerText="Thought process"
                headerButtonProps={isDisabledThoughtProcessTab ? pivotItemDisabledStyle : undefined}
            >
                <div className={styles.thoughtProcess}>
                    <Label className={styles.thoughtProcessHeader}>Confidence:</Label>
                    <Label className={styles.thoughtProcessParagraph}>{answer.classification}</Label>
                    {answer.answer?.explanation && <Label className={styles.thoughtProcessHeader}>Answer's explanation:</Label>}
                    {answer.answer?.explanation && <Label className={classNames(styles.thoughtProcessParagraph, styles.thoughtProcessAllowNewLine)}>{answer.answer?.explanation}</Label>}
                    {answer.answer?.query_generation_prompt && <Label className={styles.thoughtProcessHeader}>Query Generation Prompt:</Label>}
                    {answer.answer?.query_generation_prompt && <Label className={styles.thoughtProcessParagraph}>{answer.answer?.query_generation_prompt}</Label>}
                    {answer.answer?.query && <Label className={styles.thoughtProcessHeader}>Generated Query:</Label>}
                    {answer.answer?.query && <Label className={classNames(styles.thoughtProcessParagraph, styles.thoughtProcessAllowNewLine)}>{format(answer.answer?.query, { language: 'plsql', keywordCase: "upper" })}</Label>}
                    {answer.answer?.query_result && <Label className={styles.thoughtProcessHeader}>Query Result:</Label>}
                    {answer.answer?.query_result && <Label className={styles.thoughtProcessParagraph}>{answer.answer?.query_result}</Label>}
                    {answer.files?.length > 0 && answer.answer?.agent == Agents.rag && <Label className={styles.thoughtProcessHeader}>Download Files:</Label>}
                    {answer.files?.length > 0 && answer.answer?.agent == Agents.rag && <Label className={classNames(styles.thoughtProcessParagraph, styles.thoughtProcessAllowNewLine)}> <ul style={{ listStyleType: 'disc', paddingLeft: '20px' }}>
                                                                                                                                                                          {answer.files.map((item: File, index: number) => (
                                                                                                                                                                            <li key={index}>
                                                                                                                                                                                <a href={"/download-file?path=" + encodeURIComponent(item.path.split("Attach")[1])}>
                                                                                                                                                                                    <Label>{item.name}</Label>
                                                                                                                                                                                </a>
                                                                                                                                                                            </li>
                                                                                                                                                                          ))}
                                                                                                                                                                        </ul></Label>}
                </div>
            </PivotItem>
            <PivotItem
                itemKey={AnalysisPanelTabs.SupportingContentTab}
                headerText="Supporting content"
                headerButtonProps={isDisabledSupportingContentTab ? pivotItemDisabledStyle : undefined}
            >
                <SupportingContent supportingContent={answer?.data_points} />
            </PivotItem>
            <PivotItem
                itemKey={AnalysisPanelTabs.CitationTab}
                headerText="Citation"
                headerButtonProps={isDisabledCitationTab ? pivotItemDisabledStyle : undefined}
            >
                <div className={styles.Citation}>
                    {answer.answer?.source_file && <Label className={styles.CitationHeader}>Source files:</Label>}
                    {answer.answer?.source_file && <Label className={classNames(styles.CitationParagraph, styles.CitationAllowNewLine)}>{answer.answer?.source_file}</Label>}
                </div>                
                {/* <iframe title="Citation" src={activeCitation} width="100%" height={citationHeight} />  da modificare quando verrà introdotto il link del documento*/}
            </PivotItem>
        </Pivot>
    );
};
