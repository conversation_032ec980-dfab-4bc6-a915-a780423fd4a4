from typing import Any, Collection, Dict, List

from src.common_tools.history.compilers import HistoryCompiler


class ConversationHistory:
    """Contains the conversation history of a specific user."""

    def __init__(
        self, user_id: Any, messages: Collection[Any], compiler: HistoryCompiler
    ) -> None:
        """Creates a new conversation history for `user` based on the provided `messages`, translated by a specific `compiler`.

        Args:
            user_id (Any): Data that uniquely identify the user.
            messages (Collection[Any]): A collection of messages in a certain representation.
            compiler (HistoryCompiler): A compiler capable to translate the `messages` provided into a representation that can be understood by the agents.
        """
        self.__user = user_id
        self.__messages = compiler.compile(messages)

    def is_empty(self) -> bool:
        return len(self.messages) == 0

    @property
    def messages(self) -> List[Dict[str, str]]:
        """The messages exchanged by the user and the bot, in a agent-understandable representation.

        Returns:
            List[Dict[str, str]]: The representation of the messages that can be used by the agents.
        """
        return self.__messages
    
    @messages.setter
    def messages(self, list: List[Dict[str,str]]):
        self.__messages = list

    def history_messages(self, agent: str) -> List[Dict[str, str]]:
        history = []

        for message in self.__messages:
            if message["agent"] == agent and message["role"] != 'system':
                history.append({"role": message["role"], "content": message["content"]})

        return history

    @property
    def user(self) -> Any:
        """The user id.

        Returns:
            Any: The user id.
        """
        return self.__user
