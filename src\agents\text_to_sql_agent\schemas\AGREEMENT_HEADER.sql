CREATE TABLE DWH_PUBLIC.AGREEMENT_HEADER (
	COMPANY_CODE VARCHAR2(3 BYTE),
	COMPANY_DESCRIPTION VARCHAR2(360 BYTE),
	PLANT_CODE VARCHAR2(8 BYTE),
	PLANT_DESCRIPTION VARCHAR2(240 BYTE),
	AGREEMENT_CODE VARCHAR2(8 BYTE),
	BUYER_CODE VARCHAR2(2 BYTE),
	BUYER_DESCRIPTION VARCHAR2(240 BYTE),
	SUPPLIER VARCHAR2(10 BYTE),
	SUPPLIER_DESCRIPTION VARCHAR2(240 BYTE),
	START_VALIDITY_DATE DATE,
	STOP_VALIDITY_DATE DATE,
	LOCKED_CODE VARCHAR2(1 BYTE),
	LOCKED VARCHAR2(25 BYTE),
	ROW_NUMBER NUMBER(5, 0),
	AMOUNT NUMBER(13, 2),
	CURRENCY VARCHAR2(4 BYTE),
	CURRENCY_DESCRIPTION VARCHAR2(240 BYTE),
	TRANSPORT_CODE VARCHAR2(4 BYTE),
	TRANSPORT_DESCRIPTION VARCHAR2(254 BYTE),
	DELIVERY_TERMS_CODE VARCHAR2(4 BYTE),
	DELIVERY_TERMS_DESCRIPTION VARCHAR2(254 BYTE),
	PACKAGE_CODE VARCHAR2(10 BYTE),
	PACKAGE_DESCRIPTION VARCHAR2(254 BYTE),
	PAYMENT_CODE VARCHAR2(10 BYTE),
	PAYMENT_DESCRIPTION VARCHAR2(360 BYTE),
	REJECT_RETURN_CODE VARCHAR2(4 BYTE),
	REJECT_RETURN_DESCRIPTION VARCHAR2(254 BYTE),
	DESTINATION_ADDRESS_CODE VARCHAR2(4 BYTE),
	DESTINATION_ADDRESS_DESCRIPTION VARCHAR2(254 BYTE),
	PRIMARY KEY (COMPANY_CODE, PLANT_CODE, AGREEMENT_CODE)
);