import React from "react";
import { Stack, IconButton } from "@fluentui/react";
import { HtmlParsedAnswer } from "../AnswerParser";
import styles from "../Answer.module.css";

interface Props {
    parsedAnswer: HtmlParsedAnswer;
    onFollowupQuestionClicked?: (question: string) => void;
    showFollowupQuestions?: boolean;
}

export const FollowupQuestion = ({
    parsedAnswer,  
    onFollowupQuestionClicked, 
    showFollowupQuestions, 
}: Props) => {
    return(
        <>
        {!!parsedAnswer.followupQuestions.length && showFollowupQuestions && onFollowupQuestionClicked && (
                <Stack.Item>
                    <Stack
                        horizontal
                        wrap
                        className={`${!!parsedAnswer.citations.length ? styles.followupQuestionsList : ""}`}
                        tokens={{ childrenGap: 6 }}
                    >
                        <span className={styles.followupQuestionLearnMore}>Follow-up questions:</span>
                        {parsedAnswer.followupQuestions.map((question, i) => (
                            <a
                                key={i}
                                className={styles.followupQuestion}
                                title={question}
                                onClick={() => onFollowupQuestionClicked(question)}
                            >
                                {question}
                            </a>
                        ))}
                    </Stack>
                </Stack.Item>
            )}
        </>
    )
}