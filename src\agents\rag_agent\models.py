from enum import Enum

class QuestionStatus(str, Enum):
    QUERY = "query"
    FOLLOWUP = "followup"
    FOLLOWUP_COMPLETE = "followup_complete"


class QuestionFoodLaundryCategory(str, Enum):
    FOOD = "FOOD"
    LANUDRY = "LAUNDRY"
    NONE = "missing"


class DocumentStatusMapping(Enum):
    IN_PRODUCTION_IN_DISTRIBUTION = (1, "In production, in distribution", '10')
    OUT_OF_PRODUCTION_OUT_OF_DISTRIBUTION = (2, "Out of production, out of distribution", '21')
    IN_PRODUCTION_OUT_OF_DISTRIBUTION = (3, "In production, out of distribution", '11')
    OUT_OF_PRODUCTION_IN_DISTRIBUTION = (4, "Out of production, in distribution", '20')

    def __init__(self, code, description, status_map):
        self.code = code
        self.description = description
        self.status_map = status_map

    @classmethod
    def from_code(cls, code):
        for status in cls:
            if status.code == code:
                return status
        raise ValueError(f"No status found for code: {code}")
    
    @classmethod
    def from_map(cls, map):
        for status in cls:
            if status.status_map == map:
                return status
        raise ValueError(f"No status found for map: {map}")        
    

class DistributionStatus(Enum):
    IN_DISTRIBUTION = (0, "IN_DISTRIBUTION")
    OUT_OF_DISTRIBUTION = (1, "OUT_OF_DISTRIBUTION")


    def __init__(self, code, description):
        self.code = code
        self.description = description

    @classmethod
    def from_code(cls, code):
        for status in cls:
            if status.code == code:
                return status
        raise ValueError(f"No status found for code: {code}")
    


class ProductionStatus(Enum):
    DRAFT = (0, "DRAFT")
    IN_PRODUCTION = (1, "IN_PRODUCTION")
    OUT_OF_PRODUCTION = (2, "OUT_OF_PRODUCTION")


    def __init__(self, code, description):
        self.code = code
        self.description = description

    @classmethod
    def from_code(cls, code):
        for status in cls:
            if status.code == code:
                return status
        raise ValueError(f"No status found for code: {code}")