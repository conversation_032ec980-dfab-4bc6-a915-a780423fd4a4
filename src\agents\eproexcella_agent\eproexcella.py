from argparse import Argument<PERSON><PERSON><PERSON>, Namespace
import json
import os
from typing import List, <PERSON><PERSON>
from pydantic import BaseModel, Field
import pandas as pd
from tabulate import tabulate

from config.config import CommonConfig
from src.agents.agent import Agent
from src.agents.answer import Answer
from src.agents.eproexcella_agent.models.interpreter import Interpreter
from src.backend.contracts.chat_data import Agent<PERSON>ame
from utils.core import get_logger
from src.agents.eproexcella_agent.models.testual import TextTranslator
from src.agents.eproexcella_agent.models.custom_class import CustomClass


FILE_DIR = os.path.dirname(__file__)
logger = get_logger(__file__)

class EPROExcelLaAnswer(Answer):
    data: List[Tuple[str]] = None
    info: str = None
    request: str = None
    agent_name: str = None

    def __init__(self, data: List[Tuple[str]]) -> None:
        self.data = data

    def add_info_translation(self, info_translation: str) -> None:
        self.info = info_translation or ""

    def add_request(self, request: str) -> None:
        self.request = request

    def to_json(self):
        return {
            "data": self.data,
            "info_translation": self.info,
            "agent": self.agent_name
    }

class eproexcella(BaseModel):
    """
        This function should be called when the user asks questions that require a translation of a excel file.
        Typical queries might include:

        Requests for translation in the same column
        ("Translate column A of this excel and replace the old column with the new one",
        "Translate column A of this excel",
        "Translate column A of this excel and add a new column in the excel containing the translation").
    """

    query: str =  Field(description=" The question that will be used as search query")

class EPROExcelLa(Agent):
    def __init__(self, bot_name: str, name= AgentName.EPROEXCELLA.value, user_id = "") -> None:
        """Initialise the FlExcel Agent, setting up the preliminary model, and the LLM used."""
        super().__init__(name, bot_name)

        logger.debug("Init EPROExcelLa clients")

        self.interpreter = Interpreter()
        self.common_config = CommonConfig()

        logger.info("FlExcel agent init completed")
        logger.info(user_id)
        self.excel_path = os.path.join(FILE_DIR, f"upload\\data_{user_id}.xlsx")
        self.file = pd.DataFrame()


    def __read_file(self, preview):
        if preview:
            # Read the first 20 file
            file = pd.read_excel(self.excel_path, nrows=40)
        else:
            # Read all the file
            file = pd.read_excel(self.excel_path)
        self.file = file

    def __get_excel_info(self):
        """
            Returns the main information of the excel file
            such as the list of columns and a subset of the data.
        """
        # Get the list of columns name
        columns = self.file.columns.ravel()
        # Get a subset of the excel data
        samples = ""
        n_rows = self.file.shape[0]
        if n_rows < 50:
            samples = self.file.sample(n_rows).reset_index(drop=True)
        else:
            samples = self.file.sample(int(n_rows*0.1)).reset_index(drop=True)

        # Change format to the subset to a json format
        for col in samples.columns:
            samples[col] = ' '.join(samples[col].astype(str))
        samples = samples.iloc[0,:]
        return columns, samples

    def __get_data_from_file(self, column):
        """
            Returns the data of the column to be translated formatted in the correct way,
            that is, an array containing dictionaries
            [{"text":"<description 1>"}, {"text":"<description 1>"}, ..., {"text":"<description 1>"}]
        """
        data = []
        for row in self.file[str(column)]:
            data.append({"Text":str(row)})
        return data

    def __add_translation(self, translation, op, col_name, to_lang):
        """
            Add the traslation to the excel
        """
        for i in range(len(to_lang)):
            new_col_name = col_name + "_" + to_lang[i].upper()
            if op == 'new':
                try:
                    self.file[new_col_name] = translation[i]
                except Exception as e:
                    print(e)
            else:
                self.file[col_name] = translation[i]
                self.file.rename(columns={col_name:new_col_name})

    def __prepare_excel(self, op, infos):
        translator = TextTranslator()

        for item in infos:
            # column name
            c_name = item[0]
            # language of the description
            from_lang = item[1]
            # languages for the translation
            to_lang = item[2]

            data = self.__get_data_from_file(c_name)

            # Call the API for the translation
            translations = translator.translate(from_lang, to_lang, data)
            logger.info("Dimnesioni delle traduzioni: " + str(len(translations[0])))
            self.__add_translation(translation=translations, op=op, col_name=c_name, to_lang=to_lang)

    def __get_interpretation(self, request, preview):
        # Interrogate the LLM model
        # Get columns from excel
        self.__read_file(preview)
        columns, samples = self.__get_excel_info()
        interpretation = self.interpreter.interprets(request, columns, samples)
        print(interpretation)
        try:
            inter = json.loads(interpretation)
            print(inter["op"])
        except json.JSONDecodeError as e:
            print(f"Errore nel decodificare il JSON: {e}")
        # #info per column
        op = inter["op"]
        ipc = inter['info']

        return op, ipc

    def __get_intent(self, request):
        intent = self.interpreter.intent(request)
        intent = json.loads(intent)
        print(intent)
        return intent["request"], intent["type"]

    def __generate_custom_class(self, preview):
        self.__read_file(preview)
        cc = CustomClass()
        cols, _ = self.__get_excel_info()
        col = cc.get_column(cols)
        codes = cc.custom_class(self.file[col])
        self.file["Custom_Class"] = codes

    def ask(self, request:str, history = None, preview = True) -> EPROExcelLaAnswer:
        """
            Interrogates the bot with a specific request and returns the interpretation of the request.
            Args:
                request (str): The request in natural language posed by the user.

            Returns:
                Answer: An object containing all relevant values for the answer.
        """
        prompt, intent = self.__get_intent(request)
        logger.info("Intent of the bot is: " + intent)

        if intent == '':
            print("BOH")
        elif intent == 'cc':
            self.__generate_custom_class(preview)
        else:
            # Interrogate the LLM model
            op, info = self.__get_interpretation(prompt, preview)
            logger.info("Dimensioni del file: " + str(self.file.shape))
            self.__prepare_excel(op, info)

        # Build answer
        answer = EPROExcelLaAnswer(self.file.to_json())
        answer.add_info_translation("interpretation")
        answer.add_request(request)
        #answer.add_agent_name(self.agent_name)
        logger.info(preview)
        return answer


# def get_args() -> Namespace:
#     parser = ArgumentParser(
#         description="Translator that return the translation of a file excel"
#     )
#     parser.add_argument(
#         "request",
#         type=str,
#         help="The natural language request about the translation to perform on the excel.",
#     )
#     return parser.parse_args()

# if __name__ == "__main__":
#     args = get_args()

#     trans = FlExcle("TRANSLATOR")
#     answer, tab = trans.ask(args.request)

#     print("\n\n\nPRINTING ANSWER-----------------")
#     print(tab)
#     print(tabulate(trans.file, headers=trans.file.columns, tablefmt='grid'))
#     #print(answer.to_json())
#     print("\n\n\n")