CREATE TABLE DWH_PUBLIC.ITEM_COMPLIANCES ( -- Use only if asked for details on a certification
	COMPANY_CODE VARCHAR2(3 BYTE),
	COMPANY_DESCRIPTION VARCHAR2(100 BYTE),
	PLANT_CODE VARCHAR2(20 BYTE),
	PLANT_DESCRIPTION VARCHAR2(100 BYTE),
	SUPPLIER VARCHAR2(10 BYTE),
	SUPPLIER_DESCRIPTION VARCHAR2(240 BYTE),
	ITEM_CODE VARCHAR2(20 BYTE),
	CERTIFICATION_TYPE VARCHAR2(3 BYTE),
	CERTIFICATION_DESCRIPTION VARCHAR2(100 BYTE),
	CERTIFICATION_LINE NUMBER,
	CERTIFICATION_STATUS VARCHAR2(2 BYTE),
	CERTIFICATION_STATUS_DESCRIPTION VARCHAR2(100 BYTE),
	CERTIFICATION_START_VALIDITY_DATE DATE,
	CERTIFICATION_END_VALIDITY_DATE DATE,
	CERTIFICATION_COUNTRY VARCHAR2(3 BYTE),
	CERTIFICATION_COUNTRY_DESCRIPTION VARCHAR2(100 BYTE),
	PREFERENTIAL CHAR(1 BYTE),
	PREFERENTIAL_DESCRIPTION VARCHAR2(100 BYTE),
	HS_CODE VARCHAR2(10 BYTE),
	DUAL_USE CHAR(1 BYTE),
	CERTIFICATION_CATEGORY VARCHAR2(3 BYTE),
	CERTIFICATION_CATEGORY_DESCRIPTION VARCHAR2(100 BYTE),
	LINE_CATEGORY CHAR(1 BYTE),
	PERCENTAGE_SUBSTANCE VARCHAR2(10 BYTE),
	SUBSTANCE_CAS_NUMBER VARCHAR2(20 BYTE),
	SUBSTANCE_EC_NUMBER VARCHAR2(20 BYTE),
	SUBSTANCE_OTHER_NUMBER VARCHAR2(20 BYTE),
	TOTAL_VOLUME VARCHAR2(20 BYTE),
	EXEMPTED VARCHAR2(20 BYTE),
	PRE_REGISTER VARCHAR2(20 BYTE),
	PRE_REGISTER_DATE DATE,
	REGISTER_DEADLINE_DATE DATE,
	REGISTER_NUMBER VARCHAR2(20 BYTE),
	WEIGHT VARCHAR2(20 BYTE),
	RML_SUBST_OVER_PERC VARCHAR2(20 BYTE),
	UPD_DATE DATE,
	TABLE_SOURCE VARCHAR2(25 BYTE),
	PRIMARY KEY (COMPANY_CODE, PLANT_CODE, SUPPLIER, ITEM_CODE, CERTIFICATION_TYPE, CERTIFICATION_LINE)
);