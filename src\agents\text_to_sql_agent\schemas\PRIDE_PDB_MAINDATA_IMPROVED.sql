CREATE TABLE DWH_PUBLIC.PRIDE_PDB_MAINDATA_IMPROVED (
  "SINTCODE" NVARCHAR2 (25) NOT NULL ENABLE,
  "CCOMSTATUS" NVARCHAR2 (1),
  "CCOMSTATUS_DESCRIPTION" NVARCHAR2 (400),
  "ISTATUS" NUMBER,
  "ISTATUS_DESCRIPTION" NVARCHAR2 (400),
  "IBRAND" NUMBER,
  "IBRAND_DESCRIPTION" NVARCHAR2 (400),
  "SPNC" NVARCHAR2 (30),
  "SFACTMODEL" NVARCHAR2 (30),
  "SFACTCODE" NVARCHAR2 (30),
  "IPLATFORM" NUMBER,
  "IPLATFORM_DESCRIPTION" NVARCHAR2 (400),
  "SFACTORY" NVARCHAR2 (5),
  "SFACTORY_DESCRIPTION" NVARCHAR2 (400),
  "BSPECLOCKED" NUMBER (1, 0),
  "SBENEFITLEVEL" NVARCHAR2 (50),
  "SAGGCODE" NVARCHAR2 (10),
  "CCLASS" NVARCHAR2 (1),
  "CCLASS_DESCRIPTION" NVARCHAR2 (400),
  "SFAMILY" NVARCHAR2 (2),
  "SFAMILY_DESCRIPTION" NVARCHAR2 (400),
  "CTYPE" NVARCHAR2 (1),
  "CTYPE_DESCRIPTION" NVARCHAR2 (400),
  "CCONDITION" NVARCHAR2 (1),
  "SSUPPLIER" NVARCHAR2 (20),
  "SSUPPLIER_DESCRIPTION" NVARCHAR2 (400),
  "SINTCODESUPPLIER" NVARCHAR2 (25),
  "SDESIGNFAM" NVARCHAR2 (100),
  "SAPPROVALS" NVARCHAR2 (1000),
  "SKEYACCOUNT" NVARCHAR2 (100),
  "SENERGY" NVARCHAR2 (100),
  "SDESCRIPTION" NVARCHAR2 (120),
  "SDESCRIPTION_ELS" NVARCHAR2 (4000),
  "SSUPDESCRIPTION" NVARCHAR2 (100),
  "SCLASSIFICATION" NVARCHAR2 (4),
  "SCOMMENT" NVARCHAR2 (2000),
  "SCOMMENT1" NVARCHAR2 (30),
  "SOBJTOUSETECH" NVARCHAR2 (100),
  "SOBJTOUSEDSGN" NVARCHAR2 (100),
  "SINSBY" NVARCHAR2 (10),
  "DINSDATE" DATE,
  "SUPDBY" NVARCHAR2 (10),
  "DUPDDATE" DATE,
  "SDQUALITY" NVARCHAR2 (2000),
  "SITSCOMMENT" NVARCHAR2 (1000),
  "IPREVSTATUS" NUMBER,
  "BISNEWDOC" NUMBER (1, 0),
  "BGRANTTOCOM" NUMBER (1, 0),
  "BINCOM" NUMBER (1, 0),
  "STOCOMBY" NVARCHAR2 (10),
  "STOCOMDATE" DATE,
  "IWEBSTATUS" NUMBER,
  "DUPDFROMCOM" DATE,
  "BUPDFROMNOTES" NUMBER (1, 0),
  "DUPDFROMNOTES" DATE,
  "DUPDFROMGULP" DATE,
  "SCEAPPROVALNUMBER" NVARCHAR2 (50),
  "BMACHINEDIRECTIVE" NUMBER (1, 0),
  "SAPPLIANCEINFO" NVARCHAR2 (60),
  "SORDINARYMAINTENANCE" NVARCHAR2 (100),
  "SPREVENTIVEMAINTENANCE" NVARCHAR2 (100),
  "SORDINARYMAINTENANCEDESC" NVARCHAR2 (100),
  "SPREVENTIVEMAINTENANCEDESC" NVARCHAR2 (100),
  "SCONFORMTO" NVARCHAR2 (1000),
  "SCERTIFIEDTO" NVARCHAR2 (1000),
  "BELSCODE" NUMBER (1, 0),
  "SCONFORMTOSANITATION" NVARCHAR2 (1000),
  "SMANUFACTURINGCOUNTRY" NVARCHAR2 (20),
  "BCOMPETITORCODE" NUMBER (1, 0),
  "SCOUNTRY" NVARCHAR2 (50),
  "SOLDFACTCODE" NVARCHAR2 (30),
  "BFUMIGATION" NUMBER (1, 0),
  "SSTOPFUMIGATIONDATE" NVARCHAR2 (6),
  "BCEDECLARATION" NUMBER (1, 0),
  "BSCODE" NUMBER (1, 0),
  "BCOMPOSITIONCODE" NUMBER (1, 0),
  "SEXTERNALMODEL" NVARCHAR2 (30),
  "SPRODCLASS" NVARCHAR2 (10),
  "SLANGUAGECODE" NVARCHAR2 (10),
  "SAPPROVALTYPE" NVARCHAR2 (32),
  "STYPEOFHEATING" NVARCHAR2 (32),
  "BINSAP" NUMBER (1, 0),
  "GUIDPPD2" NVARCHAR2 (255),
  "BSERIALNUMBERMANDATORY" NUMBER (1, 0),
  "SWMAPPROVALNUMBER" NVARCHAR2 (50),
  "SPMMODELDESCRIPTION" NVARCHAR2 (1000),
  "SSTATISTICALMODEL" NVARCHAR2 (30),
  "BUPDPACKAGE" NUMBER (1, 0),
  "SKINDOFPRODUCT" NVARCHAR2 (1),
  "SEANCODE" NVARCHAR2 (25),
  "SSERVMODEL" NVARCHAR2 (30),
  "SMODEL" NVARCHAR2 (30),
  "IREQUESTCODE" NUMBER,
  "BMARINE" NUMBER (1, 0),
  "BLOCAL" NUMBER (1, 0),
  "SSPECIALFLOW" NVARCHAR2 (1),
  "SEUPCLASS" NVARCHAR2 (20),
  "FEUPENERGYCONSUMPTION" BINARY_DOUBLE,
  "SEUPENERGYCLASS" NVARCHAR2 (5),
  "SEUPEEI" NVARCHAR2 (10),
  "BFROMCONFIGURATOR" NUMBER (1, 0),
  "BISKIT" NUMBER (1, 0),
  "BNOMAXLINE" NUMBER (1, 0),
  "BEXCLUDEFROMQESEXPORT" NUMBER (1, 0),
  "FEPRELEQUIVALENTMODEL" NUMBER (20, 0),
  "FEPRELREGISTRATIONCODE" NUMBER (20, 0),
  "DEPRELREGISTRATIONDATE" DATE,
  "SEPRELPRODUCTGROUP" NVARCHAR2 (64),
  "SEPRELIDENTIFIER" NVARCHAR2 (255),
  "BCREATEDBYCONFIG" NUMBER (1, 0),
  "SENERGYGROUP" NVARCHAR2 (15),
  "BKMODEL" NUMBER (1, 0),
  "BENERGYLABEL" NUMBER (1, 0),
  "SPNCMODEL" NVARCHAR2 (20),
  "IROWORDER" NUMBER,
  "SEPRELSTATUS" NVARCHAR2 (30),
  "SKW" NVARCHAR2 (15),
  "SCEIDNUMBERPRESSURE" NVARCHAR2 (15),
  "SCEIDNUMBERGAS" NVARCHAR2 (15),
  "SUKCAIDNUMBERPRESSURE" NVARCHAR2 (15),
  "SUKCAIDNUMBERGAS" NVARCHAR2 (15),
  "SCHANNEL" NVARCHAR2 (2),
  "STYPEFAMILY" NVARCHAR2 (10),
  "SAPPROVALAGGREGATIONSYMBOLS" NVARCHAR2 (50),
  "SETLCONTROLNUMBER" NVARCHAR2 (50),
  CONSTRAINT "PK_TB_PDB_MAINDATA" PRIMARY KEY ("SINTCODE") USING INDEX ENABLE
);