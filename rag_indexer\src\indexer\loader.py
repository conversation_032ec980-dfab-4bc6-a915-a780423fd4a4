from concurrent.futures import Thr<PERSON><PERSON><PERSON><PERSON>xecutor
from math import ceil
from time import sleep
from typing import Callable, Iterable, Iterator, List, NamedTuple, Self

from datetime import datetime
import pandas as pd
from langchain_community.document_loaders.base import BaseLoader
from rag_indexer.src.indexer.azure_loader.azure_document_intelligence_loader import (
    AzureAIDocumentIntelligenceLoader,
)
from langchain_core.documents.base import Document
from tqdm.auto import tqdm

from config.config import RAGConfig
from utils.core import SourceType, get_logger
from utils.clients import DatabaseClient

logger = get_logger(__file__)


class File(NamedTuple):
    """Represents a file with its URI, associating a loader to retrieve it."""

    loader: BaseLoader
    uri: str
    source: SourceType
    document_update_date: str


class DocumentLoader:
    """Provides methods to load documents."""

    class LoaderIterator:
        """Provides a way to lazily load batches of documents."""

        def __init__(self, loaders: List[File], batch_size: int) -> None:
            """Prepares the iterator by defining how to split the loaders for execution and allocating the resources for said execution.

            Args:
                loaders (List[BaseLoader]): A set of loaders. Optimal results are obtained if each loader is mapped to only one document.
                batch_size (int): The number of loaders to execute concurrently.
            """
            assert loaders, "No loaders provided. Cannot perform loading."

            self.to_load = loaders
            self.batch_size = batch_size
            self.total_batches = ceil(len(loaders) / batch_size)

            logger.info(
                f"{len(loaders)} total documents to load. {self.total_batches} batches of max {batch_size} elements created."
            )

        # Change to adpt to the new documents data
        def __run_loader(file: File) -> List[Document]:
            performance_metric_file = open('performance_metrics.log', 'a')
            try:
                print(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - Document Intelligence parsing started", file=performance_metric_file)
                docs = file.loader.load()
                print(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - Document Intelligence parsing finished", file=performance_metric_file)
                performance_metric_file.close()
            except:
                with open('files_not_processed.log', 'a') as file_log:
                    print(f"File {file.uri} not processed", file=file_log)
                    return []
            for doc in docs:
                
                doc.metadata.update({"tables": []})      
                doc.metadata.update({"url": file.uri})
                doc.metadata.update({"document_update": file.document_update_date})
            return docs

        def __iter__(self) -> Self:
            """Starts the iteration.

            Returns:
                Self: The iterator object
            """
            logger.debug("Begin iterative upload.")
            self.current_batch: int = 0

            return self

        def __next__(self) -> Iterator[Document]:
            """Loads the next batch of documents until no more loaders are available.

            Raises:
                StopIteration: When the last batch has been consumed.

            Yields:
                Iterator[Document]: An Iterator of the representations of the loaded documents for the next batch.
            """
            logger.debug(
                f"Loading batch {self.current_batch} of {self.total_batches}..."
            )
            if self.current_batch < self.total_batches:
                # Define batch boundaries and select batch elements.
                batch_start = self.current_batch * self.batch_size
                batch_end = min(
                    self.current_batch * self.batch_size + self.batch_size,
                    len(self.to_load),
                )
                batch = self.to_load[batch_start:batch_end]

                with ThreadPoolExecutor(batch_end - batch_start + 1) as executor:
                    batch_docs = list(
                        executor.map(DocumentLoader.LoaderIterator.__run_loader, batch)
                    )
                # batch_docs = []
                # for file in (pbar := tqdm(batch)):
                #     pbar.set_description(f"Loading file {file.uri}")
                #     batch_docs.append(DocumentLoader.LoaderIterator.__run_loader(file))
                #     # Required by Document Intelligence (https://learn.microsoft.com/en-us/azure/ai-services/document-intelligence/service-limits?view=doc-intel-4.0.0#example-of-a-workload-pattern-best-practice)
                #     sleep(10)

                batch_docs = [
                    document for documents in batch_docs for document in documents
                ]

                # Loading done
                logger.info(f"Batch {self.current_batch} loaded!")
                self.current_batch += 1
                logger.debug(f"Yielding {len(batch_docs)} loaded documents...")
                return list(batch_docs)
            else:
                logger.info("End of iteration")
                raise StopIteration

        def __len__(self) -> int:
            return self.total_batches

    def __init__(self, source: SourceType) -> None:
        """Create a new document loader based on a supported source type.

        Args:
            source (SourceType): A supported source type

        Raises:
            Exception: If `source` is not of a supported type.
        """
        logger.debug(f"Setting document source = {SourceType(source).name}")
        config = RAGConfig()

        if source == SourceType.LOCAL_FILE_SYSTEM:
            self.loader: Callable[[str], AzureAIDocumentIntelligenceLoader] = (
                lambda uri: AzureAIDocumentIntelligenceLoader(
                    api_endpoint=config.document_intelligence_endpoint,
                    api_key=config.document_intelligence_key,
                    file_path=uri,
                    api_model="prebuilt-layout",
                    api_version="2024-07-31-preview"
                )
            )
            logger.info("Local loader initialized!")
        elif source == SourceType.REMOTE_FILE_SYSTEM:
            self.loader: Callable[[str], AzureAIDocumentIntelligenceLoader] = (
                lambda uri: AzureAIDocumentIntelligenceLoader(
                    api_endpoint=config.document_intelligence_endpoint,
                    api_key=config.document_intelligence_key,
                    file_path=uri, #remote file system for PrIDE is considered as a local file system
                    api_model="prebuilt-layout",
                    api_version="2024-07-31-preview"
                )
            )
            logger.info("Online loader initialized!")
        else:
            logger.critical(
                f"{SourceType(source).name} is not recognized as a valid source."
            )
            raise Exception(f'Case "{SourceType(source).name}" is not handled.')

        self.__pool: List[str] = []
        self.source = source

    def add_documents(self, uris: Iterable[tuple]) -> None:
        """Add an iterable set of URIs identifying documents from a source of the type specified during the creation of the document loader.

        Args:
            uris (Iterable[str]): A set of URIs, each identifying a specific document from the specified source type.
        """
        self.__pool.extend(uris)
        logger.info(f"{len(list(uris))} URIs have been added for loading.")

    def load(self, batch_size: int) -> LoaderIterator:
        """Loads the documents provided, sectioning them in batches of the specified size.

        Args:
            batch_size (int): The maximum number of documents that each batch can contain.

        Returns:
            LoaderIterator: An iterator which yields batches, of max size = `batch_size`, of loaded documents.
        """
        # Remove possible URI duplicates
        uris = list(dict.fromkeys(self.__pool)) #cambiato da set() a dict.fromkeys() per mantenere l'ordine per data dei file

        logger.debug(f"The following URIs are being scheduled for loading:\n{uris}")
        loaders = [File(self.loader(uri[0].replace("\t","")), uri[0].replace("\t",""), self.source, uri[1]) for uri in uris]
        logger.info(f"Loaders have been set-up for {len(uris)} URIs")

        return DocumentLoader.LoaderIterator(loaders, batch_size)
