{% extends 'base.html' %}

{% block head_css %}
    <link rel="stylesheet" href="{{ url_for('static', filename='bubble.css') }}">
{% endblock %}

{% block header %}
  <h1>{% block title %}EProBot{% endblock %}</h1>
  {% if g.user %}
    <a class="action" href="{{ url_for('blog.create') }}">New</a>
  {% endif %}
{% endblock %}

{% block content %}
    <div class="container pt-3">
        <div class="row">
            <div class="offset-md-3 col-md-6">
                <div class="row">
                    <h2 class="anim-typewriter">Chat</h2>
                    <div id="chat" class="chat-box overflow-auto"  style="height: 438px;"></div>
                </div>
                <div class="row">
                    <form id="message-form" class="form-inline" autocomplete="off">
                        <div class="input-group mx-sm-3 mb-2">
                          <input id="message" type="text" class="form-control" placeholder="Type your question..." aria-label="Type your question..." aria-describedby="send-test-msg">
                          <button type="submit" class="btn btn-primary">Ask...</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div class="row">
        </div>
    </div>

    <script id='bubble-template-placeholder' type='text/template'>
        <div class='anim-typewriter bubble'>
        </div>
    </script>
{% endblock %}

{% block tail_js %}
    <script src="https://cdn.socket.io/4.4.1/socket.io.min.js"></script>
    <script type="text/javascript" charset="utf-8">
        var $bubble = $($("#bubble-template").html());
        var socket = io();

        $( document ).ready(function() {

            $("#message-form").submit(function(event) {
                event.preventDefault();
                var message = $("#message").val().trim();
                if (message.length > 0) {
                    const bid = getRandomId(); //{# get random id for bubble #}
                    showBubble(message, true); //{# bubble for my message #}
                    setTimeout(function() { showBubble('', false, bid) }, 500); //{# bubble for server rensponse #}
                    socket.emit("message", { bid: bid, message: message });
                }
                $("#message").val("");
            });
            socket.onAny((event, ...args) => {
                console.log(event, args);
            });
            socket.on("message", function(data) {
                console.log(`message <- ${data.message}`);
                queueMicrotask(() => {
                    fillBubble(data.bid, data.message, data.sequence)
                });
            });
            socket.on('connect', function() {
                console.log('socket -> Connected to SocketIO server!')
                socket.emit('my event', {data: 'I\'m connected!'});
            });

        });

        function showBubble(message, isSend, bid) {
            var $template = $($("#bubble-template-placeholder").html());
            $template.addClass(isSend ? 'send' : 'receive');
            if (bid)  { $template.attr('id', bid); }
            if (!isSend) {
                $template.append('<div class="bubble-placeholder"> <span class="jumping-dots"> <span class="dotreceive dot-1"></span> <span class="dotreceive dot-2"></span> <span class="dotreceive dot-3"></span> </span> </div>');
            } else {
                $template.text(message);
            }
            $("#chat").append($template);
            $template.css({
                "animation-name": "expand-bounce",
                "animation-duration": "0.25s"
            });
            scrollDown();
        }
        function fillBubble(id, message, sequence) {
            if (sequence == 0) {
                $('#'+id).empty()
                typeWriter($('#'+id), message);
            } else {
                showBubble(message, false, id + sequence);
                fillBubble(id + sequence, message, 0);
            }
        }

        function typeWriter(el, text, i = 0) {
            if (i < text.length) {
                el.append(text.charAt(i++));
                setTimeout(typeWriter, 30, el, text, i)
            }
        }

        function getRandomId() {
            return (Math.random() + 1).toString(36).substring(2);
        }

        function scrollDown() {
            $('#chat').animate({scrollTop: $('#chat')[0].scrollHeight}, "slow");
        }

    </script>
{% endblock %}
