import os
import json
import logging
from apispec import APISpec
from apispec.ext.marshmallow import MarshmallowPlugin
from apispec_webframeworks.flask import FlaskPlugin
from flask import (
    Blueprint,
    jsonify,
    request,
    current_app,
    send_file,
    Blueprint)
from utils.core import get_logger
from flask.wrappers import Response as FlaskResponse
from functools import wraps
from config.config import CommonConfig
from src.backend.blueprints.rest.document_controller import retrieve_document, get_document_list
from src.backend.blueprints.rest.models.document_models import DocumentRequest, DocumentListRequest, DocumentResponse, DocumentListResponse, ErrorResponse
from marshmallow import ValidationError
from datetime import datetime


logger = get_logger(__file__)

api_v1 = Blueprint("api_v1", __name__, url_prefix="/api/v1")

cfg = CommonConfig()

def handle_validation_errors(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            return f(*args, **kwargs)
        except ValidationError as err:
            error_response = ErrorResponse.create("validation_error", err.messages)
            return jsonify(error_response), 422
        except json.JSONDecodeError:
            error_response = ErrorResponse.create("json_decode_error", {"error": "Invalid JSON format in request body"})
            return jsonify(error_response), 400
    return decorated_function

def handle_server_errors(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            return f(*args, **kwargs)
        except Exception as e:
            logger.error(f'Server error: {str(e)}')
            error_response = ErrorResponse.create("server_error", {"error": "An internal server error occurred"})
            return jsonify(error_response), 500
    return decorated_function

@api_v1.before_request
def log_request_details():
    log_data = {
        'client_ip': request.remote_addr,
        'forwarded_ip': request.headers.get('X-Forwarded-For')
    }
    log_data['method'] = request.method,
    log_data['path'] = request.path
    if request.is_json:
        log_data['payload'] = request.json

    if current_app.logger.isEnabledFor(logging.DEBUG):

        current_app.logger.debug(f"Content-Type: {request.content_type}")
        current_app.logger.debug(f"Content-Length: {request.content_length}")
        current_app.logger.debug(f"Headers: {dict(request.headers)}")

        # Metodo 1: get_data() - legge tutto il body
        try:
            data1 = request.get_data()
            current_app.logger.debug(f"get_data(): {len(data1)} bytes")
            if data1:
                current_app.logger.debug(f"First 100 chars: {data1[:100]}")
        except Exception as e:
            current_app.logger.error(f"get_data() failed: {e}")

        # Metodo 2: Accesso diretto al form data (se form-encoded)
        try:
            if request.form:
                current_app.logger.debug(f"Form data: {dict(request.form)}")
        except Exception as e:
            current_app.logger.error(f"Form access failed: {e}")

        # Metodo 3: JSON data
        try:
            if request.is_json:
                json_data = request.get_json()
                current_app.logger.debug(f"JSON data: {json_data}")
        except Exception as e:
            current_app.logger.error(f"JSON access failed: {e}")

        # Metodo 4: Raw input stream (attenzione: consuma lo stream)
        try:
            # Solo se gli altri metodi falliscono
            if not data1:
                raw_data = request.stream.read(1024)  # Leggi max 1024 bytes
                current_app.logger.debug(f"Raw stream read: {len(raw_data)} bytes")
                if raw_data:
                    current_app.logger.debug(f"Raw data: {raw_data[:100]}")
        except Exception as e:
            current_app.logger.error(f"Raw stream read failed: {e}")

        log_data['user_agent'] = request.headers.get('User-Agent'),
        current_app.logger.debug("BOT request: %s", json.dumps(log_data))
    else:
        current_app.logger.info("BOT request: %s", json.dumps(log_data))


@api_v1.route("/documents", methods=['POST'])
@handle_server_errors
@handle_validation_errors
def documents():
    logger.info("Received request for /documents with payload: %s", request.json)
    """Get specific document
    ---
    post:
      summary: Get specific document
      description: |
        Get a specific document by **Internal Code** (Product Code), **Product Number** (PNC).

        To retrieve the right document fill-out all the parameters

        + `internal_code` or `product_number`

        + `document_type`

        + `document_number`

        + `document_edition`

        + `language_id`

        + `id_file`

      requestBody:
        required: true
        content:
          application/json:
            schema: DocumentRequest
      responses:
        200:
          description: Document file returned
          content:
            application/octet-stream:
              schema:
                type: string
                format: binary
      tags:
        - Documents
    """
    schema = DocumentRequest()
    validated_data = schema.load(request.json)
    file_path = retrieve_document(
        validated_data.get('internal_code'),
        validated_data.get('product_number'),
        validated_data['document_type'],
        validated_data.get('document_number'),
        validated_data.get('document_edition'),
        validated_data.get('language_id'),
        validated_data.get('id_file')
    )
    if not file_path:
        logger.warning("File not found for metadata: %s", validated_data)
        return jsonify(error="File not found, check the metadata"), 404

    full_path = os.path.join(cfg.pride_product_root, file_path)
    if os.path.exists(full_path):
        logger.info("File found at path: %s", full_path)
        return send_file(full_path, as_attachment=True)
    else:
        logger.warning("File not found on disk at path: %s", full_path)
        return jsonify(error="File not found"), 404


@api_v1.route("/documentList", methods=['POST'])
@handle_server_errors
@handle_validation_errors
def documentList():
    logger.info("Received request for /documentList with payload: %s", request.json)
    """Get document list
    ---
    post:
      summary: Get document list
      description: Get list of documents by Internal Code (Product Code) or Product Number (PNC) or Factory Model.
      requestBody:
        required: true
        content:
          application/json:
            schema: DocumentListRequest
      responses:
        200:
          description: Document list returned
          content:
            application/json:
              schema: DocumentListResponse
      tags:
        - Documents
    """
    schema = DocumentListRequest()
    validated_data = schema.load(request.json)
    document_list = get_document_list(
        internal_code=validated_data.get('internal_code'),
        product_number=validated_data.get('product_number'),
        factory_model=validated_data.get('factory_model'),
        document_type_filter=validated_data.get('document_type'),
        language_id_filter=validated_data.get('language_id'),
        document_edition_filter=validated_data.get('document_edition'),
        document_status_filter= validated_data.get('document_status'),
        document_number_filter = validated_data.get('document_number')
    )
    logger.info("Returning document list with %d items", len(document_list))
    return {'document_list': document_list}, 200


spec = APISpec(
    title="Swagger Document API",
    version="0.1",
    openapi_version="3.0.2",  # Usa 3.0.2 per una migliore compatibilità con gli strumenti
    plugins=[FlaskPlugin(), MarshmallowPlugin()],
    info=dict(description="API per il recupero dei documenti"),
    servers=[{"url": "/", "description": "Server locale"}]
)
spec.components.schema("DocumentRequest", schema=DocumentRequest)
spec.components.schema("DocumentListRequest", schema=DocumentListRequest)
spec.components.schema("DocumentResponse", schema=DocumentResponse)
spec.components.schema("DocumentListResponse", schema=DocumentListResponse)
spec.components.schema("ErrorResponse", schema=ErrorResponse)

@api_v1.route("/openapi.yaml", methods=['GET'])
def get_openapi_yaml():
    """
    Registra tutte le route del Blueprint nello spec OpenAPI.
    """
    with current_app.test_request_context():
        # Registra tutte le route nel blueprint api_v1
        for route in current_app.url_map.iter_rules():
            if route.endpoint.startswith('api_v1.'):
                # Estrai il nome della funzione dall'endpoint
                view_func_name = route.endpoint.split('.')[-1]
                view_func = current_app.view_functions.get(f'api_v1.{view_func_name}')
                if view_func:
                    spec.path(view=view_func)

    """Get OpenAPI specification in YAML format
    ---
    get:
      summary: Get OpenAPI specification
      description: Returns the complete OpenAPI specification in YAML format
      responses:
        200:
          description: OpenAPI specification
          content:
            text/yaml:
              schema:
                type: string
      tags:
        - Documentation
    """
    logger.info("Returning OpenAPI specification in YAML format")
    return spec.to_yaml(), 200, {"Content-Type": "text/yaml"}
